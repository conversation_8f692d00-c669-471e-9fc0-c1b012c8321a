const mysql = require('mysql2/promise');

console.log('🔧 FIXING EXISTING BALANCE RECORDS');
console.log('==================================\n');

async function fixBalanceRecords() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jewellery_wholesale'
    });

    console.log('✅ Connected to database');

    // Get all inventory records
    console.log('\n📊 Fetching inventory records...');
    const [records] = await connection.execute(`
      SELECT id, procured_in_24k, balance_weight_22k, sold_value_22k, 
             sold_gold_weight_22k, balance_gold_weight_22k
      FROM inventory 
      ORDER BY id
    `);

    console.log(`Found ${records.length} inventory records`);

    for (const record of records) {
      console.log(`\n🔧 Processing Record ID: ${record.id}`);
      console.log(`   Procured 24K: ${record.procured_in_24k}g`);
      console.log(`   Current 22K Balance: ${record.balance_weight_22k}g`);
      console.log(`   Sold 22K: ${record.sold_value_22k || record.sold_gold_weight_22k || 0}g`);

      // Calculate correct available balance
      const original22kBalance = record.balance_weight_22k || (record.procured_in_24k * 0.916);
      const totalSold22k = record.sold_value_22k || record.sold_gold_weight_22k || 0;
      const correctAvailableBalance = Math.max(0, original22kBalance - totalSold22k);

      console.log(`   Original 22K Balance: ${original22kBalance}g`);
      console.log(`   Correct Available Balance: ${correctAvailableBalance}g`);

      // Update the record with correct balance_gold_weight_22k
      await connection.execute(`
        UPDATE inventory 
        SET balance_gold_weight_22k = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [correctAvailableBalance, record.id]);

      console.log(`   ✅ Updated available balance to ${correctAvailableBalance}g`);
    }

    console.log('\n🎉 All records updated successfully!');

    // Verify the updates
    console.log('\n📊 Verification - Updated Records:');
    const [updatedRecords] = await connection.execute(`
      SELECT id, procured_in_24k, balance_weight_22k, balance_gold_weight_22k,
             sold_gold_weight_22k
      FROM inventory 
      ORDER BY id
    `);

    for (const record of updatedRecords) {
      console.log(`\nRecord ID: ${record.id}`);
      console.log(`• Procured 24K: ${record.procured_in_24k}g`);
      console.log(`• Original 22K Balance: ${record.balance_weight_22k}g`);
      console.log(`• Available 22K Balance: ${record.balance_gold_weight_22k}g`);
      console.log(`• Sold 22K: ${record.sold_gold_weight_22k || 0}g`);
      
      // Verify calculation
      const expectedAvailable = Math.max(0, record.balance_weight_22k - (record.sold_gold_weight_22k || 0));
      const isCorrect = Math.abs(record.balance_gold_weight_22k - expectedAvailable) < 0.001;
      console.log(`• Calculation: ${isCorrect ? '✅ Correct' : '❌ Incorrect'}`);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

// Run the fix
fixBalanceRecords();
