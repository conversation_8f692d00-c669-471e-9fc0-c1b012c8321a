-- MySQL Database Schema for Jewellery Wholesale Management System
-- Run this script to create the database and tables
-- Note: Replace 'jewellery_wholesale_software' with your desired database name from .env.local

CREATE DATABASE IF NOT EXISTS jewellery_wholesale_software;
USE jewellery_wholesale_software;

-- Drop tables if they exist (for clean setup)
DROP TABLE IF EXISTS bills;
DROP TABLE IF EXISTS inventory;
DROP TABLE IF EXISTS customers;
DROP TABLE IF EXISTS suppliers;
DROP TABLE IF EXISTS gold_rates;
DROP TABLE IF EXISTS users;

-- Suppliers table
CREATE TABLE suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    contact_person VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    speciality VARCHAR(255),
    total_purchases DECIMAL(15,2) DEFAULT 0,
    last_purchase_date DATE,
    status ENUM('Active', 'Inactive') DEFAULT 'Active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_suppliers_name (name),
    INDEX idx_suppliers_status (status)
);

-- Customers table
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    contact_person VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    total_purchases DECIMAL(15,2) DEFAULT 0,
    last_purchase_date DATE,
    status ENUM('Active', 'Inactive') DEFAULT 'Active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_customers_name (name),
    INDEX idx_customers_status (status)
);

-- Inventory table
CREATE TABLE inventory (
    id INT AUTO_INCREMENT PRIMARY KEY,
    supplier_id INT,
    product_name VARCHAR(255) NOT NULL,
    product_type VARCHAR(255),
    with_stone_weight DECIMAL(10,3) DEFAULT 0,
    without_stone_weight DECIMAL(10,3) DEFAULT 0,
    with_stone_cost DECIMAL(5,2) DEFAULT 0,
    without_stone_cost DECIMAL(5,2) DEFAULT 0,
    procured_in_24k DECIMAL(10,3) DEFAULT 0,
    sold_value_with_stone DECIMAL(10,3) DEFAULT 0,
    sold_value_without_stone DECIMAL(10,3) DEFAULT 0,
    balance_weight_24k DECIMAL(10,3) DEFAULT 0,
    balance_weight_22k DECIMAL(10,3) DEFAULT 0,
    status ENUM('Available', 'Low Stock', 'Out of Stock') DEFAULT 'Available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
    INDEX idx_inventory_supplier_id (supplier_id),
    INDEX idx_inventory_product_name (product_name),
    INDEX idx_inventory_status (status)
);

-- Bills table
CREATE TABLE bills (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT,
    bill_number VARCHAR(50) UNIQUE,
    product_name VARCHAR(255),
    product_type VARCHAR(255),
    with_stone DECIMAL(10,3) DEFAULT 0,
    without_stone DECIMAL(10,3) DEFAULT 0,
    gross_weight DECIMAL(10,3) DEFAULT 0,
    stone_weight DECIMAL(10,3) DEFAULT 0,
    net_weight DECIMAL(10,3) DEFAULT 0,
    tunch_with_stone INT DEFAULT 0,
    tunch_without_stone INT DEFAULT 0,
    weight_in_24k DECIMAL(10,3) DEFAULT 0,
    gold_24k_price DECIMAL(10,2) DEFAULT 0,
    stone_price DECIMAL(10,2) DEFAULT 0,
    making_charges DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    status ENUM('Pending', 'Completed', 'Cancelled') DEFAULT 'Pending',
    bill_date DATE DEFAULT (CURRENT_DATE),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    INDEX idx_bills_customer_id (customer_id),
    INDEX idx_bills_bill_date (bill_date),
    INDEX idx_bills_status (status)
);

-- Gold rates table
CREATE TABLE gold_rates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rate_24k DECIMAL(10,2) NOT NULL,
    rate_22k DECIMAL(10,2) NOT NULL,
    rate_18k DECIMAL(10,2) NOT NULL,
    rate_date DATE DEFAULT (CURRENT_DATE),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_gold_rates_date (rate_date)
);

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    role ENUM('Admin', 'Manager', 'Staff', 'Viewer') DEFAULT 'Staff',
    status ENUM('Active', 'Inactive') DEFAULT 'Active',
    last_login TIMESTAMP NULL,
    permissions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_users_email (email),
    INDEX idx_users_role (role),
    INDEX idx_users_status (status)
);
