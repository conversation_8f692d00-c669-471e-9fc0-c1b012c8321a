import { executeQuery, executeSingle, executeUpdate } from "../database"

export interface GoldRate {
  id: number
  rate_24k: number
  rate_22k: number
  rate_18k: number
  rate_date: string
  created_at: string
}

export interface CreateGoldRateData {
  rate_24k: number
  rate_22k?: number
  rate_18k?: number
  rate_date?: string
}

export class GoldRateModel {
  // Get all gold rates
  static async getAll(): Promise<GoldRate[]> {
    const query = "SELECT * FROM gold_rates ORDER BY rate_date DESC"
    return executeQuery<GoldRate>(query)
  }

  // Get current gold rate
  static async getCurrent(): Promise<GoldRate | null> {
    const query = "SELECT * FROM gold_rates ORDER BY rate_date DESC LIMIT 1"
    const results = await executeQuery<GoldRate>(query)
    return results[0] || null
  }

  // Get gold rate by date
  static async getByDate(date: string): Promise<GoldRate | null> {
    const query = "SELECT * FROM gold_rates WHERE rate_date = ?"
    const results = await executeQuery<GoldRate>(query, [date])
    return results[0] || null
  }

  // Create new gold rate
  static async create(data: CreateGoldRateData): Promise<number> {
    const rate22k = data.rate_22k || data.rate_24k * 0.916
    const rate18k = data.rate_18k || data.rate_24k * 0.75
    const rateDate = data.rate_date || new Date().toISOString().slice(0, 10)

    const query = `
      INSERT INTO gold_rates (rate_24k, rate_22k, rate_18k, rate_date)
      VALUES (?, ?, ?, ?)
    `
    const result = await executeUpdate(query, [data.rate_24k, rate22k, rate18k, rateDate])
    return result.insertId!
  }

  // Update gold rate
  static async update(id: number, data: Partial<CreateGoldRateData>): Promise<boolean> {
    const fields = Object.keys(data)
      .map((key) => `${key} = ?`)
      .join(", ")
    const values = Object.values(data)

    const query = `UPDATE gold_rates SET ${fields} WHERE id = ?`
    const result = await executeUpdate(query, [...values, id])
    return result.affectedRows > 0
  }

  // Delete gold rate
  static async delete(id: number): Promise<boolean> {
    const query = "DELETE FROM gold_rates WHERE id = ?"
    const result = await executeUpdate(query, [id])
    return result.affectedRows > 0
  }

  // Get rate history for a date range
  static async getHistory(startDate: string, endDate: string): Promise<GoldRate[]> {
    const query = `
      SELECT * FROM gold_rates 
      WHERE rate_date BETWEEN ? AND ?
      ORDER BY rate_date DESC
    `
    return executeQuery<GoldRate>(query, [startDate, endDate])
  }

  // Get rate changes
  static async getRateChanges(limit = 10): Promise<
    Array<
      GoldRate & {
        change_24k: number
        change_percent: number
      }
    >
  > {
    const query = `
      SELECT 
        gr.*,
        COALESCE(gr.rate_24k - LAG(gr.rate_24k) OVER (ORDER BY gr.rate_date), 0) as change_24k,
        COALESCE(
          ((gr.rate_24k - LAG(gr.rate_24k) OVER (ORDER BY gr.rate_date)) / 
           LAG(gr.rate_24k) OVER (ORDER BY gr.rate_date)) * 100, 
          0
        ) as change_percent
      FROM gold_rates gr
      ORDER BY gr.rate_date DESC
      LIMIT ?
    `
    return executeQuery(query, [limit])
  }
}
