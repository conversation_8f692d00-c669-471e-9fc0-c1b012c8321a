import { type NextRequest, NextResponse } from "next/server"
import { BillModel } from "@/lib/models/bill"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const bill = await BillModel.getById(id)

    if (!bill) {
      return NextResponse.json({ success: false, error: "Bill not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: bill })
  } catch (error) {
    console.error("Error fetching bill:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch bill" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const { status } = await request.json()

    const success = await BillModel.updateStatus(id, status)

    if (!success) {
      return NextResponse.json({ success: false, error: "Bill not found" }, { status: 404 })
    }

    const bill = await BillModel.getById(id)
    return NextResponse.json({ success: true, data: bill })
  } catch (error) {
    console.error("Error updating bill:", error)
    return NextResponse.json({ success: false, error: "Failed to update bill" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const success = await BillModel.delete(id)

    if (!success) {
      return NextResponse.json({ success: false, error: "Bill not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "Bill deleted successfully" })
  } catch (error) {
    console.error("Error deleting bill:", error)
    return NextResponse.json({ success: false, error: "Failed to delete bill" }, { status: 500 })
  }
}
