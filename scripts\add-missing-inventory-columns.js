const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function addMissingColumns() {
  let connection;

  try {
    console.log('🔌 Connecting to database...');
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
    });

    console.log('✅ Connected to database');

    // Check current table structure
    console.log('\n📊 Checking current inventory table structure...');
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME]);

    const existingColumns = columns.map(col => col.COLUMN_NAME);
    console.log('Current columns:', existingColumns);

    // Define all required columns
    const requiredColumns = [
      { name: 'metal_type', definition: "ENUM('Gold', 'Silver', 'Platinum') DEFAULT 'Gold'" },
      { name: 'form_type', definition: "ENUM('Bar', 'Jewel', 'Old Jewel') DEFAULT 'Jewel'" },
      { name: 'jewel_type', definition: "ENUM('With Stone', 'Without Stone') NULL" },
      { name: 'jewel_category', definition: "VARCHAR(100) NULL" },
      { name: 'stone_weight', definition: "DECIMAL(10,3) DEFAULT 0.000" },
      { name: 'with_stone_tunch_percentage', definition: "DECIMAL(5,2) DEFAULT 0.00" },
      { name: 'without_stone_tunch_percentage', definition: "DECIMAL(5,2) DEFAULT 0.00" },
      { name: 'stone_weight_22k', definition: "DECIMAL(10,3) DEFAULT 0.000" },
      { name: 'sold_gold_weight_22k', definition: "DECIMAL(10,3) DEFAULT 0.000" },
      { name: 'sold_gold_weight_24k', definition: "DECIMAL(10,3) DEFAULT 0.000" },
      { name: 'balance_gold_weight_22k', definition: "DECIMAL(10,3) DEFAULT 0.000" },
      { name: 'wastage_percentage', definition: "DECIMAL(5,2) DEFAULT 0.00" },
      { name: 'expected_processing_loss', definition: "DECIMAL(10,3) DEFAULT 0.000" },
      { name: 'making_charges', definition: "DECIMAL(10,2) DEFAULT 0.00" }
    ];

    // Find missing columns
    const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col.name));

    if (missingColumns.length === 0) {
      console.log('\n✅ All required columns already exist!');
      return;
    }

    console.log(`\n🔧 Found ${missingColumns.length} missing columns:`);
    missingColumns.forEach(col => console.log(`   - ${col.name}`));

    // Add missing columns
    console.log('\n📝 Adding missing columns...');
    for (const col of missingColumns) {
      try {
        const alterQuery = `ALTER TABLE inventory ADD COLUMN ${col.name} ${col.definition}`;
        console.log(`   Adding: ${col.name}`);
        await connection.execute(alterQuery);
        console.log(`   ✅ Successfully added ${col.name}`);
      } catch (error) {
        console.log(`   ❌ Failed to add ${col.name}: ${error.message}`);
      }
    }

    // Update existing records to have proper balance_gold_weight_22k values
    console.log('\n🔄 Updating existing records...');
    try {
      await connection.execute(`
        UPDATE inventory 
        SET balance_gold_weight_22k = balance_weight_22k 
        WHERE balance_gold_weight_22k = 0 OR balance_gold_weight_22k IS NULL
      `);
      console.log('   ✅ Updated balance_gold_weight_22k for existing records');
    } catch (error) {
      console.log(`   ❌ Failed to update existing records: ${error.message}`);
    }

    // Verify the schema is now correct
    console.log('\n🧪 Testing inventory operations...');
    
    try {
      // Test INSERT query
      const testInsertQuery = `
        INSERT INTO inventory (
          supplier_id, product_name, product_type, metal_type, form_type,
          jewel_type, jewel_category, with_stone_weight, without_stone_weight, stone_weight,
          with_stone_cost, without_stone_cost, with_stone_tunch_percentage,
          without_stone_tunch_percentage, procured_in_24k, balance_weight_24k, balance_weight_22k,
          stone_weight_22k, sold_gold_weight_22k, sold_gold_weight_24k, balance_gold_weight_22k,
          wastage_percentage, expected_processing_loss, making_charges
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      // Just prepare the statement to test syntax
      const [stmt] = await connection.prepare(testInsertQuery);
      await stmt.close();
      console.log('   ✅ INSERT query syntax is valid');

      // Test UPDATE query
      const testUpdateQuery = `
        UPDATE inventory 
        SET supplier_id = ?, product_name = ?, product_type = ?, metal_type = ?, form_type = ?, 
            jewel_type = ?, jewel_category = ?, with_stone_weight = ?, without_stone_weight = ?, 
            stone_weight = ?, with_stone_cost = ?, without_stone_cost = ?, procured_in_24k = ?, 
            balance_weight_24k = ?, balance_weight_22k = ?, balance_gold_weight_22k = ?, 
            with_stone_tunch_percentage = ?, without_stone_tunch_percentage = ?, 
            wastage_percentage = ?, expected_processing_loss = ?, making_charges = ?, 
            updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `;
      
      const [stmt2] = await connection.prepare(testUpdateQuery);
      await stmt2.close();
      console.log('   ✅ UPDATE query syntax is valid');

    } catch (error) {
      console.log(`   ❌ Query test failed: ${error.message}`);
    }

    console.log('\n🎉 Database schema update completed successfully!');
    console.log('\n📊 Final inventory table structure:');
    
    const [finalColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME]);

    finalColumns.forEach(col => {
      console.log(`   ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'NO' ? 'NOT NULL' : 'NULL'} ${col.COLUMN_DEFAULT ? `DEFAULT ${col.COLUMN_DEFAULT}` : ''}`);
    });

  } catch (error) {
    console.error('❌ Error updating database schema:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the migration
addMissingColumns();
