import { type NextRequest, NextResponse } from "next/server"
import { SupplierModel } from "@/lib/models/supplier"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search")

    let suppliers
    if (search) {
      suppliers = await SupplierModel.search(search)
    } else {
      suppliers = await SupplierModel.getAll()
    }

    return NextResponse.json({ success: true, data: suppliers })
  } catch (error) {
    console.error("Error fetching suppliers:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch suppliers" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.name || !data.contact_person) {
      return NextResponse.json({ success: false, error: "Name and contact person are required" }, { status: 400 })
    }

    const supplierId = await SupplierModel.create(data)
    const supplier = await SupplierModel.getById(supplierId)

    return NextResponse.json({ success: true, data: supplier }, { status: 201 })
  } catch (error) {
    console.error("Error creating supplier:", error)
    return NextResponse.json({ success: false, error: "Failed to create supplier" }, { status: 500 })
  }
}
