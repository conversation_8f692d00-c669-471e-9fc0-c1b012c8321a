#!/usr/bin/env node

require("dotenv").config({ path: ".env.local" })

const mysql = require("mysql2/promise")

async function testBillCreation() {
  console.log("🧪 Testing bill creation and inventory stock reduction...\n")

  const config = {
    host: process.env.DB_HOST || "localhost",
    port: Number.parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "jewellery_wholesale_software",
  }

  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(config)

    // Get initial inventory stock for item ID 1 (Chain)
    const [initialStock] = await connection.execute(
      "SELECT id, product_name, balance_weight_24k, balance_weight_22k, status FROM inventory WHERE id = 1"
    )
    
    if (initialStock.length === 0) {
      console.log("❌ Inventory item not found!")
      return
    }

    const item = initialStock[0]
    console.log("📦 Initial Inventory Stock:")
    console.log(`   Product: ${item.product_name}`)
    console.log(`   24K Stock: ${Number(item.balance_weight_24k).toFixed(3)}g`)
    console.log(`   22K Stock: ${Number(item.balance_weight_22k).toFixed(3)}g`)
    console.log(`   Status: ${item.status}`)

    // Create a test bill
    const weightToSell = 5.000 // 5 grams
    const tunchPercent = 95
    const weight24k = (weightToSell * tunchPercent) / 100 // 4.75g in 24K equivalent
    
    console.log(`\n🧾 Creating test bill:`)
    console.log(`   Weight to sell: ${weightToSell}g`)
    console.log(`   Tunch: ${tunchPercent}%`)
    console.log(`   24K equivalent: ${weight24k}g`)

    const billData = {
      customer_id: 1, // Assuming customer ID 1 exists
      inventory_item_id: 1, // Chain
      bill_number: `TEST${Date.now()}`,
      product_name: item.product_name,
      product_type: "Gold Chain",
      with_stone: 0,
      without_stone: weightToSell,
      gross_weight: weightToSell,
      stone_weight: 0,
      net_weight: weightToSell,
      tunch_with_stone: 0,
      tunch_without_stone: tunchPercent,
      weight_in_24k: weight24k,
      gold_24k_price: 10140.00,
      stone_price: 0,
      making_charges: 10.00,
      total_amount: (weight24k * 10140.00) + 10.00,
      status: 'Pending'
    }

    // Insert the bill
    const [billResult] = await connection.execute(`
      INSERT INTO bills (
        customer_id, inventory_item_id, bill_number, product_name, product_type, 
        with_stone, without_stone, gross_weight, stone_weight, net_weight, 
        tunch_with_stone, tunch_without_stone, weight_in_24k, gold_24k_price, 
        stone_price, making_charges, total_amount, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      billData.customer_id, billData.inventory_item_id, billData.bill_number,
      billData.product_name, billData.product_type, billData.with_stone,
      billData.without_stone, billData.gross_weight, billData.stone_weight,
      billData.net_weight, billData.tunch_with_stone, billData.tunch_without_stone,
      billData.weight_in_24k, billData.gold_24k_price, billData.stone_price,
      billData.making_charges, billData.total_amount, billData.status
    ])

    console.log(`✅ Bill created with ID: ${billResult.insertId}`)

    // Update inventory stock
    const newStock24k = Number(item.balance_weight_24k) - weight24k
    const newStock22k = newStock24k * 0.916
    const newStatus = newStock24k <= 0 ? 'Out of Stock' : newStock24k < 10 ? 'Low Stock' : 'Available'

    await connection.execute(`
      UPDATE inventory 
      SET balance_weight_24k = ?, balance_weight_22k = ?, status = ?
      WHERE id = ?
    `, [newStock24k, newStock22k, newStatus, 1])

    console.log(`✅ Inventory updated`)

    // Check final inventory stock
    const [finalStock] = await connection.execute(
      "SELECT id, product_name, balance_weight_24k, balance_weight_22k, status FROM inventory WHERE id = 1"
    )

    const finalItem = finalStock[0]
    console.log("\n📦 Final Inventory Stock:")
    console.log(`   Product: ${finalItem.product_name}`)
    console.log(`   24K Stock: ${Number(finalItem.balance_weight_24k).toFixed(3)}g`)
    console.log(`   22K Stock: ${Number(finalItem.balance_weight_22k).toFixed(3)}g`)
    console.log(`   Status: ${finalItem.status}`)

    console.log("\n📊 Summary:")
    console.log(`   Stock Reduced: ${(Number(item.balance_weight_24k) - Number(finalItem.balance_weight_24k)).toFixed(3)}g`)
    console.log(`   Bill Total: ₹${billData.total_amount.toLocaleString('en-IN')}`)

    await connection.end()
    console.log("\n🎉 Test completed successfully!")
    
  } catch (error) {
    console.log("\n❌ Test failed:")
    console.log(`   Error: ${error.message}`)
    process.exit(1)
  }
}

testBillCreation()
