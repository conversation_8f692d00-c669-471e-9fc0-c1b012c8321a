async function analyzeCostData() {
  console.log('🔍 ANALYZING COST DATA STRUCTURE');
  console.log('================================\n');

  try {
    const response = await fetch('http://localhost:3000/api/inventory');
    if (response.ok) {
      const data = await response.json();
      if (data.success && data.data.length > 0) {
        const item = data.data[0];
        
        console.log('📊 CURRENT DATABASE VALUES:');
        console.log('===========================');
        console.log(`with_stone_cost: ${item.with_stone_cost}`);
        console.log(`without_stone_cost: ${item.without_stone_cost}`);
        console.log(`with_stone_tunch_percentage: ${item.with_stone_tunch_percentage}`);
        console.log(`without_stone_tunch_percentage: ${item.without_stone_tunch_percentage}`);
        
        console.log('\n📋 YOUR CSV DATA REFERENCE:');
        console.log('============================');
        console.log('Chain, 0, 120.420, 93, 94, 113.195');
        console.log('Positions:');
        console.log('- With Stone Weight: 0');
        console.log('- Without Stone Weight: 120.420');
        console.log('- With Stone Cost: 93');
        console.log('- Without Stone Cost: 94');
        console.log('- Procured 24K: 113.195');
        
        console.log('\n🤔 ANALYSIS:');
        console.log('=============');
        console.log('From your CSV, the cost values are 93 and 94.');
        console.log('These appear to be tunch percentages, not currency amounts.');
        console.log('');
        console.log('However, in the database we have:');
        console.log('- with_stone_cost: 0.00 (should be 93?)');
        console.log('- without_stone_cost: 94.00 (matches CSV)');
        console.log('- with_stone_tunch_percentage: 93.00 (matches CSV with_stone_cost)');
        console.log('- without_stone_tunch_percentage: 96.00 (different from CSV)');
        
        console.log('\n💡 INTERPRETATION:');
        console.log('==================');
        console.log('It seems like the CSV data mapping might be:');
        console.log('- CSV "With Stone Cost" (93) → DB "with_stone_tunch_percentage"');
        console.log('- CSV "Without Stone Cost" (94) → DB "without_stone_cost" (as percentage)');
        console.log('');
        console.log('The confusion is whether "cost" fields should be:');
        console.log('A) Currency amounts (₹)');
        console.log('B) Tunch percentages (%)');
        console.log('C) Cost percentages (% of gold value)');
        
        console.log('\n🎯 RECOMMENDATION:');
        console.log('==================');
        console.log('Based on your feedback that my fix is wrong,');
        console.log('the cost fields should display as percentages:');
        console.log('- "With Stone: 0.00%"');
        console.log('- "Without: 94.00%"');
        console.log('');
        console.log('This suggests these are cost percentages, not currency amounts.');
      }
    }
  } catch (error) {
    console.log(`❌ Analysis failed: ${error.message}`);
  }
  
  console.log('\n🔧 CORRECT FIX NEEDED:');
  console.log('======================');
  console.log('Revert cost display back to percentage format:');
  console.log('- Change "₹{amount}" back to "{amount}%"');
  console.log('- Keep tunch percentage as separate column');
  console.log('- Clarify what each field represents');
}

analyzeCostData();
