#!/usr/bin/env node

require("dotenv").config({ path: ".env.local" })

const mysql = require("mysql2/promise")

async function addInventoryItemIdColumn() {
  console.log("🔧 Adding inventory_item_id column to bills table...\n")

  const config = {
    host: process.env.DB_HOST || "localhost",
    port: Number.parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "jewellery_wholesale_software",
  }

  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(config)

    // Check if column already exists
    const [columns] = await connection.execute(
      "SHOW COLUMNS FROM bills LIKE 'inventory_item_id'"
    )

    if (columns.length > 0) {
      console.log("✅ inventory_item_id column already exists")
    } else {
      console.log("➕ Adding inventory_item_id column...")
      await connection.execute(`
        ALTER TABLE bills 
        ADD COLUMN inventory_item_id INT AFTER customer_id,
        ADD FOREIGN KEY (inventory_item_id) REFERENCES inventory(id) ON DELETE SET NULL
      `)
      console.log("✅ inventory_item_id column added successfully")
    }

    await connection.end()
    console.log("\n🎉 Database update completed!")
    
  } catch (error) {
    console.log("\n❌ Database update failed:")
    console.log(`   Error: ${error.message}`)
    process.exit(1)
  }
}

addInventoryItemIdColumn()
