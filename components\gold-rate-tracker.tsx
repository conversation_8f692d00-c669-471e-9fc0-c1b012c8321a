"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { TrendingUp, TrendingDown, Minus, RefreshCw, Bell, AlertTriangle, Edit, Trash2 } from "lucide-react"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { useDatabase } from "@/hooks/use-database"
import { useDatabaseMutation } from "@/hooks/use-database-mutation"
import { useToast } from "@/hooks/use-toast"
import { useBusinessSettings } from "@/hooks/use-database-settings"
import { BusinessLogic } from "@/lib/utils/business-logic"

interface GoldRate {
  id: number
  rate_24k: number
  rate_22k: number
  rate_18k: number
  rate_date: string
  created_at: string
}

export default function GoldRateTracker() {
  const { data: goldRates, loading, error, refetch } = useDatabase<GoldRate>("gold-rates")
  const { mutate, loading: mutating } = useDatabaseMutation<GoldRate>()
  const { settings: businessSettings, getConversionFactor } = useBusinessSettings()
  const { toast } = useToast()

  // Get current rates from database with proper fallbacks
  const currentRate = goldRates?.[0] || { rate_24k: 0, rate_22k: 0, rate_18k: 0 }
  const [currentRates, setCurrentRates] = useState({
    rate_24k: currentRate.rate_24k,
    rate_22k: currentRate.rate_22k,
    rate_18k: currentRate.rate_18k,
  })

  const [newRate24k, setNewRate24k] = useState(6890)
  const [alertThreshold, setAlertThreshold] = useState(100)
  const [isUpdating, setIsUpdating] = useState(false)
  const [editingRate, setEditingRate] = useState<GoldRate | null>(null)
  const [editRate24k, setEditRate24k] = useState(0)

  // Use database data for rate history
  const rateHistory = goldRates || []


  const chartData =
    goldRates
      ?.slice(0, 10)
      .reverse()
      .map((rate) => ({
        date: new Date(rate.rate_date).toLocaleDateString(),
        rate_24k: rate.rate_24k,
        rate_22k: rate.rate_22k,
        rate_18k: rate.rate_18k,
      })) || []

  const updateGoldRate = async () => {
    if (!newRate24k || newRate24k <= 0) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid 24K gold rate.",
        variant: "destructive",
      })
      return
    }

    setIsUpdating(true)

    try {
      const rateData = {
        rate_24k: newRate24k,
        rate_22k: Math.round(newRate24k * 0.916),
        rate_18k: Math.round(newRate24k * 0.75),
        rate_date: new Date().toISOString().slice(0, 10), // YYYY-MM-DD format
      }

      const result = await mutate("gold-rates", "POST", rateData)
      if (result) {
        toast({
          title: "Gold Rate Updated",
          description: `New 24K rate: ₹${newRate24k}/g has been saved.`,
          variant: "default",
        })

        setCurrentRates({
          rate_24k: rateData.rate_24k,
          rate_22k: rateData.rate_22k,
          rate_18k: rateData.rate_18k,
        })
        await refetch()
      } else {
        toast({
          title: "Update Failed",
          description: "Failed to update gold rate. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Update Error",
        description: "An error occurred while updating the gold rate.",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const handleEditRate = (rate: GoldRate) => {
    setEditingRate(rate)
    setEditRate24k(rate.rate_24k)
  }

  const handleUpdateRate = async () => {
    if (!editingRate) return

    if (!editRate24k || editRate24k <= 0) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid 24K gold rate.",
        variant: "destructive",
      })
      return
    }

    try {
      const rateData = {
        rate_24k: editRate24k,
        rate_22k: Math.round(editRate24k * 0.916),
        rate_18k: Math.round(editRate24k * 0.75),
      }

      const result = await mutate(`gold-rates/${editingRate.id}`, "PUT", rateData)
      if (result) {
        toast({
          title: "Gold Rate Updated",
          description: `Rate updated to ₹${editRate24k}/g for 24K gold.`,
          variant: "default",
        })
        setEditingRate(null)
        await refetch()
      } else {
        toast({
          title: "Update Failed",
          description: "Failed to update gold rate. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Update Error",
        description: "An error occurred while updating the gold rate.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteRate = async (id: number) => {
    const rate = goldRates?.find(r => r.id === id)
    const rateDate = rate ? new Date(rate.rate_date).toLocaleDateString() : `Rate #${id}`

    if (confirm(`Are you sure you want to delete the gold rate from ${rateDate}? This action cannot be undone.`)) {
      try {
        const result = await mutate(`gold-rates/${id}`, "DELETE")
        if (result && (result as any).deleted) {
          toast({
            title: "Gold Rate Deleted",
            description: `Gold rate from ${rateDate} has been successfully deleted.`,
            variant: "default",
          })
          await refetch()
        } else {
          toast({
            title: "Delete Failed",
            description: "Failed to delete the gold rate. Please try again.",
            variant: "destructive",
          })
        }
      } catch (error) {
        toast({
          title: "Delete Error",
          description: "An error occurred while deleting the gold rate.",
          variant: "destructive",
        })
      }
    }
  }

  const getTrendIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4 text-green-600" />
    if (change < 0) return <TrendingDown className="h-4 w-4 text-red-600" />
    return <Minus className="h-4 w-4 text-gray-600" />
  }

  const getTrendColor = (change: number) => {
    if (change > 0) return "text-green-600"
    if (change < 0) return "text-red-600"
    return "text-gray-600"
  }

  // Calculate change from the difference between latest and previous rates
  const latestChange = rateHistory.length >= 2
    ? (Number(rateHistory[0]?.rate_24k) || 0) - (Number(rateHistory[1]?.rate_24k) || 0)
    : 0
  const shouldAlert = Math.abs(latestChange) >= alertThreshold

  return (
    <div className="space-y-6">
      {shouldAlert && (
        <Alert className="border-amber-200 bg-amber-50">
          <AlertTriangle className="h-4 w-4 text-amber-600" />
          <AlertDescription className="text-amber-800">
            Gold rate has changed by ₹{Math.abs(latestChange)} which exceeds your alert threshold of ₹
            {alertThreshold}.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">24K Gold Rate</CardTitle>
            {getTrendIcon(latestChange)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{(currentRates.rate_24k || 0).toLocaleString("en-IN")}</div>
            <p className={`text-xs ${getTrendColor(latestChange)}`}>
              {latestChange > 0 ? "+" : ""}₹{latestChange} per 10g
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">22K Gold Rate</CardTitle>
            {getTrendIcon(latestChange)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{(currentRates.rate_22k || 0).toLocaleString("en-IN")}</div>
            <p className={`text-xs ${getTrendColor(latestChange)}`}>
              {latestChange > 0 ? "+" : ""}₹{Math.round(latestChange * 0.916)} per 10g
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">18K Gold Rate</CardTitle>
            {getTrendIcon(latestChange)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{(currentRates.rate_18k || 0).toLocaleString("en-IN")}</div>
            <p className={`text-xs ${getTrendColor(latestChange)}`}>
              {latestChange > 0 ? "+" : ""}₹{Math.round(latestChange * 0.75)} per 10g
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Update Gold Rate</CardTitle>
            <CardDescription>Manually update the current gold rates</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="newRate">24K Gold Rate (per 10g)</Label>
              <Input
                id="newRate"
                type="number"
                value={newRate24k}
                onChange={(e) => setNewRate24k(Number.parseFloat(e.target.value) || 0)}
                placeholder="Enter new rate"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="alertThreshold">Price Alert Threshold (₹)</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="alertThreshold"
                  type="number"
                  value={alertThreshold}
                  onChange={(e) => setAlertThreshold(Number.parseFloat(e.target.value) || 0)}
                  placeholder="Alert threshold"
                />
                <Bell className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
            <Button onClick={updateGoldRate} disabled={isUpdating} className="w-full">
              {isUpdating ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Rate"
              )}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Rate Trend (Last 10 Days)</CardTitle>
            <CardDescription>Gold rate movement visualization</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis domain={["dataMin - 50", "dataMax + 50"]} />
                <Tooltip
                  formatter={(value, name) => [
                    `₹${value}`,
                    name === "rate_24k" ? "24K" : name === "rate_22k" ? "22K" : "18K",
                  ]}
                />
                <Line type="monotone" dataKey="rate_24k" stroke="#f59e0b" strokeWidth={2} name="24K" />
                <Line type="monotone" dataKey="rate_22k" stroke="#10b981" strokeWidth={2} name="22K" />
                <Line type="monotone" dataKey="rate_18k" stroke="#3b82f6" strokeWidth={2} name="18K" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Rate History</CardTitle>
          <CardDescription>Historical gold rate data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto max-h-[400px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>24K Rate</TableHead>
                    <TableHead>22K Rate</TableHead>
                    <TableHead>18K Rate</TableHead>
                    <TableHead>Change</TableHead>
                    <TableHead>Trend</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {rateHistory.map((rate, index) => {
                    const change = index < rateHistory.length - 1
                      ? (Number(rate.rate_24k) || 0) - (Number(rateHistory[index + 1]?.rate_24k) || 0)
                      : 0
                    return (
                      <TableRow key={rate.id}>
                        <TableCell>{new Date(rate.rate_date).toLocaleDateString()}</TableCell>
                        <TableCell className="font-medium">₹{(Number(rate.rate_24k) || 0).toLocaleString("en-IN")}</TableCell>
                        <TableCell>₹{(Number(rate.rate_22k) || 0).toLocaleString("en-IN")}</TableCell>
                        <TableCell>₹{(Number(rate.rate_18k) || 0).toLocaleString("en-IN")}</TableCell>
                        <TableCell className={getTrendColor(change)}>
                          {change > 0 ? "+" : ""}₹{change.toFixed(0)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getTrendIcon(change)}
                            <Badge variant={change > 0 ? "default" : change < 0 ? "destructive" : "secondary"}>
                              {change > 0 ? "+" : ""}
                              {((change / (Number(rateHistory[index + 1]?.rate_24k) || 1)) * 100).toFixed(2)}%
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm" onClick={() => handleEditRate(rate)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700 bg-transparent"
                              onClick={() => handleDeleteRate(rate.id)}
                              disabled={mutating}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Edit Rate Dialog */}
      <Dialog open={!!editingRate} onOpenChange={() => setEditingRate(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Gold Rate</DialogTitle>
            <DialogDescription>
              Update the 24K gold rate. 22K and 18K rates will be calculated automatically.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-rate">24K Gold Rate (₹/10g)</Label>
              <Input
                id="edit-rate"
                type="number"
                value={editRate24k}
                onChange={(e) => setEditRate24k(Number.parseFloat(e.target.value) || 0)}
                placeholder="Enter 24K rate"
              />
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
              <div>
                <span className="font-medium">22K Rate:</span> ₹{Math.round(editRate24k * 0.916).toLocaleString("en-IN")}
              </div>
              <div>
                <span className="font-medium">18K Rate:</span> ₹{Math.round(editRate24k * 0.75).toLocaleString("en-IN")}
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setEditingRate(null)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateRate} disabled={mutating}>
              Update Rate
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
