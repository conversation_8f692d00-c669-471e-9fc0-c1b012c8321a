"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { TrendingUp, TrendingDown, Minus, RefreshCw, Bell, AlertTriangle } from "lucide-react"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { useDatabase } from "@/hooks/use-database"
import { useDatabaseMutation } from "@/hooks/use-database-mutation"

interface GoldRate {
  id: number
  date: string
  rate24k: number
  rate22k: number
  rate18k: number
  change: number
  changePercent: number
  rate_24k: number
  rate_22k: number
  rate_18k: number
  rate_date: string
}

export default function GoldRateTracker() {
  const { data: goldRates, loading, error, refetch } = useDatabase<GoldRate>("gold-rates")
  const { mutate, loading: mutating } = useDatabaseMutation<GoldRate>()

  // Get current rates from database
  const currentRate = goldRates?.[0] || { rate_24k: 6890, rate_22k: 6295, rate_18k: 5168 }
  const [currentRates, setCurrentRates] = useState({
    rate24k: currentRate.rate_24k,
    rate22k: currentRate.rate_22k,
    rate18k: currentRate.rate_18k,
  })

  const [newRate24k, setNewRate24k] = useState(6890)
  const [alertThreshold, setAlertThreshold] = useState(100)
  const [isUpdating, setIsUpdating] = useState(false)

  const [rateHistory, setRateHistory] = useState<GoldRate[]>([
    {
      id: 1,
      date: "2024-01-29",
      rate24k: 6890,
      rate22k: 6295,
      rate18k: 5168,
      change: 30,
      changePercent: 0.44,
      rate_24k: 0,
      rate_22k: 0,
      rate_18k: 0,
      rate_date: "",
    },
    {
      id: 2,
      date: "2024-01-28",
      rate24k: 6860,
      rate22k: 6268,
      rate18k: 5145,
      change: -20,
      changePercent: -0.29,
      rate_24k: 0,
      rate_22k: 0,
      rate_18k: 0,
      rate_date: "",
    },
    {
      id: 3,
      date: "2024-01-27",
      rate24k: 6880,
      rate22k: 6286,
      rate18k: 5160,
      change: 40,
      changePercent: 0.58,
      rate_24k: 0,
      rate_22k: 0,
      rate_18k: 0,
      rate_date: "",
    },
    {
      id: 4,
      date: "2024-01-26",
      rate24k: 6840,
      rate22k: 6250,
      rate18k: 5130,
      change: -15,
      changePercent: -0.22,
      rate_24k: 0,
      rate_22k: 0,
      rate_18k: 0,
      rate_date: "",
    },
    {
      id: 5,
      date: "2024-01-25",
      rate24k: 6855,
      rate22k: 6264,
      rate18k: 5141,
      change: 25,
      changePercent: 0.37,
      rate_24k: 0,
      rate_22k: 0,
      rate_18k: 0,
      rate_date: "",
    },
  ])

  const chartData =
    goldRates
      ?.slice(0, 10)
      .reverse()
      .map((rate) => ({
        date: new Date(rate.rate_date).toLocaleDateString(),
        rate24k: rate.rate_24k,
        rate22k: rate.rate_22k,
        rate18k: rate.rate_18k,
      })) || []

  const updateGoldRate = async () => {
    setIsUpdating(true)

    try {
      const rateData = {
        rate_24k: newRate24k,
        rate_22k: Math.round(newRate24k * 0.916),
        rate_18k: Math.round(newRate24k * 0.75),
        rate_date: new Date().toISOString().slice(0, 10), // YYYY-MM-DD format
      }

      const result = await mutate("gold-rates", "POST", rateData)
      if (result) {
        setCurrentRates({
          rate24k: rateData.rate_24k,
          rate22k: rateData.rate_22k,
          rate18k: rateData.rate_18k,
        })
        refetch()
      }
    } finally {
      setIsUpdating(false)
    }
  }

  const getTrendIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4 text-green-600" />
    if (change < 0) return <TrendingDown className="h-4 w-4 text-red-600" />
    return <Minus className="h-4 w-4 text-gray-600" />
  }

  const getTrendColor = (change: number) => {
    if (change > 0) return "text-green-600"
    if (change < 0) return "text-red-600"
    return "text-gray-600"
  }

  const latestChange = rateHistory[0]?.change || 0
  const shouldAlert = Math.abs(latestChange) >= alertThreshold

  return (
    <div className="space-y-6">
      {shouldAlert && (
        <Alert className="border-amber-200 bg-amber-50">
          <AlertTriangle className="h-4 w-4 text-amber-600" />
          <AlertDescription className="text-amber-800">
            Gold rate has changed by ₹{Math.abs(latestChange)} (
            {Math.abs(rateHistory[0]?.changePercent || 0).toFixed(2)}%) which exceeds your alert threshold of ₹
            {alertThreshold}.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">24K Gold Rate</CardTitle>
            {getTrendIcon(latestChange)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{currentRates.rate24k.toLocaleString("en-IN")}</div>
            <p className={`text-xs ${getTrendColor(latestChange)}`}>
              {latestChange > 0 ? "+" : ""}₹{latestChange} per 10g
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">22K Gold Rate</CardTitle>
            {getTrendIcon(latestChange)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{currentRates.rate22k.toLocaleString("en-IN")}</div>
            <p className={`text-xs ${getTrendColor(latestChange)}`}>
              {latestChange > 0 ? "+" : ""}₹{Math.round(latestChange * 0.916)} per 10g
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">18K Gold Rate</CardTitle>
            {getTrendIcon(latestChange)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{currentRates.rate18k.toLocaleString("en-IN")}</div>
            <p className={`text-xs ${getTrendColor(latestChange)}`}>
              {latestChange > 0 ? "+" : ""}₹{Math.round(latestChange * 0.75)} per 10g
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Update Gold Rate</CardTitle>
            <CardDescription>Manually update the current gold rates</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="newRate">24K Gold Rate (per 10g)</Label>
              <Input
                id="newRate"
                type="number"
                value={newRate24k}
                onChange={(e) => setNewRate24k(Number.parseFloat(e.target.value) || 0)}
                placeholder="Enter new rate"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="alertThreshold">Price Alert Threshold (₹)</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="alertThreshold"
                  type="number"
                  value={alertThreshold}
                  onChange={(e) => setAlertThreshold(Number.parseFloat(e.target.value) || 0)}
                  placeholder="Alert threshold"
                />
                <Bell className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
            <Button onClick={updateGoldRate} disabled={isUpdating} className="w-full">
              {isUpdating ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Rate"
              )}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Rate Trend (Last 10 Days)</CardTitle>
            <CardDescription>Gold rate movement visualization</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis domain={["dataMin - 50", "dataMax + 50"]} />
                <Tooltip
                  formatter={(value, name) => [
                    `₹${value}`,
                    name === "rate24k" ? "24K" : name === "rate22k" ? "22K" : "18K",
                  ]}
                />
                <Line type="monotone" dataKey="rate24k" stroke="#f59e0b" strokeWidth={2} name="24K" />
                <Line type="monotone" dataKey="rate22k" stroke="#10b981" strokeWidth={2} name="22K" />
                <Line type="monotone" dataKey="rate18k" stroke="#3b82f6" strokeWidth={2} name="18K" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Rate History</CardTitle>
          <CardDescription>Historical gold rate data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto max-h-[400px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>24K Rate</TableHead>
                    <TableHead>22K Rate</TableHead>
                    <TableHead>18K Rate</TableHead>
                    <TableHead>Change</TableHead>
                    <TableHead>Trend</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {rateHistory.map((rate) => (
                    <TableRow key={rate.id}>
                      <TableCell>{new Date(rate.date).toLocaleDateString()}</TableCell>
                      <TableCell className="font-medium">₹{(Number(rate.rate24k) || 0).toLocaleString("en-IN")}</TableCell>
                      <TableCell>₹{(Number(rate.rate22k) || 0).toLocaleString("en-IN")}</TableCell>
                      <TableCell>₹{(Number(rate.rate18k) || 0).toLocaleString("en-IN")}</TableCell>
                      <TableCell className={getTrendColor(rate.change)}>
                        {(Number(rate.change) || 0) > 0 ? "+" : ""}₹{Number(rate.change) || 0}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getTrendIcon(rate.change)}
                          <Badge variant={rate.change > 0 ? "default" : rate.change < 0 ? "destructive" : "secondary"}>
                            {rate.changePercent > 0 ? "+" : ""}
                            {(Number(rate.changePercent) || 0).toFixed(2)}%
                          </Badge>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
