"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Printer, Download, Eye } from "lucide-react"

interface InvoiceData {
  invoiceNumber: string
  date: string
  customerName: string
  customerAddress: string
  customerPhone: string
  items: Array<{
    description: string
    weight: number
    rate: number
    amount: number
  }>
  subtotal: number
  tax: number
  total: number
  goldRate: number
}

export default function PrintInvoice({ billData }: { billData?: any }) {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)

  const sampleInvoice: InvoiceData = {
    invoiceNumber: "INV-2024-001",
    date: new Date().toLocaleDateString(),
    customerName: "VS Jewellery",
    customerAddress: "12, Main Street, Nammakal, Tamil Nadu - 637001",
    customerPhone: "+91 99887 76655",
    items: [
      {
        description: "22K Gold Chain - 10.16g",
        weight: 10.16,
        rate: 6890,
        amount: 98901.5,
      },
    ],
    subtotal: 98901.5,
    tax: 0,
    total: 98901.5,
    goldRate: 6890,
  }

  const handlePrint = () => {
    const printContent = document.getElementById("invoice-content")
    if (printContent) {
      const printWindow = window.open("", "_blank")
      if (printWindow) {
        printWindow.document.write(`
        <html>
          <head>
            <title>Invoice ${sampleInvoice.invoiceNumber}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .text-center { text-align: center; }
              .text-right { text-align: right; }
              .font-bold { font-weight: bold; }
              .border { border: 1px solid #ccc; }
              .p-3 { padding: 12px; }
              table { width: 100%; border-collapse: collapse; }
              th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
              .bg-amber-50 { background-color: #fef3c7; }
            </style>
          </head>
          <body>
            ${printContent.innerHTML}
          </body>
        </html>
      `)
        printWindow.document.close()
        printWindow.print()
        printWindow.close()
      }
    }
  }

  const handleDownloadPDF = () => {
    // Use browser's print dialog with PDF option
    const printContent = document.getElementById("invoice-content")
    if (printContent) {
      window.print()
    }
  }

  const InvoiceTemplate = ({ data }: { data: InvoiceData }) => (
    <div className="max-w-4xl mx-auto p-8 bg-white text-black" id="invoice-content">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-amber-600 mb-2">SHREE JEWELLERS</h1>
        <p className="text-lg">Wholesale Gold & Diamond Jewelry</p>
        <p className="text-sm text-gray-600">
          123, Jewelry Street, Coimbatore, Tamil Nadu - 641001
          <br />
          Phone: +91 98765 43210 | Email: <EMAIL>
          <br />
          GSTIN: 33ABCDE1234F1Z5
        </p>
      </div>

      <Separator className="mb-6" />

      {/* Invoice Details */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="font-bold text-lg mb-3">Bill To:</h3>
          <p className="font-semibold">{data.customerName}</p>
          <p className="text-sm text-gray-600">{data.customerAddress}</p>
          <p className="text-sm text-gray-600">Phone: {data.customerPhone}</p>
        </div>
        <div className="text-right">
          <div className="mb-4">
            <p className="text-2xl font-bold">INVOICE</p>
            <p className="text-lg">{data.invoiceNumber}</p>
          </div>
          <div className="space-y-1">
            <p>
              <span className="font-semibold">Date:</span> {data.date}
            </p>
            <p>
              <span className="font-semibold">Gold Rate (24K):</span> ₹{data.goldRate}/g
            </p>
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-8">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-amber-50">
              <th className="border border-gray-300 p-3 text-left">Description</th>
              <th className="border border-gray-300 p-3 text-center">Weight (g)</th>
              <th className="border border-gray-300 p-3 text-center">Rate (₹/g)</th>
              <th className="border border-gray-300 p-3 text-right">Amount (₹)</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={index}>
                <td className="border border-gray-300 p-3">{item.description}</td>
                <td className="border border-gray-300 p-3 text-center">{item.weight.toFixed(3)}</td>
                <td className="border border-gray-300 p-3 text-center">{item.rate.toLocaleString("en-IN")}</td>
                <td className="border border-gray-300 p-3 text-right">
                  {item.amount.toLocaleString("en-IN", { minimumFractionDigits: 2 })}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-80">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>₹{data.subtotal.toLocaleString("en-IN", { minimumFractionDigits: 2 })}</span>
            </div>
            {data.tax > 0 && (
              <div className="flex justify-between">
                <span>Tax (3% GST):</span>
                <span>₹{data.tax.toLocaleString("en-IN", { minimumFractionDigits: 2 })}</span>
              </div>
            )}
            <Separator />
            <div className="flex justify-between font-bold text-lg">
              <span>Total:</span>
              <span>₹{data.total.toLocaleString("en-IN", { minimumFractionDigits: 2 })}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Amount in Words */}
      <div className="mb-8">
        <p className="font-semibold">Amount in Words:</p>
        <p className="text-sm bg-gray-50 p-2 rounded">
          Rupees Ninety Eight Thousand Nine Hundred One and Fifty Paise Only
        </p>
      </div>

      {/* Terms and Conditions */}
      <div className="mb-8">
        <h4 className="font-bold mb-2">Terms & Conditions:</h4>
        <ul className="text-sm space-y-1 text-gray-600">
          <li>• All gold rates are subject to market fluctuation</li>
          <li>• Goods once sold cannot be returned or exchanged</li>
          <li>• Payment terms: Cash on delivery</li>
          <li>• All disputes subject to Coimbatore jurisdiction</li>
        </ul>
      </div>

      {/* Signature */}
      <div className="flex justify-between items-end">
        <div>
          <p className="text-sm text-gray-600">Customer Signature</p>
          <div className="border-b border-gray-400 w-48 mt-8"></div>
        </div>
        <div className="text-right">
          <p className="font-semibold">For SHREE JEWELLERS</p>
          <div className="border-b border-gray-400 w-48 mt-8 ml-auto"></div>
          <p className="text-sm text-gray-600 mt-2">Authorized Signatory</p>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center mt-8 pt-4 border-t border-gray-300">
        <p className="text-sm text-gray-600">Thank you for your business!</p>
      </div>
    </div>
  )

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="flex items-center gap-2 bg-transparent">
              <Eye className="h-4 w-4" />
              Preview
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Invoice Preview</DialogTitle>
              <DialogDescription>Preview before printing or downloading</DialogDescription>
            </DialogHeader>
            <div className="max-h-[70vh] overflow-y-auto px-1">
              <InvoiceTemplate data={sampleInvoice} />
            </div>
            <div className="flex justify-end gap-2 mt-4 pt-4 border-t bg-white sticky bottom-0">
              <Button variant="outline" onClick={handleDownloadPDF} className="flex items-center gap-2 bg-transparent">
                <Download className="h-4 w-4" />
                Download PDF
              </Button>
              <Button onClick={handlePrint} className="flex items-center gap-2">
                <Printer className="h-4 w-4" />
                Print
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        <Button size="sm" onClick={handlePrint} className="flex items-center gap-2">
          <Printer className="h-4 w-4" />
          Print
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleDownloadPDF}
          className="flex items-center gap-2 bg-transparent"
        >
          <Download className="h-4 w-4" />
          PDF
        </Button>
      </div>

      <style jsx global>{`
        @media print {
          body * {
            visibility: hidden;
          }
          #invoice-content, #invoice-content * {
            visibility: visible;
          }
          #invoice-content {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
        }
      `}</style>
    </div>
  )
}
