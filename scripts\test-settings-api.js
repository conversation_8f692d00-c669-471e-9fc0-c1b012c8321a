const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function testSettingsAPI() {
  let connection;

  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME
  };

  try {
    console.log('🔌 Testing Settings API...');
    connection = await mysql.createConnection(config);
    console.log(`📊 Connected to database: ${process.env.DB_NAME}`);

    // Test direct database query
    console.log('\n🧪 Testing direct database query...');
    const [settings] = await connection.execute(`
      SELECT category, setting_key, setting_value 
      FROM settings 
      WHERE category IN ('conversion', 'wastage', 'business', 'pricing', 'calculation', 'alerts')
      ORDER BY category, setting_key
    `);

    console.log(`✅ Found ${settings.length} business settings in database`);

    // Group by category
    const businessSettings = {};
    settings.forEach(setting => {
      let value;
      try {
        value = typeof setting.setting_value === 'string' 
          ? JSON.parse(setting.setting_value) 
          : setting.setting_value;
      } catch (error) {
        value = setting.setting_value;
      }
      businessSettings[setting.setting_key] = value;
    });

    console.log('\n📊 Business Settings Object:');
    console.log(JSON.stringify(businessSettings, null, 2));

    // Test API endpoint
    console.log('\n🌐 Testing API endpoint...');
    try {
      const response = await fetch('http://localhost:3001/api/settings?business=true');
      if (response.ok) {
        const data = await response.json();
        console.log('✅ API Response:', data);
      } else {
        console.log('❌ API Error:', response.status, response.statusText);
        const errorText = await response.text();
        console.log('Error details:', errorText);
      }
    } catch (error) {
      console.log('❌ API Request failed:', error.message);
      console.log('   Make sure the Next.js server is running on port 3001');
    }

  } catch (error) {
    console.error('❌ Error testing settings:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
testSettingsAPI();
