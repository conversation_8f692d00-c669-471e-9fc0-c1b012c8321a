const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function fixWastageRates() {
  let connection;

  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME
  };

  try {
    console.log('🔌 Connecting to database...');
    connection = await mysql.createConnection(config);
    console.log(`📊 Connected to database: ${process.env.DB_NAME}`);

    console.log('\n=== FIXING WASTAGE RATES ===\n');

    // Update wastage rates to proper default values
    const wastageUpdates = [
      {
        key: 'wastage_rate_bar',
        value: 0.5,
        description: 'Default wastage rate for gold bars (%)'
      },
      {
        key: 'wastage_rate_jewel', 
        value: 2.0,
        description: 'Default wastage rate for jewellery (%)'
      },
      {
        key: 'wastage_rate_old_jewel',
        value: 3.0,
        description: 'Default wastage rate for old jewellery (%)'
      }
    ];

    console.log('🔧 Updating wastage rates to proper defaults...');
    
    for (const update of wastageUpdates) {
      try {
        const [result] = await connection.execute(`
          UPDATE settings 
          SET setting_value = ?, updated_at = CURRENT_TIMESTAMP, updated_by = 'system_fix'
          WHERE category = 'wastage' AND setting_key = ?
        `, [JSON.stringify(update.value), update.key]);

        if (result.affectedRows > 0) {
          console.log(`✅ Updated ${update.key}: ${update.value}%`);
        } else {
          console.log(`❌ Failed to update ${update.key} - setting not found`);
        }
      } catch (error) {
        console.log(`❌ Error updating ${update.key}:`, error.message);
      }
    }

    // Verify the updates
    console.log('\n📊 Verifying updated wastage rates...');
    const [updatedSettings] = await connection.execute(`
      SELECT setting_key, setting_value, updated_at, updated_by
      FROM settings 
      WHERE category = 'wastage'
      ORDER BY setting_key
    `);

    updatedSettings.forEach(setting => {
      let value;
      try {
        value = typeof setting.setting_value === 'string' 
          ? JSON.parse(setting.setting_value) 
          : setting.setting_value;
      } catch (error) {
        value = setting.setting_value;
      }
      
      console.log(`   ${setting.setting_key}: ${value}% (updated: ${setting.updated_at})`);
    });

    // Test the API to make sure it returns the updated values
    console.log('\n🌐 Testing API with updated values...');
    try {
      const response = await fetch('http://localhost:3000/api/settings?business=true');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          console.log('✅ Updated Business Settings from API:');
          console.log(`   wastage_rate_bar: ${data.data.wastage_rate_bar}%`);
          console.log(`   wastage_rate_jewel: ${data.data.wastage_rate_jewel}%`);
          console.log(`   wastage_rate_old_jewel: ${data.data.wastage_rate_old_jewel}%`);
          
          // Check if all values are now correct
          const expectedValues = {
            wastage_rate_bar: 0.5,
            wastage_rate_jewel: 2.0,
            wastage_rate_old_jewel: 3.0
          };
          
          let allCorrect = true;
          Object.keys(expectedValues).forEach(key => {
            if (data.data[key] !== expectedValues[key]) {
              console.log(`❌ ${key} is ${data.data[key]}%, expected ${expectedValues[key]}%`);
              allCorrect = false;
            }
          });
          
          if (allCorrect) {
            console.log('✅ All wastage rates are now correct!');
          }
        } else {
          console.log('❌ API Error:', data.error);
        }
      } else {
        console.log('❌ API Request failed:', response.status);
      }
    } catch (error) {
      console.log('❌ API Request error:', error.message);
    }

    console.log('\n=== WASTAGE RATES FIXED ===');
    console.log('✅ Default wastage rates have been updated to proper values');
    console.log('✅ Bar: 0.5%, Jewel: 2.0%, Old Jewel: 3.0%');
    console.log('✅ The inventory page should now show the correct wastage rates');
    console.log('\n🔄 Next steps:');
    console.log('   1. Refresh the inventory page in your browser');
    console.log('   2. Create a new inventory item to see the updated wastage rates');
    console.log('   3. The wastage percentage should now auto-populate with correct values');

  } catch (error) {
    console.error('❌ Error fixing wastage rates:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the fix
fixWastageRates();
