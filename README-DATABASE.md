# MySQL Database Setup for Jewellery Wholesale Software

This guide will help you set up the MySQL database for the Jewellery Wholesale Management System.

## Prerequisites

- MySQL Server 8.0 or higher
- Node.js 18 or higher
- npm or yarn package manager

## Database Setup

### 1. Install MySQL

**On Ubuntu/Debian:**
\`\`\`bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
\`\`\`

**On macOS (using Homebrew):**
\`\`\`bash
brew install mysql
brew services start mysql
\`\`\`

**On Windows:**
Download and install MySQL from the official website: https://dev.mysql.com/downloads/mysql/

### 2. Create Database User (Optional but Recommended)

\`\`\`sql
-- Connect to MySQL as root
mysql -u root -p

-- Create a dedicated user for the application
CREATE USER 'jewellery_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON jewellery_wholesale.* TO 'jewellery_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
\`\`\`

### 3. Environment Configuration

1. Copy the example environment file:
\`\`\`bash
cp .env.example .env.local
\`\`\`

2. Update the database configuration in `.env.local`:
\`\`\`env
DB_HOST=localhost
DB_PORT=3306
DB_USER=jewellery_user
DB_PASSWORD=secure_password_here
DB_NAME=jewellery_wholesale
\`\`\`

### 4. Install Dependencies

\`\`\`bash
npm install
# or
yarn install
\`\`\`

### 5. Create Database Schema

Run the schema creation script:
\`\`\`bash
mysql -u jewellery_user -p < scripts/mysql-schema.sql
\`\`\`

Or use the npm script:
\`\`\`bash
npm run db:setup
\`\`\`

### 6. Seed Sample Data

Load sample data into the database:
\`\`\`bash
mysql -u jewellery_user -p < scripts/mysql-seed-data.sql
\`\`\`

Or use the npm script:
\`\`\`bash
npm run db:seed
\`\`\`

### 7. Test Database Connection

Start the development server and test the database connection:
\`\`\`bash
npm run dev
\`\`\`

Visit: http://localhost:3000/api/test-db

You should see a success message if the database is connected properly.

## Database Schema Overview

### Tables

1. **suppliers** - Jewelry suppliers information
2. **customers** - Customer details and purchase history
3. **inventory** - Product inventory with supplier relationships
4. **bills** - Sales transactions and billing information
5. **gold_rates** - Historical gold rate tracking
6. **users** - System users and permissions

### Key Features

- **Foreign Key Constraints** - Maintains data integrity
- **Indexes** - Optimized for common queries
- **JSON Support** - User permissions stored as JSON
- **Automatic Timestamps** - Created/updated tracking
- **Enum Types** - Status fields with predefined values

## API Endpoints

### Suppliers
- `GET /api/suppliers` - List all suppliers
- `POST /api/suppliers` - Create new supplier
- `GET /api/suppliers/[id]` - Get supplier by ID
- `PUT /api/suppliers/[id]` - Update supplier
- `DELETE /api/suppliers/[id]` - Delete supplier

### Customers
- `GET /api/customers` - List all customers
- `POST /api/customers` - Create new customer

### Inventory
- `GET /api/inventory` - List inventory items
- `POST /api/inventory` - Add inventory item

### Bills
- `GET /api/bills` - List all bills
- `POST /api/bills` - Create new bill

### Gold Rates
- `GET /api/gold-rates` - Get gold rates
- `POST /api/gold-rates` - Update gold rates

## Database Maintenance

### Backup Database
\`\`\`bash
mysqldump -u jewellery_user -p jewellery_wholesale > backup_$(date +%Y%m%d).sql
\`\`\`

### Restore Database
\`\`\`bash
mysql -u jewellery_user -p jewellery_wholesale < backup_file.sql
\`\`\`

### Reset Database
\`\`\`bash
npm run db:reset
\`\`\`

## Troubleshooting

### Connection Issues

1. **Check MySQL Service:**
\`\`\`bash
sudo systemctl status mysql
# or on macOS
brew services list | grep mysql
\`\`\`

2. **Verify User Permissions:**
\`\`\`sql
SHOW GRANTS FOR 'jewellery_user'@'localhost';
\`\`\`

3. **Check Database Exists:**
\`\`\`sql
SHOW DATABASES;
USE jewellery_wholesale;
SHOW TABLES;
\`\`\`

### Performance Optimization

1. **Monitor Slow Queries:**
\`\`\`sql
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
\`\`\`

2. **Check Index Usage:**
\`\`\`sql
EXPLAIN SELECT * FROM suppliers WHERE name LIKE '%search%';
\`\`\`

3. **Optimize Tables:**
\`\`\`sql
OPTIMIZE TABLE suppliers, customers, inventory, bills;
\`\`\`

## Security Considerations

1. **Use Strong Passwords** - For database users
2. **Limit User Privileges** - Grant only necessary permissions
3. **Enable SSL** - For production environments
4. **Regular Backups** - Automated backup strategy
5. **Update MySQL** - Keep server updated

## Production Deployment

### Environment Variables
\`\`\`env
DB_HOST=your-production-host
DB_PORT=3306
DB_USER=production_user
DB_PASSWORD=strong_production_password
DB_NAME=jewellery_wholesale
DB_SSL=true
\`\`\`

### Connection Pooling
The application uses connection pooling with these settings:
- **Connection Limit:** 10
- **Acquire Timeout:** 60 seconds
- **Reconnect:** Enabled

### Monitoring
Monitor these metrics in production:
- Connection pool usage
- Query execution time
- Database size growth
- Index efficiency

For more detailed information, refer to the MySQL documentation: https://dev.mysql.com/doc/
