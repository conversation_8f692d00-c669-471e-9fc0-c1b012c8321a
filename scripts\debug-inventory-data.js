async function debugInventoryData() {
  console.log('🔍 DEBUGGING INVENTORY DATA DISPLAY ISSUES');
  console.log('==========================================\n');

  // Test 1: Check inventory API response
  console.log('📋 TEST 1: INVENTORY API RESPONSE');
  try {
    const response = await fetch('http://localhost:3000/api/inventory');
    if (response.ok) {
      const data = await response.json();
      if (data.success && data.data.length > 0) {
        console.log(`✅ Found ${data.data.length} inventory items`);
        
        const firstItem = data.data[0];
        console.log('\n📊 FIRST ITEM STRUCTURE:');
        console.log('========================');
        Object.keys(firstItem).forEach(key => {
          const value = firstItem[key];
          const type = typeof value;
          console.log(`   ${key}: ${value} (${type})`);
        });

        console.log('\n🔍 KEY FIELD ANALYSIS:');
        console.log('======================');
        console.log(`   product_name: "${firstItem.product_name}"`);
        console.log(`   product_type: "${firstItem.product_type}"`);
        console.log(`   with_stone_weight: ${firstItem.with_stone_weight}`);
        console.log(`   without_stone_weight: ${firstItem.without_stone_weight}`);
        console.log(`   with_stone_cost: ${firstItem.with_stone_cost}`);
        console.log(`   without_stone_cost: ${firstItem.without_stone_cost}`);
        console.log(`   procured_in_24k: ${firstItem.procured_in_24k}`);
        console.log(`   balance_weight_24k: ${firstItem.balance_weight_24k}`);
        console.log(`   balance_weight_22k: ${firstItem.balance_weight_22k}`);

        console.log('\n📋 EXPECTED VS ACTUAL:');
        console.log('======================');
        console.log('From your CSV data:');
        console.log('   Product Name: Chain');
        console.log('   With Stone: 0');
        console.log('   Without Stone: 120.420');
        console.log('   With Stone Cost: 93');
        console.log('   Without Stone Cost: 94');
        console.log('   Procured 24K: 113.195');
        
        console.log('\nFrom API response:');
        console.log(`   Product Name: ${firstItem.product_name}`);
        console.log(`   With Stone: ${firstItem.with_stone_weight}`);
        console.log(`   Without Stone: ${firstItem.without_stone_weight}`);
        console.log(`   With Stone Cost: ${firstItem.with_stone_cost}`);
        console.log(`   Without Stone Cost: ${firstItem.without_stone_cost}`);
        console.log(`   Procured 24K: ${firstItem.procured_in_24k}`);

        // Check for data type issues
        console.log('\n⚠️  POTENTIAL ISSUES:');
        console.log('====================');
        
        if (firstItem.product_name !== 'Chain') {
          console.log(`❌ Product name mismatch: Expected "Chain", got "${firstItem.product_name}"`);
        }
        
        if (Number(firstItem.without_stone_weight) !== 120.420) {
          console.log(`❌ Without stone weight mismatch: Expected 120.420, got ${firstItem.without_stone_weight}`);
        }
        
        if (Number(firstItem.procured_in_24k) !== 113.195) {
          console.log(`❌ Procured 24K mismatch: Expected 113.195, got ${firstItem.procured_in_24k}`);
        }

        // Check if values are being displayed as percentages incorrectly
        console.log('\n🔍 COST DISPLAY ANALYSIS:');
        console.log('=========================');
        console.log('Interface shows: "With Stone: 0.00%, Without: 94.00%"');
        console.log(`API returns: with_stone_cost=${firstItem.with_stone_cost}, without_stone_cost=${firstItem.without_stone_cost}`);
        
        if (firstItem.with_stone_cost > 100 || firstItem.without_stone_cost > 100) {
          console.log('⚠️  Cost values appear to be absolute amounts, not percentages');
          console.log('🔧 Interface should display as currency, not percentage');
        }

      } else {
        console.log('❌ No inventory data found');
      }
    } else {
      console.log(`❌ API request failed: HTTP ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ API test failed: ${error.message}`);
  }

  // Test 2: Check suppliers API
  console.log('\n📋 TEST 2: SUPPLIERS API');
  try {
    const response = await fetch('http://localhost:3000/api/suppliers');
    if (response.ok) {
      const data = await response.json();
      if (data.success && data.data.length > 0) {
        console.log(`✅ Found ${data.data.length} suppliers`);
        const firstSupplier = data.data[0];
        console.log(`   First supplier: ${firstSupplier.name} (${firstSupplier.location})`);
      }
    }
  } catch (error) {
    console.log(`❌ Suppliers test failed: ${error.message}`);
  }

  console.log('\n🎯 DIAGNOSIS SUMMARY');
  console.log('===================');
  
  console.log('\n🔍 LIKELY ISSUES:');
  console.log('1. Data type mismatches (string vs number)');
  console.log('2. Cost values displayed as percentages instead of currency');
  console.log('3. Weight calculations may be incorrect');
  console.log('4. Product name/type mapping issues');
  console.log('5. Balance calculations not reflecting correctly');

  console.log('\n🔧 FIXES NEEDED:');
  console.log('1. Fix cost display format (currency vs percentage)');
  console.log('2. Verify weight calculations and display');
  console.log('3. Check product name/type mapping');
  console.log('4. Ensure balance calculations are correct');
  console.log('5. Fix data type conversions');

  console.log('\n📋 NEXT STEPS:');
  console.log('1. Fix inventory component display logic');
  console.log('2. Update cost formatting');
  console.log('3. Verify calculation methods');
  console.log('4. Test with fresh data entry');
}

// Run the debug
debugInventoryData();
