const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function fixAvailableStockValues() {
  let connection;

  try {
    console.log('🔌 Connecting to database...');
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
    });

    console.log('✅ Connected to database');

    // Check current inventory items
    console.log('\n📊 Checking current inventory items...');
    const [items] = await connection.execute(`
      SELECT id, product_name, without_stone_weight, balance_weight_22k, balance_gold_weight_22k, sold_gold_weight_22k
      FROM inventory 
      ORDER BY id
    `);

    if (items.length === 0) {
      console.log('No inventory items found.');
      return;
    }

    console.log(`Found ${items.length} inventory items:`);
    console.log('\nCurrent values:');
    console.log('ID | Product | Without Stone | Balance 22K | Available Stock | Sold');
    console.log('---|---------|---------------|-------------|-----------------|------');
    
    items.forEach(item => {
      console.log(`${item.id.toString().padEnd(2)} | ${item.product_name.padEnd(7)} | ${Number(item.without_stone_weight).toFixed(3).padEnd(13)} | ${Number(item.balance_weight_22k).toFixed(3).padEnd(11)} | ${Number(item.balance_gold_weight_22k).toFixed(3).padEnd(15)} | ${Number(item.sold_gold_weight_22k).toFixed(3)}`);
    });

    // Fix available stock values
    console.log('\n🔧 Fixing available stock values...');
    console.log('Setting balance_gold_weight_22k = without_stone_weight - sold_gold_weight_22k');

    for (const item of items) {
      const correctAvailableStock = Number(item.without_stone_weight) - Number(item.sold_gold_weight_22k);
      
      if (Number(item.balance_gold_weight_22k) !== correctAvailableStock) {
        console.log(`\nFixing item ${item.id} (${item.product_name}):`);
        console.log(`  Current available stock: ${Number(item.balance_gold_weight_22k).toFixed(3)}`);
        console.log(`  Should be: ${item.without_stone_weight} - ${item.sold_gold_weight_22k} = ${correctAvailableStock.toFixed(3)}`);
        
        await connection.execute(`
          UPDATE inventory 
          SET balance_gold_weight_22k = ? 
          WHERE id = ?
        `, [correctAvailableStock, item.id]);
        
        console.log(`  ✅ Updated to: ${correctAvailableStock.toFixed(3)}`);
      } else {
        console.log(`✅ Item ${item.id} (${item.product_name}) already has correct available stock: ${Number(item.balance_gold_weight_22k).toFixed(3)}`);
      }
    }

    // Verify the fixes
    console.log('\n📊 Verification - Updated inventory items:');
    const [updatedItems] = await connection.execute(`
      SELECT id, product_name, without_stone_weight, balance_weight_22k, balance_gold_weight_22k, sold_gold_weight_22k
      FROM inventory 
      ORDER BY id
    `);

    console.log('\nUpdated values:');
    console.log('ID | Product | Without Stone | Balance 22K | Available Stock | Sold');
    console.log('---|---------|---------------|-------------|-----------------|------');
    
    updatedItems.forEach(item => {
      const expectedAvailable = Number(item.without_stone_weight) - Number(item.sold_gold_weight_22k);
      const actualAvailable = Number(item.balance_gold_weight_22k);
      const isCorrect = Math.abs(expectedAvailable - actualAvailable) < 0.001;
      const status = isCorrect ? '✅' : '❌';
      
      console.log(`${item.id.toString().padEnd(2)} | ${item.product_name.padEnd(7)} | ${Number(item.without_stone_weight).toFixed(3).padEnd(13)} | ${Number(item.balance_weight_22k).toFixed(3).padEnd(11)} | ${actualAvailable.toFixed(3).padEnd(15)} | ${Number(item.sold_gold_weight_22k).toFixed(3)} ${status}`);
    });

    console.log('\n🎉 Available stock values have been corrected!');
    console.log('\n📝 Summary:');
    console.log('• Available Stock = Without Stone Weight - Sold Gold Weight');
    console.log('• For new items: Available Stock = Without Stone Weight (no sales yet)');
    console.log('• For items with sales: Available Stock = Original Weight - Sold Amount');

  } catch (error) {
    console.error('❌ Error fixing available stock values:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the fix
fixAvailableStockValues();
