const mysql = require('mysql2/promise');
require('dotenv').config();

async function updateJewelleryWholesaleSoftwareDB() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME
    });

    console.log('Connected to jewellery_wholesale_software database');

    // Drop and recreate inventory table with correct schema
    console.log('Updating inventory table schema...');

    // Disable foreign key checks temporarily
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    await connection.execute('DROP TABLE IF EXISTS inventory');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');

    await connection.execute(`
      CREATE TABLE inventory (
          id INT AUTO_INCREMENT PRIMARY KEY,
          supplier_id INT,
          product_name VARCHAR(255) NOT NULL,
          product_type VARCHAR(255),
          metal_type ENUM('Gold', 'Silver', 'Platinum') DEFAULT 'Gold',
          form_type ENUM('Bar', 'Jewel', 'Old Jewel') DEFAULT 'Jewel',
          jewel_type ENUM('With Stone', 'Without Stone') NULL,
          jewel_category VARCHAR(100) NULL,
          with_stone_weight DECIMAL(10,3) DEFAULT 0,
          without_stone_weight DECIMAL(10,3) DEFAULT 0,
          stone_weight DECIMAL(10,3) DEFAULT 0,
          with_stone_cost DECIMAL(5,2) DEFAULT 0,
          without_stone_cost DECIMAL(5,2) DEFAULT 0,
          procured_in_24k DECIMAL(10,3) DEFAULT 0,
          sold_value_with_stone DECIMAL(10,3) DEFAULT 0,
          sold_value_without_stone DECIMAL(10,3) DEFAULT 0,
          balance_weight_24k DECIMAL(10,3) DEFAULT 0,
          balance_weight_22k DECIMAL(10,3) DEFAULT 0,
          status ENUM('Available', 'Low Stock', 'Out of Stock') DEFAULT 'Available',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_inventory_supplier_id (supplier_id),
          INDEX idx_inventory_product_name (product_name),
          INDEX idx_inventory_status (status),
          INDEX idx_inventory_metal_type (metal_type),
          INDEX idx_inventory_form_type (form_type)
      )
    `);
    console.log('✅ Inventory table updated successfully');

    // Check and create suppliers table if needed
    const [supplierTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'jewellery_wholesale_software' AND TABLE_NAME = 'suppliers'
    `);

    if (supplierTables.length === 0) {
      console.log('Creating suppliers table...');
      await connection.execute(`
        CREATE TABLE suppliers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            location VARCHAR(255),
            contact_person VARCHAR(255),
            phone VARCHAR(20),
            email VARCHAR(255),
            address TEXT,
            speciality VARCHAR(255),
            total_purchases DECIMAL(15,2) DEFAULT 0,
            last_purchase_date DATE,
            status ENUM('Active', 'Inactive') DEFAULT 'Active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_suppliers_name (name),
            INDEX idx_suppliers_status (status)
        )
      `);
      console.log('✅ Suppliers table created');
    }

    // Add foreign key constraint
    try {
      await connection.execute(`
        ALTER TABLE inventory 
        ADD CONSTRAINT fk_inventory_supplier 
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL
      `);
      console.log('✅ Foreign key constraint added');
    } catch (error) {
      if (error.code !== 'ER_DUP_KEYNAME') {
        console.log('Note: Foreign key constraint may already exist');
      }
    }

    // Check and create bills table if needed
    const [billTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'jewellery_wholesale_software' AND TABLE_NAME = 'bills'
    `);

    if (billTables.length === 0) {
      console.log('Creating bills table...');
      await connection.execute(`
        CREATE TABLE bills (
            id INT AUTO_INCREMENT PRIMARY KEY,
            customer_id INT,
            bill_number VARCHAR(50) UNIQUE,
            product_name VARCHAR(255),
            product_type VARCHAR(255),
            with_stone DECIMAL(10,3) DEFAULT 0,
            without_stone DECIMAL(10,3) DEFAULT 0,
            gross_weight DECIMAL(10,3) DEFAULT 0,
            stone_weight DECIMAL(10,3) DEFAULT 0,
            net_weight DECIMAL(10,3) DEFAULT 0,
            tunch_with_stone INT DEFAULT 0,
            tunch_without_stone INT DEFAULT 0,
            weight_in_24k DECIMAL(10,3) DEFAULT 0,
            gold_24k_price DECIMAL(10,2) DEFAULT 0,
            stone_price DECIMAL(10,2) DEFAULT 0,
            making_charges DECIMAL(10,2) DEFAULT 0,
            total_amount DECIMAL(15,2) DEFAULT 0,
            status ENUM('Pending', 'Completed', 'Cancelled') DEFAULT 'Pending',
            bill_date DATE DEFAULT (CURRENT_DATE),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_bills_bill_date (bill_date),
            INDEX idx_bills_status (status)
        )
      `);
      console.log('✅ Bills table created');
    }

    // Check and create customers table if needed
    const [customerTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'jewellery_wholesale_software' AND TABLE_NAME = 'customers'
    `);

    if (customerTables.length === 0) {
      console.log('Creating customers table...');
      await connection.execute(`
        CREATE TABLE customers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            location VARCHAR(255),
            contact_person VARCHAR(255),
            phone VARCHAR(20),
            email VARCHAR(255),
            address TEXT,
            total_purchases DECIMAL(15,2) DEFAULT 0,
            last_purchase_date DATE,
            status ENUM('Active', 'Inactive') DEFAULT 'Active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_customers_name (name),
            INDEX idx_customers_status (status)
        )
      `);
      console.log('✅ Customers table created');
    }

    // Check and create gold_rates table if needed
    const [goldRatesTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'jewellery_wholesale_software' AND TABLE_NAME = 'gold_rates'
    `);

    if (goldRatesTables.length === 0) {
      console.log('Creating gold_rates table...');
      await connection.execute(`
        CREATE TABLE gold_rates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            rate_24k DECIMAL(10,2) NOT NULL,
            rate_22k DECIMAL(10,2) NOT NULL,
            rate_18k DECIMAL(10,2) NOT NULL,
            rate_date DATE DEFAULT (CURRENT_DATE),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_gold_rates_date (rate_date)
        )
      `);
      console.log('✅ Gold rates table created');

      // Add sample gold rates
      const today = new Date().toISOString().split('T')[0];
      await connection.execute(`
        INSERT INTO gold_rates (rate_24k, rate_22k, rate_18k, rate_date)
        VALUES (?, ?, ?, ?)
      `, [7200.00, 6595.00, 5400.00, today]);
      console.log('✅ Sample gold rates added');
    }

    console.log('\n🎉 Database jewellery_wholesale_software updated successfully!');

  } catch (error) {
    console.error('Error updating database:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
updateJewelleryWholesaleSoftwareDB();
