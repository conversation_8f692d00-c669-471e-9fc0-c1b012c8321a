async function checkDatabaseValues() {
  console.log('🔍 CHECKING DATABASE VALUES');
  console.log('===========================\n');

  try {
    const response = await fetch('http://localhost:3000/api/inventory');
    if (response.ok) {
      const data = await response.json();
      if (data.success && data.data.length > 0) {
        const item = data.data[0]; // Get the first item (your Chain)
        
        console.log('📊 DATABASE VALUES FOR CHAIN ITEM:');
        console.log('==================================');
        console.log(`Product Name: "${item.product_name}"`);
        console.log(`Product Type: "${item.product_type}"`);
        console.log('');
        console.log('WEIGHT VALUES:');
        console.log(`procured_in_24k: ${item.procured_in_24k} (${typeof item.procured_in_24k})`);
        console.log(`balance_weight_24k: ${item.balance_weight_24k} (${typeof item.balance_weight_24k})`);
        console.log(`balance_weight_22k: ${item.balance_weight_22k} (${typeof item.balance_weight_22k})`);
        console.log(`with_stone_weight: ${item.with_stone_weight} (${typeof item.with_stone_weight})`);
        console.log(`without_stone_weight: ${item.without_stone_weight} (${typeof item.without_stone_weight})`);
        
        console.log('\nCOST VALUES:');
        console.log(`with_stone_cost: ${item.with_stone_cost} (${typeof item.with_stone_cost})`);
        console.log(`without_stone_cost: ${item.without_stone_cost} (${typeof item.without_stone_cost})`);
        
        console.log('\n🔍 ANALYSIS:');
        console.log('============');
        
        if (item.balance_weight_24k === null || item.balance_weight_24k === 0 || item.balance_weight_24k === "0.000") {
          console.log('❌ ISSUE: balance_weight_24k is null/zero');
          console.log('   Should be: 9.754');
          console.log('   Currently: ' + item.balance_weight_24k);
        } else {
          console.log('✅ balance_weight_24k has value: ' + item.balance_weight_24k);
        }
        
        if (item.balance_weight_22k === null || item.balance_weight_22k === 0 || item.balance_weight_22k === "0.000") {
          console.log('❌ ISSUE: balance_weight_22k is null/zero');
          console.log('   Should be: 110.260');
          console.log('   Currently: ' + item.balance_weight_22k);
        } else {
          console.log('✅ balance_weight_22k has value: ' + item.balance_weight_22k);
        }
        
        console.log('\n📊 EXPECTED VS ACTUAL:');
        console.log('======================');
        console.log('Expected from your data:');
        console.log('- procured_in_24k: 113.195');
        console.log('- balance_weight_24k: 9.754');
        console.log('- balance_weight_22k: 110.260');
        console.log('- without_stone_weight: 120.420');
        console.log('- without_stone_cost: 94');
        
        console.log('\nActual in database:');
        console.log(`- procured_in_24k: ${item.procured_in_24k}`);
        console.log(`- balance_weight_24k: ${item.balance_weight_24k}`);
        console.log(`- balance_weight_22k: ${item.balance_weight_22k}`);
        console.log(`- without_stone_weight: ${item.without_stone_weight}`);
        console.log(`- without_stone_cost: ${item.without_stone_cost}`);
        
        console.log('\n🎯 DISPLAY LOGIC:');
        console.log('=================');
        console.log('The app shows:');
        console.log(`- 24K: ${Number(item.balance_weight_24k || 0).toFixed(3)}g`);
        console.log(`- 22K: ${Number(item.balance_weight_22k || 0).toFixed(3)}g`);
        console.log(`- Procured: ${Number(item.procured_in_24k || 0).toFixed(3)}g`);
        
        if (Number(item.balance_weight_24k || 0) === 0) {
          console.log('\n🚨 ROOT CAUSE FOUND:');
          console.log('====================');
          console.log('balance_weight_24k is 0 or null in database');
          console.log('This means the balance weight fields were not filled');
          console.log('when adding the inventory item.');
          console.log('');
          console.log('SOLUTION: When adding new item, make sure to fill:');
          console.log('- Balance Weight 24K: 9.754');
          console.log('- Balance Weight 22K: 110.260');
        }
        
      } else {
        console.log('❌ No inventory data found');
      }
    } else {
      console.log('❌ Failed to fetch inventory data');
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
  
  console.log('\n📝 NEXT STEPS:');
  console.log('==============');
  console.log('1. Check if balance weight fields are being filled in form');
  console.log('2. Verify form submission includes balance weights');
  console.log('3. Test adding new item with correct balance weights');
  console.log('4. Update existing item if balance weights are missing');
}

checkDatabaseValues();
