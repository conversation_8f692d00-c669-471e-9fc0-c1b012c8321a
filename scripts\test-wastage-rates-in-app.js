const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function testWastageRatesInApp() {
  console.log('🧪 TESTING WASTAGE RATES IN APPLICATION\n');

  // Test 1: Database values
  console.log('📊 Test 1: Database Values');
  let connection;
  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME
  };

  try {
    connection = await mysql.createConnection(config);
    const [settings] = await connection.execute(`
      SELECT setting_key, setting_value 
      FROM settings 
      WHERE category = 'wastage'
      ORDER BY setting_key
    `);

    settings.forEach(setting => {
      let value;
      try {
        value = typeof setting.setting_value === 'string' 
          ? JSON.parse(setting.setting_value) 
          : setting.setting_value;
      } catch (error) {
        value = setting.setting_value;
      }
      console.log(`   ✅ ${setting.setting_key}: ${value}%`);
    });
  } catch (error) {
    console.log('   ❌ Database error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }

  // Test 2: API Response
  console.log('\n🌐 Test 2: API Response');
  try {
    const response = await fetch('http://localhost:3000/api/settings?business=true');
    if (response.ok) {
      const data = await response.json();
      if (data.success) {
        console.log(`   ✅ wastage_rate_bar: ${data.data.wastage_rate_bar}%`);
        console.log(`   ✅ wastage_rate_jewel: ${data.data.wastage_rate_jewel}%`);
        console.log(`   ✅ wastage_rate_old_jewel: ${data.data.wastage_rate_old_jewel}%`);
      } else {
        console.log('   ❌ API Error:', data.error);
      }
    } else {
      console.log('   ❌ API Request failed:', response.status);
    }
  } catch (error) {
    console.log('   ❌ API Request error:', error.message);
  }

  // Test 3: Individual setting values
  console.log('\n🔍 Test 3: Individual Setting Values');
  const formTypes = ['Bar', 'Jewel', 'Old Jewel'];
  
  for (const formType of formTypes) {
    try {
      const key = `wastage_rate_${formType.toLowerCase().replace(' ', '_')}`;
      const response = await fetch(`http://localhost:3000/api/settings?category=wastage&key=${key}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          console.log(`   ✅ ${formType}: ${data.data.value}%`);
        } else {
          console.log(`   ❌ ${formType}: API Error - ${data.error}`);
        }
      } else {
        console.log(`   ❌ ${formType}: Request failed - ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ ${formType}: Request error - ${error.message}`);
    }
  }

  console.log('\n=== TEST RESULTS ===');
  console.log('✅ If all tests show correct values, the wastage rates are working');
  console.log('✅ Expected values: Bar: 0.5%, Jewel: 2%, Old Jewel: 3%');
  console.log('\n🔄 To test in the application:');
  console.log('   1. Go to http://localhost:3000');
  console.log('   2. Navigate to Inventory Management');
  console.log('   3. Click "Add New Item"');
  console.log('   4. Select different Form Types (Bar, Jewel, Old Jewel)');
  console.log('   5. Check that the "Expected Wastage (%)" field shows the correct values');
  console.log('   6. The helper text should show: "auto-set from settings: X%"');
  
  console.log('\n📝 Expected behavior:');
  console.log('   - Bar: Should show 0.5% wastage');
  console.log('   - Jewel: Should show 2% wastage');
  console.log('   - Old Jewel: Should show 3% wastage');
  
  console.log('\n🐛 If still showing wrong values:');
  console.log('   1. Hard refresh the browser (Ctrl+F5)');
  console.log('   2. Clear browser cache');
  console.log('   3. Check browser console for any errors');
  console.log('   4. Verify the component is using the correct hook import');
}

// Run the test
testWastageRatesInApp();
