const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function validateAndFixAllSettings() {
  let connection;

  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME
  };

  try {
    console.log('🔧 COMPREHENSIVE SETTINGS VALIDATION & FIX');
    console.log('==========================================\n');

    connection = await mysql.createConnection(config);
    console.log(`📊 Connected to database: ${process.env.DB_NAME}`);

    // =====================================================
    // PHASE 1: VALIDATE EXISTING SETTINGS
    // =====================================================
    
    console.log('\n📋 PHASE 1: VALIDATING EXISTING SETTINGS');
    
    const [currentSettings] = await connection.execute(`
      SELECT category, setting_key, setting_value, data_type, is_user_configurable
      FROM settings 
      ORDER BY category, setting_key
    `);

    console.log(`Found ${currentSettings.length} settings in database`);

    const issues = [];
    const fixes = [];

    // Validate each setting
    currentSettings.forEach(setting => {
      let value;
      try {
        value = typeof setting.setting_value === 'string' 
          ? JSON.parse(setting.setting_value) 
          : setting.setting_value;
      } catch (error) {
        value = setting.setting_value;
      }

      // Check for invalid values
      if (setting.category === 'wastage' && (value < 0 || value > 50)) {
        issues.push(`${setting.setting_key}: Invalid wastage rate ${value}%`);
        fixes.push({
          category: setting.category,
          key: setting.setting_key,
          currentValue: value,
          fixedValue: value < 0 ? 0 : (value > 50 ? 5 : value)
        });
      }

      if (setting.category === 'conversion' && (value <= 0 || value > 2)) {
        issues.push(`${setting.setting_key}: Invalid conversion factor ${value}`);
        fixes.push({
          category: setting.category,
          key: setting.setting_key,
          currentValue: value,
          fixedValue: setting.setting_key.includes('24k_to_22k') ? 0.916 : 
                     setting.setting_key.includes('24k_to_18k') ? 0.750 :
                     setting.setting_key.includes('22k_to_24k') ? 1.092 : 1.333
        });
      }

      if (setting.category === 'pricing' && setting.setting_key === 'gst_rate' && (value < 0 || value > 30)) {
        issues.push(`${setting.setting_key}: Invalid GST rate ${value}%`);
        fixes.push({
          category: setting.category,
          key: setting.setting_key,
          currentValue: value,
          fixedValue: 3.0
        });
      }
    });

    if (issues.length > 0) {
      console.log('\n❌ ISSUES FOUND:');
      issues.forEach(issue => console.log(`   - ${issue}`));
    } else {
      console.log('✅ All existing settings are valid');
    }

    // =====================================================
    // PHASE 2: APPLY FIXES
    // =====================================================
    
    if (fixes.length > 0) {
      console.log('\n🔧 PHASE 2: APPLYING FIXES');
      
      for (const fix of fixes) {
        try {
          await connection.execute(`
            UPDATE settings 
            SET setting_value = ?, updated_at = CURRENT_TIMESTAMP, updated_by = 'validation_fix'
            WHERE category = ? AND setting_key = ?
          `, [JSON.stringify(fix.fixedValue), fix.category, fix.key]);
          
          console.log(`✅ Fixed ${fix.key}: ${fix.currentValue} → ${fix.fixedValue}`);
        } catch (error) {
          console.log(`❌ Failed to fix ${fix.key}: ${error.message}`);
        }
      }
    }

    // =====================================================
    // PHASE 3: ADD MISSING SETTINGS
    // =====================================================
    
    console.log('\n📊 PHASE 3: CHECKING FOR MISSING SETTINGS');
    
    const requiredSettings = [
      // Conversion factors
      { category: 'conversion', key: 'conversion_24k_to_22k', value: 0.916, type: 'number', desc: '24K to 22K conversion factor' },
      { category: 'conversion', key: 'conversion_24k_to_18k', value: 0.750, type: 'number', desc: '24K to 18K conversion factor' },
      { category: 'conversion', key: 'conversion_22k_to_24k', value: 1.092, type: 'number', desc: '22K to 24K conversion factor' },
      { category: 'conversion', key: 'conversion_18k_to_24k', value: 1.333, type: 'number', desc: '18K to 24K conversion factor' },
      
      // Wastage rates
      { category: 'wastage', key: 'wastage_rate_bar', value: 0.5, type: 'number', desc: 'Wastage rate for bars (%)' },
      { category: 'wastage', key: 'wastage_rate_jewel', value: 2.0, type: 'number', desc: 'Wastage rate for jewellery (%)' },
      { category: 'wastage', key: 'wastage_rate_old_jewel', value: 3.0, type: 'number', desc: 'Wastage rate for old jewellery (%)' },
      
      // Business rules
      { category: 'business', key: 'auto_calculate_balances', value: true, type: 'boolean', desc: 'Auto-calculate inventory balances' },
      { category: 'business', key: 'track_stone_separately', value: true, type: 'boolean', desc: 'Track stone weight separately' },
      { category: 'business', key: 'enable_wastage_alerts', value: true, type: 'boolean', desc: 'Enable wastage alerts' },
      { category: 'business', key: 'low_stock_threshold', value: 10.0, type: 'number', desc: 'Low stock threshold (grams)' },
      
      // Pricing
      { category: 'pricing', key: 'default_making_charges', value: 500.0, type: 'number', desc: 'Default making charges per gram' },
      { category: 'pricing', key: 'stone_pricing_method', value: 'both', type: 'string', desc: 'Stone pricing method' },
      { category: 'pricing', key: 'apply_gst', value: true, type: 'boolean', desc: 'Apply GST to calculations' },
      { category: 'pricing', key: 'gst_rate', value: 3.0, type: 'number', desc: 'GST rate percentage' },
      
      // Calculation
      { category: 'calculation', key: 'rounding_precision', value: 3, type: 'number', desc: 'Decimal places for rounding' },
      { category: 'calculation', key: 'balance_calculation_method', value: 'simple', type: 'string', desc: 'Balance calculation method' },
      
      // Alerts
      { category: 'alerts', key: 'high_wastage_threshold', value: 5.0, type: 'number', desc: 'High wastage alert threshold (%)' },
      { category: 'alerts', key: 'price_variance_threshold', value: 10.0, type: 'number', desc: 'Price variance alert threshold (%)' }
    ];

    const existingKeys = currentSettings.map(s => `${s.category}.${s.setting_key}`);
    const missingSettings = requiredSettings.filter(s => !existingKeys.includes(`${s.category}.${s.key}`));

    if (missingSettings.length > 0) {
      console.log(`Found ${missingSettings.length} missing settings:`);
      
      for (const setting of missingSettings) {
        try {
          await connection.execute(`
            INSERT INTO settings (category, setting_key, setting_value, description, data_type, is_system, is_user_configurable, created_by)
            VALUES (?, ?, ?, ?, ?, FALSE, TRUE, 'validation_fix')
          `, [
            setting.category,
            setting.key,
            JSON.stringify(setting.value),
            setting.desc,
            setting.type
          ]);
          
          console.log(`✅ Added ${setting.category}.${setting.key}: ${setting.value}`);
        } catch (error) {
          console.log(`❌ Failed to add ${setting.category}.${setting.key}: ${error.message}`);
        }
      }
    } else {
      console.log('✅ All required settings are present');
    }

    // =====================================================
    // PHASE 4: VALIDATE API ENDPOINTS
    // =====================================================
    
    console.log('\n🌐 PHASE 4: VALIDATING API ENDPOINTS');
    
    const apiTests = [
      { name: 'Business Settings', url: 'http://localhost:3000/api/settings?business=true' },
      { name: 'Wastage Settings', url: 'http://localhost:3000/api/settings?category=wastage' },
      { name: 'Conversion Settings', url: 'http://localhost:3000/api/settings?category=conversion' },
      { name: 'Pricing Settings', url: 'http://localhost:3000/api/settings?category=pricing' }
    ];

    for (const test of apiTests) {
      try {
        const response = await fetch(test.url);
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            console.log(`✅ ${test.name}: API working`);
          } else {
            console.log(`❌ ${test.name}: API error - ${data.error}`);
          }
        } else {
          console.log(`❌ ${test.name}: HTTP ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ ${test.name}: Request failed - ${error.message}`);
      }
    }

    // =====================================================
    // PHASE 5: FINAL VALIDATION
    // =====================================================
    
    console.log('\n✅ PHASE 5: FINAL VALIDATION');
    
    const [finalSettings] = await connection.execute(`
      SELECT category, COUNT(*) as count
      FROM settings 
      GROUP BY category
      ORDER BY category
    `);

    console.log('Settings by category:');
    finalSettings.forEach(cat => {
      console.log(`   ${cat.category}: ${cat.count} settings`);
    });

    console.log('\n🎉 COMPREHENSIVE SETTINGS VALIDATION COMPLETE');
    console.log('============================================');
    console.log('✅ All settings validated and fixed');
    console.log('✅ Missing settings added');
    console.log('✅ API endpoints tested');
    console.log('✅ Database is ready for robust operation');
    
    console.log('\n📋 NEXT STEPS:');
    console.log('1. Restart the Next.js application');
    console.log('2. Test the Business Settings page');
    console.log('3. Verify inventory forms show correct wastage rates');
    console.log('4. Check that all calculations use database settings');

  } catch (error) {
    console.error('❌ Error during validation:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the comprehensive validation
validateAndFixAllSettings();
