const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function createSettingsTable() {
  let connection;

  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME
  };

  try {
    console.log('🔌 Connecting to database...');
    connection = await mysql.createConnection(config);
    console.log(`📊 Connected to database: ${process.env.DB_NAME}`);

    console.log('\n=== CREATING SETTINGS SYSTEM ===\n');

    // Create settings table
    console.log('⚙️  Creating settings table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category VARCHAR(50) NOT NULL,
        setting_key VARCHAR(100) NOT NULL,
        setting_value JSON NOT NULL,
        description TEXT,
        data_type ENUM('string', 'number', 'boolean', 'object', 'array') DEFAULT 'string',
        is_system BOOLEAN DEFAULT FALSE,
        is_user_configurable BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by VARCHAR(100),
        updated_by VARCHAR(100),
        
        -- Indexes for performance
        UNIQUE KEY unique_category_key (category, setting_key),
        INDEX idx_settings_category (category),
        INDEX idx_settings_key (setting_key),
        INDEX idx_settings_system (is_system),
        INDEX idx_settings_configurable (is_user_configurable)
      )
    `);
    console.log('✅ Settings table created');

    // Create user_settings table for user-specific overrides
    console.log('👤 Creating user_settings table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        category VARCHAR(50) NOT NULL,
        setting_key VARCHAR(100) NOT NULL,
        setting_value JSON NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        -- Foreign key to users table
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        
        -- Indexes for performance
        UNIQUE KEY unique_user_category_key (user_id, category, setting_key),
        INDEX idx_user_settings_user_id (user_id),
        INDEX idx_user_settings_category (category),
        INDEX idx_user_settings_key (setting_key)
      )
    `);
    console.log('✅ User settings table created');

    // Insert default business settings
    console.log('📊 Inserting default business settings...');
    
    const defaultSettings = [
      // Conversion Factors
      {
        category: 'conversion',
        key: 'conversion_24k_to_22k',
        value: 0.916,
        description: '24K to 22K gold conversion factor',
        type: 'number'
      },
      {
        category: 'conversion',
        key: 'conversion_24k_to_18k',
        value: 0.750,
        description: '24K to 18K gold conversion factor',
        type: 'number'
      },
      {
        category: 'conversion',
        key: 'conversion_22k_to_24k',
        value: 1.092,
        description: '22K to 24K gold conversion factor',
        type: 'number'
      },
      {
        category: 'conversion',
        key: 'conversion_18k_to_24k',
        value: 1.333,
        description: '18K to 24K gold conversion factor',
        type: 'number'
      },
      
      // Wastage Rates
      {
        category: 'wastage',
        key: 'wastage_rate_bar',
        value: 0.5,
        description: 'Default wastage rate for gold bars (%)',
        type: 'number'
      },
      {
        category: 'wastage',
        key: 'wastage_rate_jewel',
        value: 2.0,
        description: 'Default wastage rate for jewellery (%)',
        type: 'number'
      },
      {
        category: 'wastage',
        key: 'wastage_rate_old_jewel',
        value: 3.0,
        description: 'Default wastage rate for old jewellery (%)',
        type: 'number'
      },
      
      // Business Rules
      {
        category: 'business',
        key: 'auto_calculate_balances',
        value: true,
        description: 'Automatically calculate inventory balances',
        type: 'boolean'
      },
      {
        category: 'business',
        key: 'track_stone_separately',
        value: true,
        description: 'Track stone weight separately from metal',
        type: 'boolean'
      },
      {
        category: 'business',
        key: 'enable_wastage_alerts',
        value: true,
        description: 'Enable alerts for high wastage',
        type: 'boolean'
      },
      {
        category: 'business',
        key: 'low_stock_threshold',
        value: 10.0,
        description: 'Low stock threshold (grams)',
        type: 'number'
      },
      
      // Pricing Rules
      {
        category: 'pricing',
        key: 'default_making_charges',
        value: 500.0,
        description: 'Default making charges per gram (₹)',
        type: 'number'
      },
      {
        category: 'pricing',
        key: 'stone_pricing_method',
        value: 'both',
        description: 'Stone pricing method (weight/value/both)',
        type: 'string'
      },
      {
        category: 'pricing',
        key: 'apply_gst',
        value: true,
        description: 'Apply GST to calculations',
        type: 'boolean'
      },
      {
        category: 'pricing',
        key: 'gst_rate',
        value: 3.0,
        description: 'GST rate percentage',
        type: 'number'
      },
      
      // Calculation Methods
      {
        category: 'calculation',
        key: 'rounding_precision',
        value: 3,
        description: 'Decimal places for rounding',
        type: 'number'
      },
      {
        category: 'calculation',
        key: 'balance_calculation_method',
        value: 'simple',
        description: 'Balance calculation method (simple/weighted_average/fifo)',
        type: 'string'
      },
      
      // Alert Thresholds
      {
        category: 'alerts',
        key: 'high_wastage_threshold',
        value: 5.0,
        description: 'High wastage alert threshold (%)',
        type: 'number'
      },
      {
        category: 'alerts',
        key: 'price_variance_threshold',
        value: 10.0,
        description: 'Price variance alert threshold (%)',
        type: 'number'
      }
    ];

    // Insert settings with proper error handling
    for (const setting of defaultSettings) {
      try {
        await connection.execute(`
          INSERT INTO settings (category, setting_key, setting_value, description, data_type, is_system, is_user_configurable, created_by)
          VALUES (?, ?, ?, ?, ?, FALSE, TRUE, 'system')
          ON DUPLICATE KEY UPDATE
          description = VALUES(description),
          data_type = VALUES(data_type),
          updated_at = CURRENT_TIMESTAMP,
          updated_by = 'system'
        `, [
          setting.category,
          setting.key,
          JSON.stringify(setting.value),
          setting.description,
          setting.type
        ]);
        console.log(`   ✅ ${setting.category}.${setting.key}`);
      } catch (error) {
        console.log(`   ❌ Failed to insert ${setting.category}.${setting.key}: ${error.message}`);
      }
    }

    // Insert system settings
    console.log('\n🔧 Inserting system settings...');
    const systemSettings = [
      {
        category: 'system',
        key: 'app_version',
        value: '1.0.0',
        description: 'Application version',
        type: 'string',
        system: true,
        configurable: false
      },
      {
        category: 'system',
        key: 'database_version',
        value: '1.0.0',
        description: 'Database schema version',
        type: 'string',
        system: true,
        configurable: false
      },
      {
        category: 'system',
        key: 'last_backup_date',
        value: null,
        description: 'Last database backup date',
        type: 'string',
        system: true,
        configurable: false
      }
    ];

    for (const setting of systemSettings) {
      try {
        await connection.execute(`
          INSERT INTO settings (category, setting_key, setting_value, description, data_type, is_system, is_user_configurable, created_by)
          VALUES (?, ?, ?, ?, ?, ?, ?, 'system')
          ON DUPLICATE KEY UPDATE
          description = VALUES(description),
          data_type = VALUES(data_type),
          updated_at = CURRENT_TIMESTAMP,
          updated_by = 'system'
        `, [
          setting.category,
          setting.key,
          JSON.stringify(setting.value),
          setting.description,
          setting.type,
          setting.system,
          setting.configurable
        ]);
        console.log(`   ✅ ${setting.category}.${setting.key}`);
      } catch (error) {
        console.log(`   ❌ Failed to insert ${setting.category}.${setting.key}: ${error.message}`);
      }
    }

    console.log('\n=== SETTINGS SYSTEM CREATED SUCCESSFULLY ===');
    console.log('✅ Settings table created with default business settings');
    console.log('✅ User settings table created for user-specific overrides');
    console.log('✅ System settings inserted');
    console.log('\n📊 Settings Categories:');
    console.log('   - conversion: Gold purity conversion factors');
    console.log('   - wastage: Default wastage rates');
    console.log('   - business: Business rules and logic');
    console.log('   - pricing: Pricing and tax settings');
    console.log('   - calculation: Calculation methods');
    console.log('   - alerts: Alert thresholds');
    console.log('   - system: System configuration');

  } catch (error) {
    console.error('❌ Error creating settings system:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the settings table creation
createSettingsTable();
