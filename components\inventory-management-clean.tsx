"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Plus, Search, Edit, Trash2, Package, TrendingUp, IndianRupee } from "lucide-react"
import { useDatabase } from "@/hooks/use-database"
import { useDatabaseMutation } from "@/hooks/use-database-mutation"
import { useSimpleToast } from "@/hooks/use-simple-toast"

// Clean interface matching your exact data structure
interface InventoryItem {
  id: number
  supplier_id: number
  supplier_name?: string
  supplier_location?: string
  product_name: string
  product_type: string
  metal_type: "Gold" | "Silver" | "Platinum"
  form_type: "Bar" | "Jewel" | "Old Jewel"
  jewel_type?: "With Stone" | "Without Stone"
  
  // Physical weights
  with_stone_weight: number | null
  without_stone_weight: number | null
  stone_weight: number | null

  // Cost percentages (tunch percentages)
  with_stone_cost: number | null
  without_stone_cost: number | null

  // Gold weights
  procured_in_24k: number | null
  balance_weight_24k: number | null
  balance_weight_22k: number | null
  
  status: "Available" | "Low Stock" | "Out of Stock"
  created_at: string
}

interface Supplier {
  id: number
  name: string
  location: string
  contact_person: string
}

// Form data interface
interface FormData {
  supplier_id?: number
  product_name?: string
  product_type?: string
  metal_type: "Gold" | "Silver" | "Platinum"
  form_type: "Bar" | "Jewel" | "Old Jewel"
  jewel_type?: "With Stone" | "Without Stone"
  
  // Physical weights
  with_stone_weight?: number
  without_stone_weight?: number
  stone_weight?: number
  
  // Cost percentages
  with_stone_cost?: number
  without_stone_cost?: number
  
  // Gold weights
  procured_in_24k?: number
  balance_weight_24k?: number
  balance_weight_22k?: number
}

const PRODUCT_TYPES = [
  "Chain", "Ring", "Bangle", "Necklace", "Earrings", "Pendant", 
  "Bracelet", "Anklet", "Nose Pin", "Mangalsutra", "Others"
]

export default function CleanInventoryManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [formData, setFormData] = useState<FormData>({
    metal_type: "Gold",
    form_type: "Jewel",
    jewel_type: "Without Stone"
  })
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null)

  const { data: inventory, refetch } = useDatabase<InventoryItem>("inventory", searchTerm)
  const { data: suppliers } = useDatabase<Supplier>("suppliers")
  const { mutate, loading: mutating } = useDatabaseMutation<InventoryItem>()
  const { toast } = useSimpleToast()

  // Filter inventory based on search
  const filteredInventory = (inventory || []).filter(item =>
    item.product_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.supplier_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.product_type?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Handle supplier selection
  const handleSupplierSelect = (supplierId: string) => {
    const supplier = suppliers?.find(s => s.id === parseInt(supplierId))
    setSelectedSupplier(supplier || null)
    setFormData(prev => ({ ...prev, supplier_id: supplier?.id }))
  }

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedSupplier || !formData.product_name) {
      toast({
        title: "Validation Error",
        description: "Please select supplier and enter product name.",
        variant: "destructive",
      })
      return
    }

    try {
      const inventoryData = {
        supplier_id: selectedSupplier.id,
        product_name: formData.product_name,
        product_type: formData.product_type || formData.product_name,
        metal_type: formData.metal_type,
        form_type: formData.form_type,
        jewel_type: formData.jewel_type,
        
        // Physical weights
        with_stone_weight: formData.with_stone_weight || 0,
        without_stone_weight: formData.without_stone_weight || 0,
        stone_weight: formData.stone_weight || 0,
        
        // Cost percentages (tunch percentages)
        with_stone_cost: formData.with_stone_cost || 0,
        without_stone_cost: formData.without_stone_cost || 0,
        
        // Gold weights
        procured_in_24k: formData.procured_in_24k || 0,
        balance_weight_24k: formData.balance_weight_24k || 0,
        balance_weight_22k: formData.balance_weight_22k || 0,
      }

      const result = await mutate("inventory", "POST", inventoryData)
      if (result) {
        toast({
          title: "Success",
          description: "Inventory item added successfully.",
          variant: "default",
        })
        
        // Reset form
        setFormData({
          metal_type: "Gold",
          form_type: "Jewel",
          jewel_type: "Without Stone"
        })
        setSelectedSupplier(null)
        setIsAddDialogOpen(false)
        await refetch()
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add inventory item.",
        variant: "destructive",
      })
    }
  }

  // Calculate stats
  const totalItems = filteredInventory.length
  const availableItems = filteredInventory.filter(item => item.status === "Available").length
  const totalValue = filteredInventory.reduce((sum, item) => 
    sum + (item.balance_weight_24k * 7000), 0) // Approximate value

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalItems}</div>
            <p className="text-xs text-muted-foreground">Inventory items</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{availableItems}</div>
            <p className="text-xs text-muted-foreground">Ready for sale</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Estimated Value</CardTitle>
            <IndianRupee className="h-4 w-4 text-amber-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{(totalValue / 100000).toFixed(1)}L</div>
            <p className="text-xs text-muted-foreground">Approximate value</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Inventory Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Clean Inventory Management</CardTitle>
              <CardDescription>Robust inventory system matching your data structure</CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Item
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Add New Inventory Item</DialogTitle>
                  <DialogDescription>Enter item details matching your data structure</DialogDescription>
                </DialogHeader>
                
                <div className="grid gap-4 py-4">
                  {/* Supplier Selection */}
                  <div className="space-y-2">
                    <Label>Supplier *</Label>
                    <Select onValueChange={handleSupplierSelect}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select supplier" />
                      </SelectTrigger>
                      <SelectContent>
                        {suppliers?.map((supplier) => (
                          <SelectItem key={supplier.id} value={supplier.id.toString()}>
                            {supplier.name} - {supplier.location}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Product Details */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Product Name *</Label>
                      <Input
                        value={formData.product_name || ""}
                        onChange={(e) => setFormData(prev => ({ ...prev, product_name: e.target.value }))}
                        placeholder="e.g., Chain"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Product Type</Label>
                      <Select 
                        value={formData.product_type || ""} 
                        onValueChange={(value) => setFormData(prev => ({ ...prev, product_type: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          {PRODUCT_TYPES.map((type) => (
                            <SelectItem key={type} value={type}>{type}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Metal & Form Type */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Metal Type</Label>
                      <Select 
                        value={formData.metal_type} 
                        onValueChange={(value: "Gold" | "Silver" | "Platinum") => 
                          setFormData(prev => ({ ...prev, metal_type: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Gold">Gold</SelectItem>
                          <SelectItem value="Silver">Silver</SelectItem>
                          <SelectItem value="Platinum">Platinum</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Form Type</Label>
                      <Select 
                        value={formData.form_type} 
                        onValueChange={(value: "Bar" | "Jewel" | "Old Jewel") => 
                          setFormData(prev => ({ ...prev, form_type: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Bar">Bar</SelectItem>
                          <SelectItem value="Jewel">Jewel</SelectItem>
                          <SelectItem value="Old Jewel">Old Jewel</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Jewel Type</Label>
                      <Select 
                        value={formData.jewel_type || ""} 
                        onValueChange={(value: "With Stone" | "Without Stone") => 
                          setFormData(prev => ({ ...prev, jewel_type: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="With Stone">With Stone</SelectItem>
                          <SelectItem value="Without Stone">Without Stone</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Physical Weights Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-3 text-blue-700">Physical Weights (g)</h3>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>With Stone Weight</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.with_stone_weight || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            with_stone_weight: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="0.000"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Without Stone Weight</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.without_stone_weight || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            without_stone_weight: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="120.420"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Stone Weight</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.stone_weight || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            stone_weight: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="0.000"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Cost Percentages (Tunch) Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-3 text-green-700">Cost Price / Tunch (%)</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>With Stone Cost (%)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={formData.with_stone_cost || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            with_stone_cost: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="0.00"
                        />
                        <p className="text-xs text-muted-foreground">Tunch percentage for with stone</p>
                      </div>
                      <div className="space-y-2">
                        <Label>Without Stone Cost (%)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={formData.without_stone_cost || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            without_stone_cost: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="94.00"
                        />
                        <p className="text-xs text-muted-foreground">Tunch percentage for without stone</p>
                      </div>
                    </div>
                  </div>

                  {/* Gold Weights Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-3 text-amber-700">Gold Weights (g)</h3>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>Procured in 24K</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.procured_in_24k || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            procured_in_24k: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="113.195"
                        />
                        <p className="text-xs text-muted-foreground">Total procured weight</p>
                      </div>
                      <div className="space-y-2">
                        <Label>Balance Weight 24K</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.balance_weight_24k || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            balance_weight_24k: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="9.754"
                        />
                        <p className="text-xs text-muted-foreground">Actual balance in 24K</p>
                      </div>
                      <div className="space-y-2">
                        <Label>Balance Weight 22K</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.balance_weight_22k || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            balance_weight_22k: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="110.260"
                        />
                        <p className="text-xs text-muted-foreground">Actual balance in 22K</p>
                      </div>
                    </div>
                  </div>

                  {/* Summary */}
                  {selectedSupplier && formData.product_name && (
                    <div className="border-t pt-4 bg-green-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-green-800 mb-2">Summary</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p><strong>Supplier:</strong> {selectedSupplier.name}</p>
                          <p><strong>Product:</strong> {formData.product_name}</p>
                          <p><strong>Type:</strong> {formData.product_type || formData.product_name}</p>
                        </div>
                        <div>
                          <p><strong>Physical Weight:</strong> {formData.without_stone_weight || 0}g</p>
                          <p><strong>Procured 24K:</strong> {formData.procured_in_24k || 0}g</p>
                          <p><strong>Balance 24K:</strong> {formData.balance_weight_24k || 0}g</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex justify-end gap-2 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsAddDialogOpen(false)
                      setFormData({
                        metal_type: "Gold",
                        form_type: "Jewel",
                        jewel_type: "Without Stone"
                      })
                      setSelectedSupplier(null)
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSubmit}
                    disabled={mutating || !selectedSupplier || !formData.product_name}
                  >
                    {mutating ? "Adding..." : "Add Item"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>

        <CardContent>
          {/* Search */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search inventory..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Badge variant="secondary">{filteredInventory.length} items</Badge>
          </div>

          {/* Table */}
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Sl.No</TableHead>
                    <TableHead>Supplier</TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead>Physical Weights (g)</TableHead>
                    <TableHead>Gold Weights (g)</TableHead>
                    <TableHead>Cost Price (%)</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredInventory.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                        No inventory items found. Click "Add Item" to get started.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredInventory.map((item, index) => (
                    <TableRow key={item.id}>
                      <TableCell>{index + 1}</TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{item.supplier_name}</p>
                          <p className="text-sm text-muted-foreground">{item.supplier_location}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{item.product_name}</p>
                          <p className="text-sm text-muted-foreground">{item.product_type}</p>
                          <Badge variant="outline" className="text-xs">
                            {item.metal_type} {item.form_type} {item.jewel_type}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <p>With Stone: {(Number(item.with_stone_weight) || 0).toFixed(3)}g</p>
                          <p>Without Stone: {(Number(item.without_stone_weight) || 0).toFixed(3)}g</p>
                          <p className="text-xs text-muted-foreground">Stone: {(Number(item.stone_weight) || 0).toFixed(3)}g</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <p>Procured: {(Number(item.procured_in_24k) || 0).toFixed(3)}g</p>
                          <p>24K Balance: {(Number(item.balance_weight_24k) || 0).toFixed(3)}g</p>
                          <p>22K Balance: {(Number(item.balance_weight_22k) || 0).toFixed(3)}g</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <p>With Stone: {(Number(item.with_stone_cost) || 0).toFixed(2)}%</p>
                          <p>Without Stone: {(Number(item.without_stone_cost) || 0).toFixed(2)}%</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            item.status === "Available"
                              ? "default"
                              : item.status === "Low Stock"
                                ? "secondary"
                                : "destructive"
                          }
                        >
                          {item.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
