"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Plus, Search, Edit, Trash2, Package, TrendingUp, IndianRupee } from "lucide-react"
import { useDatabase } from "@/hooks/use-database"
import { useDatabaseMutation } from "@/hooks/use-database-mutation"
import { useSimpleToast } from "@/hooks/use-simple-toast"

// Clean interface matching your exact data structure
interface InventoryItem {
  id: number
  supplier_id: number
  supplier_name?: string
  supplier_location?: string
  product_name: string
  product_type: string
  metal_type: "Gold" | "Silver" | "Platinum"
  form_type: "Bar" | "Jewel" | "Old Jewel"
  jewel_type?: "With Stone" | "Without Stone"
  
  // Physical weights
  with_stone_weight: number | null
  without_stone_weight: number | null
  stone_weight: number | null

  // Cost percentages (tunch percentages)
  with_stone_cost: number | null
  without_stone_cost: number | null

  // Gold weights
  procured_in_24k: number | null
  balance_weight_24k: number | null
  balance_weight_22k: number | null

  // Sold values
  stone_weight_22k: number | null
  sold_gold_weight_22k: number | null
  sold_gold_weight_24k: number | null

  // Balance in stock
  balance_gold_weight_22k: number | null

  status: "Available" | "Low Stock" | "Out of Stock"
  created_at: string
}

interface Supplier {
  id: number
  name: string
  location: string
  contact_person: string
}

// Form data interface
interface FormData {
  supplier_id?: number
  product_name?: string
  product_type?: string
  metal_type: "Gold" | "Silver" | "Platinum"
  form_type: "Bar" | "Jewel" | "Old Jewel"
  jewel_type?: "With Stone" | "Without Stone"
  jewel_category?: string
  
  // Physical weights
  with_stone_weight?: number
  without_stone_weight?: number
  stone_weight?: number
  
  // Cost percentages
  with_stone_cost?: number
  without_stone_cost?: number
  
  // Gold weights
  procured_in_24k?: number
  balance_weight_24k?: number
  balance_weight_22k?: number

  // Balance in stock (calculated from procured - sold)
  balance_gold_weight_22k?: number

  // Business parameters
  with_stone_tunch_percentage?: number
  without_stone_tunch_percentage?: number
  wastage_percentage?: number
  expected_processing_loss?: number
  making_charges?: number
}

const PRODUCT_TYPES = [
  "Chain", "Ring", "Bangle", "Necklace", "Earrings", "Pendant",
  "Bracelet", "Anklet", "Nose Pin", "Mangalsutra", "Others"
]

// Metal Information Constants (from previous implementation)
const METAL_TYPES = ["Gold", "Silver", "Platinum"] as const
const FORM_TYPES = ["Bar", "Jewel", "Old Jewel"] as const
const JEWEL_TYPES = ["With Stone", "Without Stone"] as const
const JEWEL_CATEGORIES = [
  "Bangle", "Ring", "Chain", "Necklace", "Studs", "Pendant",
  "Bracelet", "Mangalsutra", "Nosepin", "Vaddanam", "Choker",
  "Earrings", "Haram", "Anklet", "Others"
] as const

export default function CleanInventoryManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<InventoryItem | null>(null)
  const [formData, setFormData] = useState<FormData>({
    metal_type: "Gold",
    form_type: "Jewel",
    jewel_type: "Without Stone"
  })
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null)

  const { data: inventory, refetch } = useDatabase<InventoryItem>("inventory", searchTerm)
  const { data: suppliers } = useDatabase<Supplier>("suppliers")
  const { mutate, loading: mutating } = useDatabaseMutation<InventoryItem>()
  const { toast } = useSimpleToast()

  // Filter inventory based on search
  const filteredInventory = (inventory || []).filter(item =>
    item.product_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.supplier_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.product_type?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Handle supplier selection
  const handleSupplierSelect = (supplierId: string) => {
    const supplier = suppliers?.find(s => s.id === parseInt(supplierId))
    setSelectedSupplier(supplier || null)
    setFormData(prev => ({ ...prev, supplier_id: supplier?.id }))
  }

  // Form validation
  const validateForm = () => {
    const errors = []

    if (!selectedSupplier) errors.push("Please select a supplier")
    if (!formData.product_name?.trim()) errors.push("Please enter product name")
    if (!formData.without_stone_weight || formData.without_stone_weight <= 0) {
      errors.push("Please enter valid without stone weight")
    }
    if (!formData.procured_in_24k || formData.procured_in_24k <= 0) {
      errors.push("Please enter valid procured 24K weight")
    }

    return errors
  }

  // Handle edit item
  const handleEdit = (item: InventoryItem) => {
    setEditingItem(item)

    // Find the supplier for this item
    const supplier = suppliers?.find(s => s.id === item.supplier_id)
    setSelectedSupplier(supplier || null)

    // Populate form with existing data
    setFormData({
      product_name: item.product_name,
      product_type: item.product_type,
      metal_type: item.metal_type,
      form_type: item.form_type,
      jewel_type: item.jewel_type,
      jewel_category: item.jewel_category,
      with_stone_weight: item.with_stone_weight,
      without_stone_weight: item.without_stone_weight,
      stone_weight: item.stone_weight,
      with_stone_cost: item.with_stone_cost,
      without_stone_cost: item.without_stone_cost,
      procured_in_24k: item.procured_in_24k,
      balance_weight_24k: item.balance_weight_24k,
      balance_weight_22k: item.balance_weight_22k,
      balance_gold_weight_22k: item.balance_gold_weight_22k,
      with_stone_tunch_percentage: item.with_stone_tunch_percentage,
      without_stone_tunch_percentage: item.without_stone_tunch_percentage,
      wastage_percentage: item.wastage_percentage,
      expected_processing_loss: item.expected_processing_loss,
      making_charges: item.making_charges,
    })

    setIsEditDialogOpen(true)
  }

  // Handle delete item
  const handleDelete = async (item: InventoryItem) => {
    if (!confirm(`Are you sure you want to delete "${item.product_name}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/inventory/${item.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Inventory item deleted successfully.",
          variant: "default",
        })
        await refetch()
      } else {
        throw new Error('Failed to delete item')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete inventory item.",
        variant: "destructive",
      })
    }
  }

  // Handle form submission (Create or Update)
  const handleSubmit = async () => {
    const validationErrors = validateForm()

    if (validationErrors.length > 0) {
      toast({
        title: "Validation Error",
        description: validationErrors.join(", "),
        variant: "destructive",
      })
      return
    }

    try {
      const inventoryData = {
        supplier_id: selectedSupplier.id,
        product_name: formData.product_name,
        product_type: formData.product_name, // Use product_name as product_type
        metal_type: formData.metal_type,
        form_type: formData.form_type,
        jewel_type: formData.jewel_type,
        jewel_category: formData.jewel_category,

        // Physical weights
        with_stone_weight: formData.with_stone_weight || 0,
        without_stone_weight: formData.without_stone_weight || 0,
        stone_weight: formData.stone_weight || 0,

        // Cost percentages (tunch percentages)
        with_stone_cost: formData.with_stone_cost || 0,
        without_stone_cost: formData.without_stone_cost || 0,

        // Gold weights
        procured_in_24k: formData.procured_in_24k || 0,
        balance_weight_24k: formData.balance_weight_24k || 0,
        balance_weight_22k: formData.balance_weight_22k || 0,

        // Balance in stock (initially same as procured, will be reduced on sales)
        balance_gold_weight_22k: formData.balance_gold_weight_22k || formData.balance_weight_22k || 0,

        // Business parameters
        with_stone_tunch_percentage: formData.with_stone_tunch_percentage || 0,
        without_stone_tunch_percentage: formData.without_stone_tunch_percentage || 0,
        wastage_percentage: formData.wastage_percentage || 0,
        expected_processing_loss: formData.expected_processing_loss || 0,
        making_charges: formData.making_charges || 0,
      }

      let result
      if (editingItem) {
        // Update existing item
        const response = await fetch(`/api/inventory/${editingItem.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(inventoryData),
        })
        result = await response.json()
      } else {
        // Create new item
        result = await mutate("inventory", "POST", inventoryData)
      }

      if (result && result.success !== false) {
        toast({
          title: "Success",
          description: editingItem ? "Inventory item updated successfully." : "Inventory item added successfully.",
          variant: "default",
        })

        // Reset form
        setFormData({
          metal_type: "Gold",
          form_type: "Jewel",
          jewel_type: "Without Stone"
        })
        setSelectedSupplier(null)
        setEditingItem(null)
        setIsAddDialogOpen(false)
        setIsEditDialogOpen(false)
        await refetch()
      } else {
        throw new Error(result?.error || 'Operation failed')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: editingItem ? "Failed to update inventory item." : "Failed to add inventory item.",
        variant: "destructive",
      })
    }
  }

  // Calculate stats
  const totalItems = filteredInventory.length
  const availableItems = filteredInventory.filter(item => item.status === "Available").length
  const totalValue = filteredInventory.reduce((sum, item) => 
    sum + (item.balance_weight_24k * 7000), 0) // Approximate value

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalItems}</div>
            <p className="text-xs text-muted-foreground">Inventory items</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{availableItems}</div>
            <p className="text-xs text-muted-foreground">Ready for sale</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Estimated Value</CardTitle>
            <IndianRupee className="h-4 w-4 text-amber-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{(totalValue / 100000).toFixed(1)}L</div>
            <p className="text-xs text-muted-foreground">Approximate value</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Inventory Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Clean Inventory Management</CardTitle>
              <CardDescription>Robust inventory system matching your data structure</CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Item
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Add New Inventory Item</DialogTitle>
                  <DialogDescription>Enter item details matching your data structure</DialogDescription>
                </DialogHeader>
                
                <div className="grid gap-4 py-4">
                  {/* Supplier Selection */}
                  <div className="space-y-2">
                    <Label>Supplier *</Label>
                    <Select onValueChange={handleSupplierSelect}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select supplier" />
                      </SelectTrigger>
                      <SelectContent>
                        {suppliers?.map((supplier) => (
                          <SelectItem key={supplier.id} value={supplier.id.toString()}>
                            {supplier.name} - {supplier.location}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Product Details */}
                  <div className="space-y-2">
                    <Label>Product Name *</Label>
                    <Input
                      value={formData.product_name || ""}
                      onChange={(e) => setFormData(prev => ({ ...prev, product_name: e.target.value }))}
                      placeholder="e.g., Chain"
                    />
                  </div>

                  {/* Metal Information Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-3 text-amber-700">Metal Information</h3>

                    {/* Metal Type and Form Type */}
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="space-y-2">
                        <Label htmlFor="metal_type">Metal Type</Label>
                        <Select
                          value={formData.metal_type}
                          onValueChange={(value) => setFormData(prev => ({ ...prev, metal_type: value as any }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select metal type" />
                          </SelectTrigger>
                          <SelectContent>
                            {METAL_TYPES.map((type) => (
                              <SelectItem key={type} value={type}>{type}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="form_type">Form Type</Label>
                        <Select
                          value={formData.form_type}
                          onValueChange={(value) => {
                            setFormData(prev => ({
                              ...prev,
                              form_type: value as any,
                              // Reset jewel-specific fields if not Jewel
                              jewel_type: value === "Jewel" ? prev.jewel_type : undefined,
                              jewel_category: value === "Jewel" ? prev.jewel_category : undefined
                            }))
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select form type" />
                          </SelectTrigger>
                          <SelectContent>
                            {FORM_TYPES.map((type) => (
                              <SelectItem key={type} value={type}>{type}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Jewel-specific fields */}
                    {formData.form_type === "Jewel" && (
                      <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                        <h4 className="font-medium mb-3 text-amber-800">Jewel Type Selection</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="jewel_type">Jewel Type</Label>
                            <Select
                              value={formData.jewel_type || "Without Stone"}
                              onValueChange={(value) => setFormData(prev => ({ ...prev, jewel_type: value as any }))}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select jewel type" />
                              </SelectTrigger>
                              <SelectContent>
                                {JEWEL_TYPES.map((type) => (
                                  <SelectItem key={type} value={type}>{type}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="jewel_category">Jewel Category</Label>
                            <Select
                              value={formData.jewel_category || ""}
                              onValueChange={(value) => setFormData(prev => ({ ...prev, jewel_category: value }))}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                              <SelectContent>
                                {JEWEL_CATEGORIES.map((category) => (
                                  <SelectItem key={category} value={category}>{category}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>



                  {/* Physical Weights Section - Adaptive based on jewel type */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-3 text-blue-700">Physical Weights (g)</h3>

                    <div className="grid grid-cols-3 gap-4 mb-4">
                      {/* Gross Weight - Always shown, but label and behavior changes */}
                      <div className="space-y-2">
                        <Label htmlFor="grossWeight">
                          {formData.form_type === "Bar" || formData.jewel_type === "Without Stone"
                            ? "Weight (g)"
                            : "Gross Weight (g)"}
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="grossWeight"
                          type="number"
                          step="0.001"
                          value={formData.form_type === "Bar" || formData.jewel_type === "Without Stone"
                            ? (formData.without_stone_weight || "")
                            : (formData.with_stone_weight || "")
                          }
                          onChange={(e) => {
                            const value = parseFloat(e.target.value) || 0
                            if (formData.form_type === "Bar" || formData.jewel_type === "Without Stone") {
                              setFormData(prev => ({
                                ...prev,
                                without_stone_weight: value,
                                with_stone_weight: 0,
                                stone_weight: 0
                              }))
                            } else {
                              setFormData(prev => ({ ...prev, with_stone_weight: value }))
                            }
                          }}
                          placeholder="120.420"
                        />
                        <p className="text-xs text-muted-foreground">
                          {formData.form_type === "Bar" || formData.jewel_type === "Without Stone"
                            ? "Total weight of the item"
                            : "Total weight including stones"}
                        </p>
                      </div>

                      {/* Stone Weight - Only for "With Stone" jewels */}
                      {formData.form_type === "Jewel" && formData.jewel_type === "With Stone" && (
                        <div className="space-y-2">
                          <Label htmlFor="stoneWeight">
                            Stone Weight (g)
                            <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="stoneWeight"
                            type="number"
                            step="0.001"
                            value={formData.stone_weight || ""}
                            onChange={(e) => {
                              const stoneWeight = parseFloat(e.target.value) || 0
                              const grossWeight = formData.with_stone_weight || 0
                              setFormData(prev => ({
                                ...prev,
                                stone_weight: stoneWeight,
                                without_stone_weight: Math.max(0, grossWeight - stoneWeight)
                              }))
                            }}
                            placeholder="0.000"
                          />
                          <p className="text-xs text-muted-foreground">
                            Net Weight: {((Number(formData.with_stone_weight) || 0) - (Number(formData.stone_weight) || 0)).toFixed(3)}g
                          </p>
                        </div>
                      )}

                      {/* Net Weight Display - Only for "With Stone" jewels (read-only) */}
                      {formData.form_type === "Jewel" && formData.jewel_type === "With Stone" && (
                        <div className="space-y-2">
                          <Label>Net Weight (g)</Label>
                          <Input
                            type="number"
                            step="0.001"
                            value={formData.without_stone_weight || ""}
                            readOnly
                            className="bg-gray-50"
                          />
                          <p className="text-xs text-muted-foreground">
                            Auto-calculated: Gross - Stone
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Cost Information - Adaptive */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-3 text-green-700">Cost Information</h3>
                    <div className="space-y-2">
                      <Label htmlFor="costPercentage">
                        {formData.form_type === "Bar"
                          ? "Cost Percentage (%)"
                          : formData.jewel_type === "With Stone"
                            ? "With Stone Cost (%)"
                            : "Without Stone Cost (%)"}
                        <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="costPercentage"
                        type="number"
                        step="0.01"
                        value={formData.form_type === "Bar" || formData.jewel_type === "Without Stone"
                          ? (formData.without_stone_cost || "")
                          : (formData.with_stone_cost || "")
                        }
                        onChange={(e) => {
                          const value = parseFloat(e.target.value) || 0
                          if (formData.form_type === "Bar" || formData.jewel_type === "Without Stone") {
                            setFormData(prev => ({ ...prev, without_stone_cost: value, with_stone_cost: 0 }))
                          } else {
                            setFormData(prev => ({ ...prev, with_stone_cost: value }))
                          }
                        }}
                        placeholder="94.00"
                      />
                      <p className="text-xs text-muted-foreground">
                        Cost percentage for procurement pricing
                      </p>
                    </div>
                  </div>

                  {/* Gold Weights Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-3 text-amber-700">Gold Weights (g)</h3>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>Procured in 24K</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.procured_in_24k || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            procured_in_24k: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="113.195"
                        />
                        <p className="text-xs text-muted-foreground">Total procured weight</p>
                      </div>
                      <div className="space-y-2">
                        <Label>Balance Weight 24K</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.balance_weight_24k || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            balance_weight_24k: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="9.754"
                        />
                        <p className="text-xs text-muted-foreground">Actual balance in 24K</p>
                      </div>
                      <div className="space-y-2">
                        <Label>Balance Weight 22K</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.balance_weight_22k || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            balance_weight_22k: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="110.260"
                        />
                        <p className="text-xs text-muted-foreground">Actual balance in 22K</p>
                      </div>
                    </div>
                  </div>



                  {/* Balance in Stock Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-3 text-indigo-700">Balance in Stock (g)</h3>
                    <div className="grid grid-cols-1 gap-4">
                      <div className="space-y-2">
                        <Label>Gold Weight in 22K</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.balance_gold_weight_22k || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            balance_gold_weight_22k: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="110.260"
                        />
                        <p className="text-xs text-muted-foreground">Final balance gold weight in 22K</p>
                      </div>
                    </div>
                  </div>

                  {/* Business Parameters Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-3 text-red-700">Business Parameters</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>With Stone Tunch (%)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={formData.with_stone_tunch_percentage || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            with_stone_tunch_percentage: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="93.00"
                        />
                        <p className="text-xs text-muted-foreground">Tunch percentage for with stone</p>
                      </div>
                      <div className="space-y-2">
                        <Label>Without Stone Tunch (%)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={formData.without_stone_tunch_percentage || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            without_stone_tunch_percentage: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="94.00"
                        />
                        <p className="text-xs text-muted-foreground">Tunch percentage for without stone</p>
                      </div>
                      <div className="space-y-2">
                        <Label>Wastage Percentage (%)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={formData.wastage_percentage || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            wastage_percentage: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="2.00"
                        />
                        <p className="text-xs text-muted-foreground">Expected wastage percentage</p>
                      </div>
                      <div className="space-y-2">
                        <Label>Processing Loss (g)</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.expected_processing_loss || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            expected_processing_loss: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="0.500"
                        />
                        <p className="text-xs text-muted-foreground">Expected processing loss in grams</p>
                      </div>
                      <div className="space-y-2">
                        <Label>Making Charges (₹)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={formData.making_charges || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            making_charges: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="5000.00"
                        />
                        <p className="text-xs text-muted-foreground">Making charges in rupees</p>
                      </div>
                    </div>
                  </div>

                  {/* Smart Calculations Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-3 text-cyan-700">Smart Calculations</h3>
                    <div className="bg-cyan-50 p-4 rounded-lg space-y-3">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p><strong>Auto-calculated Values:</strong></p>
                          <p>• Stone Weight: {((Number(formData.with_stone_weight) || 0) - (Number(formData.without_stone_weight) || 0)).toFixed(3)}g</p>
                          <p>• Net Weight: {(Number(formData.without_stone_weight) || 0).toFixed(3)}g</p>
                          <p>• 24K from Tunch: {formData.without_stone_weight && formData.without_stone_tunch_percentage ?
                            ((Number(formData.without_stone_weight) * Number(formData.without_stone_tunch_percentage)) / 100).toFixed(3) : '0.000'}g</p>
                        </div>
                        <div>
                          <p><strong>Expected Yields:</strong></p>
                          <p>• Processing Loss: {(Number(formData.expected_processing_loss) || 0).toFixed(3)}g</p>
                          <p>• Expected 24K Yield: {formData.procured_in_24k && formData.expected_processing_loss ?
                            (Number(formData.procured_in_24k) - Number(formData.expected_processing_loss)).toFixed(3) : (Number(formData.procured_in_24k) || 0).toFixed(3)}g</p>
                          <p>• Available Stock 22K: {formData.balance_gold_weight_22k ?
                            (Number(formData.balance_gold_weight_22k)).toFixed(3) : (Number(formData.balance_weight_22k) || 0).toFixed(3)}g</p>
                        </div>
                      </div>

                      <div className="border-t pt-3">
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (formData.without_stone_weight && formData.without_stone_tunch_percentage) {
                                const calculated24K = (Number(formData.without_stone_weight) * Number(formData.without_stone_tunch_percentage)) / 100;
                                setFormData(prev => ({ ...prev, procured_in_24k: calculated24K }));
                              }
                            }}
                          >
                            Calculate 24K from Tunch
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (formData.with_stone_weight && formData.without_stone_weight) {
                                const stoneWeight = Number(formData.with_stone_weight) - Number(formData.without_stone_weight);
                                setFormData(prev => ({ ...prev, stone_weight: stoneWeight }));
                              }
                            }}
                          >
                            Calculate Stone Weight
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (formData.procured_in_24k) {
                                const balance22K = Number(formData.procured_in_24k) * 0.916; // 24K to 22K conversion
                                setFormData(prev => ({ ...prev, balance_gold_weight_22k: balance22K }));
                              }
                            }}
                          >
                            Calculate 22K Balance
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Summary */}
                  {selectedSupplier && formData.product_name && (
                    <div className="border-t pt-4 bg-green-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-green-800 mb-3">📋 Complete Inventory Summary</h4>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="font-semibold text-green-700 mb-2">Basic Info:</p>
                          <p><strong>Supplier:</strong> {selectedSupplier.name}</p>
                          <p><strong>Location:</strong> {selectedSupplier.location}</p>
                          <p><strong>Product:</strong> {formData.product_name}</p>
                          <p><strong>Type:</strong> {formData.product_type || formData.product_name}</p>
                          <p><strong>Metal:</strong> {formData.metal_type} {formData.form_type}</p>
                          {formData.form_type === "Jewel" && (
                            <>
                              <p><strong>Jewel Type:</strong> {formData.jewel_type || 'Not selected'}</p>
                              <p><strong>Category:</strong> {formData.jewel_category || 'Not selected'}</p>
                            </>
                          )}
                        </div>
                        <div>
                          <p className="font-semibold text-blue-700 mb-2">Physical Weights:</p>
                          <p><strong>With Stone:</strong> {(Number(formData.with_stone_weight) || 0).toFixed(3)}g</p>
                          <p><strong>Without Stone:</strong> {(Number(formData.without_stone_weight) || 0).toFixed(3)}g</p>
                          <p><strong>Stone Weight:</strong> {(Number(formData.stone_weight) || 0).toFixed(3)}g</p>
                          <p><strong>Procured 24K:</strong> {(Number(formData.procured_in_24k) || 0).toFixed(3)}g</p>
                          <p><strong>Balance 22K:</strong> {(Number(formData.balance_gold_weight_22k) || 0).toFixed(3)}g</p>
                        </div>
                        <div>
                          <p className="font-semibold text-amber-700 mb-2">Business Data:</p>
                          <p><strong>With Stone Cost:</strong> {(Number(formData.with_stone_cost) || 0).toFixed(2)}%</p>
                          <p><strong>Without Stone Cost:</strong> {(Number(formData.without_stone_cost) || 0).toFixed(2)}%</p>
                          <p><strong>Tunch (With):</strong> {(Number(formData.with_stone_tunch_percentage) || 0).toFixed(2)}%</p>
                          <p><strong>Tunch (Without):</strong> {(Number(formData.without_stone_tunch_percentage) || 0).toFixed(2)}%</p>
                          <p><strong>Making Charges:</strong> ₹{(Number(formData.making_charges) || 0).toFixed(2)}</p>
                        </div>
                      </div>

                      {/* Expected Table Row Preview */}
                      <div className="mt-4 pt-3 border-t border-green-200">
                        <p className="font-semibold text-green-700 mb-2">📊 Expected Table Display:</p>
                        <div className="bg-white p-3 rounded border text-xs font-mono">
                          <div className="grid grid-cols-10 gap-1 text-center">
                            <div className="font-bold">Sl.No</div>
                            <div className="font-bold">Supplier</div>
                            <div className="font-bold">Location</div>
                            <div className="font-bold">Product</div>
                            <div className="font-bold">With Stone</div>
                            <div className="font-bold">Without Stone</div>
                            <div className="font-bold">Cost %</div>
                            <div className="font-bold">Procured</div>
                            <div className="font-bold">Sold 22K</div>
                            <div className="font-bold">Balance 22K</div>
                          </div>
                          <div className="grid grid-cols-10 gap-1 text-center mt-1 py-1 bg-gray-50">
                            <div>1</div>
                            <div>{selectedSupplier.name.substring(0, 8)}...</div>
                            <div>{selectedSupplier.location}</div>
                            <div>{formData.product_name}</div>
                            <div>{(Number(formData.with_stone_weight) || 0).toFixed(1)}</div>
                            <div>{(Number(formData.without_stone_weight) || 0).toFixed(1)}</div>
                            <div>{(Number(formData.without_stone_cost) || 0).toFixed(0)}%</div>
                            <div>{(Number(formData.procured_in_24k) || 0).toFixed(1)}</div>
                            <div>0.0</div>
                            <div>{(Number(formData.balance_gold_weight_22k) || Number(formData.balance_weight_22k) || 0).toFixed(1)}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex justify-end gap-2 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsAddDialogOpen(false)
                      setFormData({
                        metal_type: "Gold",
                        form_type: "Jewel",
                        jewel_type: "Without Stone"
                      })
                      setSelectedSupplier(null)
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSubmit}
                    disabled={mutating || !selectedSupplier || !formData.product_name}
                  >
                    {mutating ? "Adding..." : "Add Item"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            {/* Edit Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Edit Inventory Item</DialogTitle>
                  <DialogDescription>Update item details</DialogDescription>
                </DialogHeader>

                <div className="grid gap-4 py-4">
                  {/* Supplier Selection */}
                  <div className="space-y-2">
                    <Label>Supplier *</Label>
                    <Select
                      value={selectedSupplier?.id.toString() || ""}
                      onValueChange={(value) => {
                        const supplier = suppliers?.find(s => s.id === parseInt(value))
                        setSelectedSupplier(supplier || null)
                        setFormData(prev => ({ ...prev, supplier_id: supplier?.id }))
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select supplier" />
                      </SelectTrigger>
                      <SelectContent>
                        {suppliers?.map((supplier) => (
                          <SelectItem key={supplier.id} value={supplier.id.toString()}>
                            {supplier.name} - {supplier.location}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Product Details */}
                  <div className="space-y-2">
                    <Label>Product Name *</Label>
                    <Input
                      value={formData.product_name || ""}
                      onChange={(e) => setFormData(prev => ({ ...prev, product_name: e.target.value }))}
                      placeholder="e.g., Chain"
                    />
                  </div>

                  {/* Metal Information Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-4 text-amber-700">Metal Information</h3>

                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="space-y-2">
                        <Label htmlFor="metal_type">Metal Type</Label>
                        <Select
                          value={formData.metal_type || "Gold"}
                          onValueChange={(value) => setFormData(prev => ({ ...prev, metal_type: value as any }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select metal type" />
                          </SelectTrigger>
                          <SelectContent>
                            {METAL_TYPES.map((type) => (
                              <SelectItem key={type} value={type}>{type}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="form_type">Form Type</Label>
                        <Select
                          value={formData.form_type || "Jewel"}
                          onValueChange={(value) => {
                            setFormData(prev => ({
                              ...prev,
                              form_type: value as any,
                              // Reset jewel-specific fields if not Jewel
                              jewel_type: value === "Jewel" ? prev.jewel_type : undefined,
                              jewel_category: value === "Jewel" ? prev.jewel_category : undefined
                            }))
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select form type" />
                          </SelectTrigger>
                          <SelectContent>
                            {FORM_TYPES.map((type) => (
                              <SelectItem key={type} value={type}>{type}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Jewel-specific fields */}
                    {formData.form_type === "Jewel" && (
                      <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                        <h4 className="font-medium mb-3 text-amber-800">Jewel Type Selection</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="jewel_type">Jewel Type</Label>
                            <Select
                              value={formData.jewel_type || "Without Stone"}
                              onValueChange={(value) => setFormData(prev => ({ ...prev, jewel_type: value as any }))}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select jewel type" />
                              </SelectTrigger>
                              <SelectContent>
                                {JEWEL_TYPES.map((type) => (
                                  <SelectItem key={type} value={type}>{type}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="jewel_category">Jewel Category</Label>
                            <Select
                              value={formData.jewel_category || ""}
                              onValueChange={(value) => setFormData(prev => ({ ...prev, jewel_category: value }))}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                              <SelectContent>
                                {JEWEL_CATEGORIES.map((category) => (
                                  <SelectItem key={category} value={category}>{category}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Note about sold values */}
                  {editingItem && (Number(editingItem.sold_gold_weight_22k) > 0 || Number(editingItem.sold_gold_weight_24k) > 0) && (
                    <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-lg">
                      <p className="text-sm text-yellow-800">
                        <strong>⚠️ Note:</strong> This item has sales history.
                        Sold: {Number(editingItem.sold_gold_weight_22k).toFixed(3)}g (22K),
                        Available: {Number(editingItem.balance_gold_weight_22k).toFixed(3)}g
                      </p>
                    </div>
                  )}

                  {/* Physical Weights Section - Adaptive */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-3 text-blue-700">Physical Weights (g)</h3>

                    <div className="grid grid-cols-3 gap-4 mb-4">
                      {/* Gross Weight - Always shown, but label and behavior changes */}
                      <div className="space-y-2">
                        <Label htmlFor="editGrossWeight">
                          {formData.form_type === "Bar" || formData.jewel_type === "Without Stone"
                            ? "Weight (g)"
                            : "Gross Weight (g)"}
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="editGrossWeight"
                          type="number"
                          step="0.001"
                          value={formData.form_type === "Bar" || formData.jewel_type === "Without Stone"
                            ? (formData.without_stone_weight || "")
                            : (formData.with_stone_weight || "")
                          }
                          onChange={(e) => {
                            const value = parseFloat(e.target.value) || 0
                            if (formData.form_type === "Bar" || formData.jewel_type === "Without Stone") {
                              setFormData(prev => ({
                                ...prev,
                                without_stone_weight: value,
                                with_stone_weight: 0,
                                stone_weight: 0
                              }))
                            } else {
                              setFormData(prev => ({ ...prev, with_stone_weight: value }))
                            }
                          }}
                          placeholder="120.420"
                        />
                      </div>

                      {/* Stone Weight - Only for "With Stone" jewels */}
                      {formData.form_type === "Jewel" && formData.jewel_type === "With Stone" && (
                        <div className="space-y-2">
                          <Label htmlFor="editStoneWeight">
                            Stone Weight (g)
                            <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="editStoneWeight"
                            type="number"
                            step="0.001"
                            value={formData.stone_weight || ""}
                            onChange={(e) => {
                              const stoneWeight = parseFloat(e.target.value) || 0
                              const grossWeight = formData.with_stone_weight || 0
                              setFormData(prev => ({
                                ...prev,
                                stone_weight: stoneWeight,
                                without_stone_weight: Math.max(0, grossWeight - stoneWeight)
                              }))
                            }}
                            placeholder="0.000"
                          />
                        </div>
                      )}

                      {/* Cost Information - Adaptive */}
                      <div className="space-y-2">
                        <Label htmlFor="editCostPercentage">
                          {formData.form_type === "Bar"
                            ? "Cost Percentage (%)"
                            : formData.jewel_type === "With Stone"
                              ? "With Stone Cost (%)"
                              : "Without Stone Cost (%)"}
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="editCostPercentage"
                          type="number"
                          step="0.01"
                          value={formData.form_type === "Bar" || formData.jewel_type === "Without Stone"
                            ? (formData.without_stone_cost || "")
                            : (formData.with_stone_cost || "")
                          }
                          onChange={(e) => {
                            const value = parseFloat(e.target.value) || 0
                            if (formData.form_type === "Bar" || formData.jewel_type === "Without Stone") {
                              setFormData(prev => ({ ...prev, without_stone_cost: value, with_stone_cost: 0 }))
                            } else {
                              setFormData(prev => ({ ...prev, with_stone_cost: value }))
                            }
                          }}
                          placeholder="94.00"
                        />
                      </div>
                    </div>

                    {/* Procured in 24K */}
                    <div className="space-y-2">
                      <Label>Procured in 24K (g)</Label>
                      <Input
                        type="number"
                        step="0.001"
                        value={formData.procured_in_24k || ""}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          procured_in_24k: parseFloat(e.target.value) || 0
                        }))}
                        placeholder="113.195"
                      />
                    </div>
                  </div>

                  {/* Balance Weight Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-3 text-indigo-700">Balance Weights (g)</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Balance Weight 22K</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.balance_weight_22k || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            balance_weight_22k: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="110.260"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Available Stock 22K</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={formData.balance_gold_weight_22k || ""}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            balance_gold_weight_22k: parseFloat(e.target.value) || 0
                          }))}
                          placeholder="110.260"
                        />
                        <p className="text-xs text-muted-foreground">Current available stock</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end gap-2 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsEditDialogOpen(false)
                      setEditingItem(null)
                      setFormData({
                        metal_type: "Gold",
                        form_type: "Jewel",
                        jewel_type: "Without Stone"
                      })
                      setSelectedSupplier(null)
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSubmit}
                    disabled={mutating || !selectedSupplier || !formData.product_name}
                  >
                    {mutating ? "Updating..." : "Update Item"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>

        <CardContent>
          {/* Search */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search inventory..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Badge variant="secondary">{filteredInventory.length} items</Badge>
          </div>

          {/* Table */}
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
              <Table className="border-collapse">
                <style jsx>{`
                  .border-r {
                    border-right: 1px solid #e5e7eb;
                  }
                  .align-middle {
                    vertical-align: middle;
                  }
                `}</style>
                <TableHeader>
                  <TableRow>
                    <TableHead rowSpan={2} className="text-center border-r align-middle">Sl.No</TableHead>
                    <TableHead colSpan={3} className="text-center border-r">Description</TableHead>
                    <TableHead colSpan={2} className="text-center border-r">Product Type</TableHead>
                    <TableHead colSpan={2} className="text-center border-r">Purchase Price</TableHead>
                    <TableHead rowSpan={2} className="text-center border-r align-middle">Procured in 24k</TableHead>
                    <TableHead colSpan={3} className="text-center border-r">Sold Value</TableHead>
                    <TableHead colSpan={1} className="text-center border-r">Balance in Stock</TableHead>
                    <TableHead rowSpan={2} className="text-center align-middle">Actions</TableHead>
                  </TableRow>
                  <TableRow>
                    <TableHead className="text-center border-r">Supplier Name</TableHead>
                    <TableHead className="text-center border-r">Location</TableHead>
                    <TableHead className="text-center border-r">Product Name</TableHead>
                    <TableHead className="text-center border-r">With Stone</TableHead>
                    <TableHead className="text-center border-r">Without Stone</TableHead>
                    <TableHead className="text-center border-r">With Stone Cost Price (tunch %)</TableHead>
                    <TableHead className="text-center border-r">Without Stone Cost Price (tunch %)</TableHead>
                    <TableHead className="text-center border-r">Stone Weight 22k (Sold)</TableHead>
                    <TableHead className="text-center border-r">Gold Weight in 22k (Sold)</TableHead>
                    <TableHead className="text-center border-r">Gold Weight in 24k (Sold)</TableHead>
                    <TableHead className="text-center border-r">Available Stock 22k</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredInventory.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={14} className="text-center py-8 text-muted-foreground">
                        No inventory items found. Click "Add Item" to get started.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredInventory.map((item, index) => (
                    <TableRow key={item.id}>
                      <TableCell className="text-center border-r">{index + 1}</TableCell>
                      <TableCell className="border-r">{item.supplier_name || 'N/A'}</TableCell>
                      <TableCell className="border-r">{item.supplier_location || 'N/A'}</TableCell>
                      <TableCell className="border-r">{item.product_name || 'N/A'}</TableCell>
                      <TableCell className="text-center border-r">{(Number(item.with_stone_weight) || 0).toFixed(3)}</TableCell>
                      <TableCell className="text-center border-r">{(Number(item.without_stone_weight) || 0).toFixed(3)}</TableCell>
                      <TableCell className="text-center border-r">{(Number(item.with_stone_cost) || 0).toFixed(2)}</TableCell>
                      <TableCell className="text-center border-r">{(Number(item.without_stone_cost) || 0).toFixed(2)}</TableCell>
                      <TableCell className="text-center border-r">{(Number(item.procured_in_24k) || 0).toFixed(3)}</TableCell>
                      <TableCell className="text-center border-r">{(Number(item.stone_weight_22k) || 0).toFixed(3)}</TableCell>
                      <TableCell className="text-center border-r">{(Number(item.sold_gold_weight_22k) || 0).toFixed(3)}</TableCell>
                      <TableCell className="text-center border-r">{(Number(item.sold_gold_weight_24k) || 0).toFixed(3)}</TableCell>
                      <TableCell className="text-center border-r">{(Number(item.balance_gold_weight_22k) || 0).toFixed(3)}</TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center gap-1 justify-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(item)}
                            title="Edit item"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(item)}
                            title="Delete item"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
