const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function auditDatabaseLinks() {
  let connection;

  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME
  };

  try {
    console.log('🔌 Connecting to database...');
    connection = await mysql.createConnection(config);
    console.log(`📊 Connected to database: ${process.env.DB_NAME}`);

    console.log('\n=== DATABASE AUDIT REPORT ===\n');

    // Check all tables exist
    console.log('📋 CHECKING TABLES...');
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ?
      ORDER BY TABLE_NAME
    `, [process.env.DB_NAME]);

    const expectedTables = [
      'suppliers', 'customers', 'inventory', 'bills', 
      'gold_rates', 'users', 'sales_transactions', 'wastage_records'
    ];

    console.log('Expected tables:', expectedTables.length);
    console.log('Found tables:', tables.length);
    console.log();

    expectedTables.forEach(expectedTable => {
      const found = tables.find(t => t.TABLE_NAME === expectedTable);
      if (found) {
        console.log(`✅ ${expectedTable} - ${found.TABLE_ROWS || 0} rows`);
      } else {
        console.log(`❌ ${expectedTable} - MISSING`);
      }
    });

    // Check for unexpected tables
    const unexpectedTables = tables.filter(t => !expectedTables.includes(t.TABLE_NAME));
    if (unexpectedTables.length > 0) {
      console.log('\n⚠️  UNEXPECTED TABLES:');
      unexpectedTables.forEach(t => {
        console.log(`   ${t.TABLE_NAME} - ${t.TABLE_ROWS || 0} rows`);
      });
    }

    // Check foreign key relationships
    console.log('\n🔗 CHECKING FOREIGN KEY RELATIONSHIPS...');
    const [foreignKeys] = await connection.execute(`
      SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        CONSTRAINT_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = ? AND REFERENCED_TABLE_NAME IS NOT NULL
      ORDER BY TABLE_NAME, COLUMN_NAME
    `, [process.env.DB_NAME]);

    const expectedForeignKeys = [
      { table: 'inventory', column: 'supplier_id', references: 'suppliers' },
      { table: 'bills', column: 'customer_id', references: 'customers' },
      { table: 'bills', column: 'inventory_item_id', references: 'inventory' },
      { table: 'sales_transactions', column: 'inventory_id', references: 'inventory' },
      { table: 'sales_transactions', column: 'customer_id', references: 'customers' },
      { table: 'wastage_records', column: 'inventory_id', references: 'inventory' }
    ];

    expectedForeignKeys.forEach(expected => {
      const found = foreignKeys.find(fk => 
        fk.TABLE_NAME === expected.table && 
        fk.COLUMN_NAME === expected.column &&
        fk.REFERENCED_TABLE_NAME === expected.references
      );
      if (found) {
        console.log(`✅ ${expected.table}.${expected.column} → ${expected.references}`);
      } else {
        console.log(`❌ ${expected.table}.${expected.column} → ${expected.references} - MISSING`);
      }
    });

    // Check indexes
    console.log('\n📊 CHECKING INDEXES...');
    const [indexes] = await connection.execute(`
      SELECT 
        TABLE_NAME,
        INDEX_NAME,
        COLUMN_NAME,
        NON_UNIQUE
      FROM INFORMATION_SCHEMA.STATISTICS
      WHERE TABLE_SCHEMA = ? AND INDEX_NAME != 'PRIMARY'
      ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX
    `, [process.env.DB_NAME]);

    const indexesByTable = {};
    indexes.forEach(idx => {
      if (!indexesByTable[idx.TABLE_NAME]) {
        indexesByTable[idx.TABLE_NAME] = [];
      }
      indexesByTable[idx.TABLE_NAME].push(idx);
    });

    Object.keys(indexesByTable).forEach(tableName => {
      const tableIndexes = indexesByTable[tableName];
      const uniqueIndexes = [...new Set(tableIndexes.map(i => i.INDEX_NAME))];
      console.log(`📊 ${tableName}: ${uniqueIndexes.length} indexes`);
    });

    // Check data integrity
    console.log('\n🔍 CHECKING DATA INTEGRITY...');
    
    // Check for orphaned records
    const integrityChecks = [
      {
        name: 'Inventory with invalid supplier_id',
        query: `SELECT COUNT(*) as count FROM inventory i 
                LEFT JOIN suppliers s ON i.supplier_id = s.id 
                WHERE i.supplier_id IS NOT NULL AND s.id IS NULL`
      },
      {
        name: 'Bills with invalid customer_id',
        query: `SELECT COUNT(*) as count FROM bills b 
                LEFT JOIN customers c ON b.customer_id = c.id 
                WHERE b.customer_id IS NOT NULL AND c.id IS NULL`
      },
      {
        name: 'Bills with invalid inventory_item_id',
        query: `SELECT COUNT(*) as count FROM bills b 
                LEFT JOIN inventory i ON b.inventory_item_id = i.id 
                WHERE b.inventory_item_id IS NOT NULL AND i.id IS NULL`
      },
      {
        name: 'Sales transactions with invalid inventory_id',
        query: `SELECT COUNT(*) as count FROM sales_transactions st 
                LEFT JOIN inventory i ON st.inventory_id = i.id 
                WHERE st.inventory_id IS NOT NULL AND i.id IS NULL`
      }
    ];

    for (const check of integrityChecks) {
      try {
        const [result] = await connection.execute(check.query);
        const count = result[0].count;
        if (count > 0) {
          console.log(`❌ ${check.name}: ${count} orphaned records`);
        } else {
          console.log(`✅ ${check.name}: OK`);
        }
      } catch (error) {
        console.log(`⚠️  ${check.name}: Could not check (table may not exist)`);
      }
    }

    // API Endpoints Status
    console.log('\n🌐 API ENDPOINTS STATUS...');
    const apiEndpoints = [
      '/api/suppliers',
      '/api/customers', 
      '/api/inventory',
      '/api/bills',
      '/api/gold-rates',
      '/api/users',
      '/api/sales',
      '/api/wastage',
      '/api/backup',
      '/api/config',
      '/api/test-db'
    ];

    console.log(`📡 Expected API endpoints: ${apiEndpoints.length}`);
    apiEndpoints.forEach(endpoint => {
      console.log(`   ${endpoint}`);
    });

    // Component Database Integration Status
    console.log('\n🧩 COMPONENT DATABASE INTEGRATION...');
    const components = [
      { name: 'Dashboard', tables: ['inventory', 'bills', 'customers', 'suppliers', 'gold_rates'] },
      { name: 'Inventory Management', tables: ['inventory', 'suppliers'] },
      { name: 'Customer Management', tables: ['customers'] },
      { name: 'Supplier Management', tables: ['suppliers'] },
      { name: 'User Management', tables: ['users'] },
      { name: 'Billing System', tables: ['bills', 'customers', 'inventory'] },
      { name: 'Gold Rate Tracker', tables: ['gold_rates'] },
      { name: 'Sales Wastage Management', tables: ['sales_transactions', 'wastage_records', 'inventory'] },
      { name: 'Reports', tables: ['bills', 'inventory', 'suppliers', 'customers'] },
      { name: 'Business Settings', tables: [] }, // Uses localStorage
      { name: 'Backup Restore', tables: ['all'] }
    ];

    components.forEach(component => {
      console.log(`🧩 ${component.name}:`);
      if (component.tables.length === 0) {
        console.log(`   📊 Uses localStorage/settings`);
      } else if (component.tables.includes('all')) {
        console.log(`   📊 Uses all database tables`);
      } else {
        component.tables.forEach(table => {
          const tableExists = tables.find(t => t.TABLE_NAME === table);
          if (tableExists) {
            console.log(`   ✅ ${table}`);
          } else {
            console.log(`   ❌ ${table} - MISSING`);
          }
        });
      }
    });

    console.log('\n=== AUDIT COMPLETE ===');
    console.log(`📊 Database: ${process.env.DB_NAME}`);
    console.log(`🏗️  Tables: ${tables.length}/${expectedTables.length}`);
    console.log(`🔗 Foreign Keys: ${foreignKeys.length}`);
    console.log(`📊 Indexes: ${Object.keys(indexesByTable).length} tables indexed`);

  } catch (error) {
    console.error('❌ Error during audit:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the audit
auditDatabaseLinks();
