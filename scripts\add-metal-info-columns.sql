-- Add metal information columns to inventory table
-- Run this script manually in your MySQL database

USE jewellery_wholesale_software;

-- Add new columns for metal information
ALTER TABLE inventory 
ADD COLUMN metal_type ENUM('Gold', 'Silver', 'Platinum') DEFAULT 'Gold' AFTER product_type,
ADD COLUMN form_type ENUM('Bar', 'Jewel', 'Old Jewel') DEFAULT 'Jewel' AFTER metal_type,
ADD COLUMN jewel_type ENUM('With Stone', 'Without Stone') NULL AFTER form_type,
ADD COLUMN jewel_category VARCHAR(100) NULL AFTER jewel_type;

-- Update existing records with default values based on existing data
UPDATE inventory 
SET metal_type = 'Gold', 
    form_type = 'Jewel',
    jewel_type = CASE 
      WHEN with_stone_weight > 0 THEN 'With Stone' 
      ELSE 'Without Stone' 
    END,
    jewel_category = CASE 
      WHEN product_name LIKE '%Chain%' THEN 'Chain'
      WHEN product_name LIKE '%Bangle%' THEN 'Bangle'
      WHEN product_name LIKE '%Ring%' THEN 'Ring'
      WHEN product_name LIKE '%Necklace%' THEN 'Necklace'
      WHEN product_name LIKE '%Stud%' THEN 'Studs'
      WHEN product_name LIKE '%Pendant%' THEN 'Pendant'
      WHEN product_name LIKE '%Bracelet%' THEN 'Bracelet'
      WHEN product_name LIKE '%Mangalsutra%' THEN 'Mangalsutra'
      WHEN product_name LIKE '%Nose%' THEN 'Nosepin'
      WHEN product_name LIKE '%Vaddanam%' THEN 'Vaddanam'
      WHEN product_name LIKE '%Choker%' THEN 'Choker'
      WHEN product_name LIKE '%Earring%' THEN 'Earrings'
      WHEN product_name LIKE '%Haram%' THEN 'Haram'
      WHEN product_name LIKE '%Anklet%' THEN 'Anklet'
      ELSE 'Others'
    END
WHERE metal_type IS NULL OR form_type IS NULL;

-- Show updated table structure
DESCRIBE inventory;
