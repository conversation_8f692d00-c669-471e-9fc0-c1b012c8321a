-- Insert sample data for Jewellery Wholesale Management System

-- Insert suppliers
INSERT INTO suppliers (name, location, contact_person, phone, email, address, speciality, total_purchases, last_purchase_date, status) VALUES
('Emerald Jewel Industry', 'Coimbatore', '<PERSON><PERSON>', '+91 98765 43210', 'r<PERSON><PERSON>@emeraldjewel.com', '123, Jewelry Street, Coimbatore, Tamil Nadu', 'Gold Chains, Necklaces', 2500000.00, '2024-01-15', 'Active'),
('Nala Gold', 'Mumbai', '<PERSON><PERSON> Sharma', '+91 87654 32109', '<EMAIL>', '456, Gold Market, Mumbai, Maharashtra', 'Gold Bangles, Rings', 3200000.00, '2024-01-16', 'Active'),
('SSJ', 'Surat', '<PERSON><PERSON>', '+91 76543 21098', '<EMAIL>', '789, Diamond Plaza, Surat, Gujarat', 'Diamond Jewelry, Studs', 1800000.00, '2024-01-17', 'Active');

-- Insert customers (updated with per-gram rate totals)
INSERT INTO customers (name, location, contact_person, phone, email, address, total_purchases, last_purchase_date) VALUES
('VS Jewellery', '<PERSON>ma<PERSON>', 'Venkatesh', '+91 99887 76655', '<EMAIL>', '12, Main Street, Nammakal, Tamil Nadu', 9890.16, '2024-01-15'),
('Krishna Jewels', 'Valapadi', 'Krishna Murthy', '+91 88776 65544', '<EMAIL>', '34, Temple Road, Valapadi, Tamil Nadu', 31918.29, '2024-01-16'),
('Dhanapal Jewels', 'Erode', 'Dhanapal', '+91 77665 54433', '<EMAIL>', '56, Market Street, Erode, Tamil Nadu', 4995.53, '2024-01-17');

-- Insert inventory items
INSERT INTO inventory (supplier_id, product_name, product_type, with_stone_weight, without_stone_weight, with_stone_cost, without_stone_cost, procured_in_24k, sold_value_with_stone, sold_value_without_stone, balance_weight_24k, balance_weight_22k) VALUES
(1, 'Chain', 'Gold Chain', 0.000, 120.420, 93.00, 94.00, 113.195, 0.000, 10.160, 9.754, 110.260),
(2, 'Bangles', 'Gold Bangles', 0.000, 246.340, 94.00, 96.00, 236.486, 0.000, 32.120, 31.478, 214.220),
(3, 'Studs', 'Diamond Studs', 110.325, 89.120, 95.00, 99.00, 193.038, 1.560, 2.010, 3.388, 195.875);

-- Insert bills (with per-gram rates)
INSERT INTO bills (customer_id, bill_number, product_name, product_type, with_stone, without_stone, gross_weight, stone_weight, net_weight, tunch_with_stone, tunch_without_stone, weight_in_24k, gold_24k_price, stone_price, total_amount, status, bill_date) VALUES
(1, 'INV001', 'Chain', 'Gold Chain', 0.000, 10.160, 10.160, 0.000, 10.160, 0, 96, 9.754, 1014.00, 0.00, 9890.16, 'Completed', '2024-01-15'),
(2, 'INV002', 'Bangles', 'Gold Bangles', 0.000, 32.120, 32.120, 0.000, 32.120, 0, 98, 31.478, 1014.00, 0.00, 31918.29, 'Pending', '2024-01-16'),
(3, 'INV003', 'Studs', 'Diamond Studs', 1.560, 2.010, 3.570, 0.160, 3.410, 97, 101, 3.388, 1014.00, 1600.00, 4995.53, 'Completed', '2024-01-17');

-- Insert current gold rates (per gram)
INSERT INTO gold_rates (rate_24k, rate_22k, rate_date) VALUES
(1014.00, 929.50, CURRENT_DATE);
