const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, '..', 'database.db');

console.log('📦 INVENTORY BALANCE CHECK');
console.log('==========================\n');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    return;
  }
  console.log('✅ Connected to SQLite database\n');
});

// Check inventory items and their balance
db.all(`
  SELECT 
    id,
    product_name,
    product_type,
    balance_gold_weight_22k,
    sold_gold_weight_22k,
    balance_weight_24k,
    balance_weight_22k,
    status,
    created_at
  FROM inventory 
  ORDER BY created_at DESC
`, [], (err, rows) => {
  if (err) {
    console.error('❌ Error fetching inventory:', err.message);
    return;
  }

  console.log('📦 INVENTORY ITEMS:');
  console.log('==================');
  console.log('ID | Product Name      | Type          | Available | Sold     | 24K Bal  | 22K Bal  | Status    ');
  console.log('---|-------------------|---------------|-----------|----------|----------|----------|----------');
  
  if (rows.length === 0) {
    console.log('No inventory items found.');
  } else {
    rows.forEach(item => {
      const id = String(item.id).padEnd(2);
      const name = String(item.product_name || '').padEnd(17);
      const type = String(item.product_type || '').padEnd(13);
      const available = String((Number(item.balance_gold_weight_22k) || 0).toFixed(3) + 'g').padEnd(9);
      const sold = String((Number(item.sold_gold_weight_22k) || 0).toFixed(3) + 'g').padEnd(8);
      const bal24k = String((Number(item.balance_weight_24k) || 0).toFixed(3) + 'g').padEnd(8);
      const bal22k = String((Number(item.balance_weight_22k) || 0).toFixed(3) + 'g').padEnd(8);
      const status = String(item.status || '').padEnd(9);
      
      console.log(`${id} | ${name} | ${type} | ${available} | ${sold} | ${bal24k} | ${bal22k} | ${status}`);
    });
  }
  
  console.log('\n');
});

// Check recent bills to see what was sold
db.all(`
  SELECT 
    id,
    bill_number,
    product_name,
    without_stone,
    weight_in_24k,
    total_amount,
    status,
    created_at
  FROM bills 
  ORDER BY created_at DESC
  LIMIT 10
`, [], (err, rows) => {
  if (err) {
    console.error('❌ Error fetching bills:', err.message);
    return;
  }

  console.log('🧾 RECENT BILLS:');
  console.log('================');
  console.log('ID | Bill Number  | Product Name      | Sold Wt  | 24K Wt   | Total Amount | Status   ');
  console.log('---|--------------|-------------------|----------|----------|--------------|----------');
  
  if (rows.length === 0) {
    console.log('No bills found.');
  } else {
    rows.forEach(bill => {
      const id = String(bill.id).padEnd(2);
      const billNum = String(bill.bill_number || '').padEnd(12);
      const product = String(bill.product_name || '').padEnd(17);
      const soldWt = String((Number(bill.without_stone) || 0).toFixed(3) + 'g').padEnd(8);
      const weight24k = String((Number(bill.weight_in_24k) || 0).toFixed(3) + 'g').padEnd(8);
      const amount = String('₹' + (Number(bill.total_amount) || 0).toLocaleString('en-IN')).padEnd(12);
      const status = String(bill.status || '').padEnd(9);
      
      console.log(`${id} | ${billNum} | ${product} | ${soldWt} | ${weight24k} | ${amount} | ${status}`);
    });
  }
  
  console.log('\n');
});

// Check if inventory balance is being updated correctly
db.all(`
  SELECT 
    i.id,
    i.product_name,
    i.balance_gold_weight_22k as current_balance,
    COALESCE(SUM(b.without_stone), 0) as total_sold_from_bills,
    i.sold_gold_weight_22k as recorded_sold,
    (i.balance_gold_weight_22k + COALESCE(SUM(b.without_stone), 0)) as calculated_original
  FROM inventory i
  LEFT JOIN bills b ON i.id = b.inventory_item_id
  GROUP BY i.id, i.product_name, i.balance_gold_weight_22k, i.sold_gold_weight_22k
`, [], (err, rows) => {
  if (err) {
    console.error('❌ Error checking balance consistency:', err.message);
    return;
  }

  console.log('🔍 INVENTORY BALANCE CONSISTENCY CHECK:');
  console.log('======================================');
  console.log('ID | Product Name      | Current Bal | Bills Sold | Recorded Sold | Calc Original');
  console.log('---|-------------------|-------------|------------|---------------|---------------');
  
  if (rows.length === 0) {
    console.log('No inventory items to check.');
  } else {
    rows.forEach(item => {
      const id = String(item.id).padEnd(2);
      const name = String(item.product_name || '').padEnd(17);
      const currentBal = String((Number(item.current_balance) || 0).toFixed(3) + 'g').padEnd(11);
      const billsSold = String((Number(item.total_sold_from_bills) || 0).toFixed(3) + 'g').padEnd(10);
      const recordedSold = String((Number(item.recorded_sold) || 0).toFixed(3) + 'g').padEnd(13);
      const calcOriginal = String((Number(item.calculated_original) || 0).toFixed(3) + 'g').padEnd(14);
      
      console.log(`${id} | ${name} | ${currentBal} | ${billsSold} | ${recordedSold} | ${calcOriginal}`);
      
      // Check for inconsistencies
      const billsSoldNum = Number(item.total_sold_from_bills) || 0;
      const recordedSoldNum = Number(item.recorded_sold) || 0;
      
      if (Math.abs(billsSoldNum - recordedSoldNum) > 0.001) {
        console.log(`   ⚠️  INCONSISTENCY: Bills show ${billsSoldNum.toFixed(3)}g sold, but inventory records ${recordedSoldNum.toFixed(3)}g`);
      }
    });
  }
  
  console.log('\n');
  
  db.close((err) => {
    if (err) {
      console.error('❌ Error closing database:', err.message);
    } else {
      console.log('✅ Database connection closed');
      console.log('\n🎯 SUMMARY:');
      console.log('===========');
      console.log('• Check if inventory balance reduces after bill creation');
      console.log('• Verify sold amounts are properly recorded');
      console.log('• Look for any inconsistencies between bills and inventory');
      console.log('• Ensure status updates correctly (Available/Low Stock/Out of Stock)');
    }
  });
});
