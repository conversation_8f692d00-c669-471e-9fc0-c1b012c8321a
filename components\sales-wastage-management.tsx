"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AlertCircle, TrendingDown, TrendingUp, Package, Trash2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"
import { useBusinessSettings } from "@/hooks/use-database-settings"
import { BusinessLogic } from "@/lib/utils/business-logic"

interface InventoryItem {
  id: number
  product_name: string
  supplier_name: string
  procured_in_24k: number
  balance_weight_24k: number
  balance_weight_22k: number
  sold_value_24k: number
  sold_value_22k: number
}

interface SalesTransaction {
  id: number
  transaction_type: string
  weight_24k: number
  weight_22k: number
  total_amount: number
  transaction_date: string
  customer_name?: string
  bill_number?: string
}

interface WastageRecord {
  id: number
  wastage_type: string
  weight_24k: number
  weight_22k: number
  wastage_percentage: number
  reason?: string
  recorded_date: string
}

export default function SalesWastageManagement() {
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null)
  const [sales, setSales] = useState<SalesTransaction[]>([])
  const [wastage, setWastage] = useState<WastageRecord[]>([])
  const [customers, setCustomers] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")

  // Sales form state
  const [salesForm, setSalesForm] = useState({
    customer_id: "",
    weight_24k: "",
    weight_22k: "",
    rate_24k: "",
    rate_22k: "",
    making_charges: "",
    stone_weight: "",
    stone_value: "",
    bill_number: "",
    notes: ""
  })

  // Wastage form state
  const [wastageForm, setWastageForm] = useState({
    wastage_type: "Processing",
    weight_24k: "",
    weight_22k: "",
    reason: "",
    process_stage: "",
    recovered_weight: "",
    notes: ""
  })

  useEffect(() => {
    fetchInventory()
    fetchCustomers()
  }, [])

  useEffect(() => {
    if (selectedItem) {
      fetchSalesData()
      fetchWastageData()
    }
  }, [selectedItem])

  const fetchInventory = async () => {
    try {
      const response = await fetch("/api/inventory")
      const data = await response.json()
      setInventory(Array.isArray(data) ? data : [])
    } catch (error) {
      setError("Failed to fetch inventory")
      setInventory([])
    }
  }

  const fetchCustomers = async () => {
    try {
      const response = await fetch("/api/customers")
      const data = await response.json()
      setCustomers(data)
    } catch (error) {
      console.error("Failed to fetch customers:", error)
    }
  }

  const fetchSalesData = async () => {
    if (!selectedItem) return
    try {
      const response = await fetch(`/api/sales?inventory_id=${selectedItem.id}`)
      const data = await response.json()
      setSales(data)
    } catch (error) {
      setError("Failed to fetch sales data")
    }
  }

  const fetchWastageData = async () => {
    if (!selectedItem) return
    try {
      const response = await fetch(`/api/wastage?inventory_id=${selectedItem.id}`)
      const data = await response.json()
      setWastage(data)
    } catch (error) {
      setError("Failed to fetch wastage data")
    }
  }

  const handleSalesSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedItem) return

    setLoading(true)
    try {
      const response = await fetch("/api/sales", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          inventory_id: selectedItem.id,
          customer_id: salesForm.customer_id ? parseInt(salesForm.customer_id) : undefined,
          weight_24k: parseFloat(salesForm.weight_24k) || 0,
          weight_22k: parseFloat(salesForm.weight_22k) || 0,
          rate_24k: parseFloat(salesForm.rate_24k) || 0,
          rate_22k: parseFloat(salesForm.rate_22k) || 0,
          making_charges: parseFloat(salesForm.making_charges) || 0,
          stone_weight: parseFloat(salesForm.stone_weight) || 0,
          stone_value: parseFloat(salesForm.stone_value) || 0,
          bill_number: salesForm.bill_number,
          notes: salesForm.notes
        })
      })

      if (response.ok) {
        setSalesForm({
          customer_id: "",
          weight_24k: "",
          weight_22k: "",
          rate_24k: "",
          rate_22k: "",
          making_charges: "",
          stone_weight: "",
          stone_value: "",
          bill_number: "",
          notes: ""
        })
        fetchSalesData()
        fetchInventory() // Refresh to get updated balances
      } else {
        setError("Failed to record sale")
      }
    } catch (error) {
      setError("Failed to record sale")
    } finally {
      setLoading(false)
    }
  }

  const handleWastageSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedItem) return

    setLoading(true)
    try {
      const response = await fetch("/api/wastage", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          inventory_id: selectedItem.id,
          wastage_type: wastageForm.wastage_type,
          weight_24k: parseFloat(wastageForm.weight_24k) || 0,
          weight_22k: parseFloat(wastageForm.weight_22k) || 0,
          reason: wastageForm.reason,
          process_stage: wastageForm.process_stage,
          recovered_weight: parseFloat(wastageForm.recovered_weight) || 0,
          notes: wastageForm.notes
        })
      })

      if (response.ok) {
        setWastageForm({
          wastage_type: "Processing",
          weight_24k: "",
          weight_22k: "",
          reason: "",
          process_stage: "",
          recovered_weight: "",
          notes: ""
        })
        fetchWastageData()
        fetchInventory() // Refresh to get updated balances
      } else {
        setError("Failed to record wastage")
      }
    } catch (error) {
      setError("Failed to record wastage")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Sales & Wastage Management</h2>
          <p className="text-muted-foreground">
            Track sales transactions and wastage records for inventory items
          </p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Inventory Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Select Inventory Item
            </CardTitle>
            <CardDescription>
              Choose an inventory item to manage sales and wastage
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {loading ? (
              <div className="text-center py-4">
                <p className="text-muted-foreground">Loading inventory...</p>
              </div>
            ) : inventory.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-muted-foreground">No inventory items found</p>
              </div>
            ) : (
              inventory.map((item) => (
                <div
                  key={item.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedItem?.id === item.id
                      ? "border-primary bg-primary/5"
                      : "border-border hover:border-primary/50"
                  }`}
                  onClick={() => setSelectedItem(item)}
                >
                  <div className="font-medium">{item.product_name}</div>
                  <div className="text-sm text-muted-foreground">{item.supplier_name}</div>
                  <div className="flex justify-between text-xs mt-2">
                    <span>Balance 24K: {(item.balance_weight_24k || 0).toFixed(3)}g</span>
                    <span>Balance 22K: {(item.balance_weight_22k || 0).toFixed(3)}g</span>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        {/* Sales & Wastage Forms */}
        <Card className="lg:col-span-2">
          {selectedItem ? (
            <>
              <CardHeader>
                <CardTitle>{selectedItem.product_name}</CardTitle>
                <CardDescription>
                  Supplier: {selectedItem.supplier_name} | 
                  Procured: {selectedItem.procured_in_24k}g (24K) | 
                  Balance: {selectedItem.balance_weight_24k}g (24K), {selectedItem.balance_weight_22k}g (22K)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="sales" className="space-y-4">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="sales">Record Sale</TabsTrigger>
                    <TabsTrigger value="wastage">Record Wastage</TabsTrigger>
                    <TabsTrigger value="history">Transaction History</TabsTrigger>
                  </TabsList>

                  <TabsContent value="sales" className="space-y-4">
                    <form onSubmit={handleSalesSubmit} className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="customer">Customer</Label>
                          <Select
                            value={salesForm.customer_id}
                            onValueChange={(value) => setSalesForm({ ...salesForm, customer_id: value })}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select customer" />
                            </SelectTrigger>
                            <SelectContent>
                              {customers.map((customer) => (
                                <SelectItem key={customer.id} value={customer.id.toString()}>
                                  {customer.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="bill_number">Bill Number</Label>
                          <Input
                            id="bill_number"
                            value={salesForm.bill_number}
                            onChange={(e) => setSalesForm({ ...salesForm, bill_number: e.target.value })}
                            placeholder="Enter bill number"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="weight_24k">Weight Sold (24K) *</Label>
                          <Input
                            id="weight_24k"
                            type="number"
                            step="0.001"
                            value={salesForm.weight_24k}
                            onChange={(e) => setSalesForm({ ...salesForm, weight_24k: e.target.value })}
                            placeholder="0.000"
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="weight_22k">Weight Sold (22K)</Label>
                          <Input
                            id="weight_22k"
                            type="number"
                            step="0.001"
                            value={salesForm.weight_22k}
                            onChange={(e) => setSalesForm({ ...salesForm, weight_22k: e.target.value })}
                            placeholder="0.000"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="rate_24k">Rate 24K (₹/g) *</Label>
                          <Input
                            id="rate_24k"
                            type="number"
                            step="0.01"
                            value={salesForm.rate_24k}
                            onChange={(e) => setSalesForm({ ...salesForm, rate_24k: e.target.value })}
                            placeholder="0.00"
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="rate_22k">Rate 22K (₹/g)</Label>
                          <Input
                            id="rate_22k"
                            type="number"
                            step="0.01"
                            value={salesForm.rate_22k}
                            onChange={(e) => setSalesForm({ ...salesForm, rate_22k: e.target.value })}
                            placeholder="0.00"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="making_charges">Making Charges (₹)</Label>
                          <Input
                            id="making_charges"
                            type="number"
                            step="0.01"
                            value={salesForm.making_charges}
                            onChange={(e) => setSalesForm({ ...salesForm, making_charges: e.target.value })}
                            placeholder="0.00"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="stone_weight">Stone Weight (g)</Label>
                          <Input
                            id="stone_weight"
                            type="number"
                            step="0.001"
                            value={salesForm.stone_weight}
                            onChange={(e) => setSalesForm({ ...salesForm, stone_weight: e.target.value })}
                            placeholder="0.000"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="stone_value">Stone Value (₹)</Label>
                          <Input
                            id="stone_value"
                            type="number"
                            step="0.01"
                            value={salesForm.stone_value}
                            onChange={(e) => setSalesForm({ ...salesForm, stone_value: e.target.value })}
                            placeholder="0.00"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="notes">Notes</Label>
                        <Input
                          id="notes"
                          value={salesForm.notes}
                          onChange={(e) => setSalesForm({ ...salesForm, notes: e.target.value })}
                          placeholder="Additional notes"
                        />
                      </div>

                      <Button type="submit" disabled={loading} className="w-full">
                        {loading ? "Recording Sale..." : "Record Sale"}
                      </Button>
                    </form>
                  </TabsContent>

                  <TabsContent value="wastage" className="space-y-4">
                    <form onSubmit={handleWastageSubmit} className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="wastage_type">Wastage Type *</Label>
                          <Select
                            value={wastageForm.wastage_type}
                            onValueChange={(value) => setWastageForm({ ...wastageForm, wastage_type: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Processing">Processing</SelectItem>
                              <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                              <SelectItem value="Refining">Refining</SelectItem>
                              <SelectItem value="Loss">Loss</SelectItem>
                              <SelectItem value="Other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="process_stage">Process Stage</Label>
                          <Input
                            id="process_stage"
                            value={wastageForm.process_stage}
                            onChange={(e) => setWastageForm({ ...wastageForm, process_stage: e.target.value })}
                            placeholder="e.g., Melting, Casting"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="wastage_weight_24k">Wastage 24K (g) *</Label>
                          <Input
                            id="wastage_weight_24k"
                            type="number"
                            step="0.001"
                            value={wastageForm.weight_24k}
                            onChange={(e) => setWastageForm({ ...wastageForm, weight_24k: e.target.value })}
                            placeholder="0.000"
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="wastage_weight_22k">Wastage 22K (g)</Label>
                          <Input
                            id="wastage_weight_22k"
                            type="number"
                            step="0.001"
                            value={wastageForm.weight_22k}
                            onChange={(e) => setWastageForm({ ...wastageForm, weight_22k: e.target.value })}
                            placeholder="0.000"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="recovered_weight">Recovered Weight (g)</Label>
                          <Input
                            id="recovered_weight"
                            type="number"
                            step="0.001"
                            value={wastageForm.recovered_weight}
                            onChange={(e) => setWastageForm({ ...wastageForm, recovered_weight: e.target.value })}
                            placeholder="0.000"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="wastage_reason">Reason</Label>
                        <Input
                          id="wastage_reason"
                          value={wastageForm.reason}
                          onChange={(e) => setWastageForm({ ...wastageForm, reason: e.target.value })}
                          placeholder="Reason for wastage"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="wastage_notes">Notes</Label>
                        <Input
                          id="wastage_notes"
                          value={wastageForm.notes}
                          onChange={(e) => setWastageForm({ ...wastageForm, notes: e.target.value })}
                          placeholder="Additional notes"
                        />
                      </div>

                      <Button type="submit" disabled={loading} className="w-full">
                        {loading ? "Recording Wastage..." : "Record Wastage"}
                      </Button>
                    </form>
                  </TabsContent>

                  <TabsContent value="history" className="space-y-4">
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold flex items-center gap-2 mb-3">
                          <TrendingUp className="h-4 w-4" />
                          Sales Transactions
                        </h4>
                        {sales.length > 0 ? (
                          <div className="space-y-2">
                            {sales.map((sale) => (
                              <div key={sale.id} className="p-3 border rounded-lg">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <div className="font-medium">
                                      {sale.weight_24k}g (24K) + {sale.weight_22k}g (22K)
                                    </div>
                                    <div className="text-sm text-muted-foreground">
                                      {sale.customer_name || "Walk-in Customer"} • {sale.bill_number}
                                    </div>
                                  </div>
                                  <div className="text-right">
                                    <div className="font-medium">₹{sale.total_amount.toFixed(2)}</div>
                                    <div className="text-sm text-muted-foreground">
                                      {new Date(sale.transaction_date).toLocaleDateString()}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-muted-foreground">No sales transactions recorded</p>
                        )}
                      </div>

                      <Separator />

                      <div>
                        <h4 className="font-semibold flex items-center gap-2 mb-3">
                          <TrendingDown className="h-4 w-4" />
                          Wastage Records
                        </h4>
                        {wastage.length > 0 ? (
                          <div className="space-y-2">
                            {wastage.map((record) => (
                              <div key={record.id} className="p-3 border rounded-lg">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <div className="font-medium">
                                      {record.weight_24k}g (24K) + {record.weight_22k}g (22K)
                                    </div>
                                    <div className="text-sm text-muted-foreground">
                                      {record.wastage_type} • {record.reason}
                                    </div>
                                  </div>
                                  <div className="text-right">
                                    <Badge variant="destructive">{record.wastage_percentage}%</Badge>
                                    <div className="text-sm text-muted-foreground">
                                      {new Date(record.recorded_date).toLocaleDateString()}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-muted-foreground">No wastage records found</p>
                        )}
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </>
          ) : (
            <CardContent className="flex items-center justify-center h-64">
              <p className="text-muted-foreground">Select an inventory item to manage sales and wastage</p>
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  )
}
