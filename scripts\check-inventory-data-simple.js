const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkInventoryData() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME
    });

    console.log('Connected to database');

    // Get inventory count
    const [countResult] = await connection.execute('SELECT COUNT(*) as count FROM inventory');
    console.log(`Total inventory items: ${countResult[0].count}`);

    if (countResult[0].count > 0) {
      // Get first few items
      const [items] = await connection.execute(`
        SELECT i.id, i.product_name, i.metal_type, i.form_type, s.name as supplier_name
        FROM inventory i
        LEFT JOIN suppliers s ON i.supplier_id = s.id
        LIMIT 5
      `);

      console.log('\nFirst 5 inventory items:');
      items.forEach(item => {
        console.log(`ID: ${item.id}, Product: ${item.product_name}, Metal: ${item.metal_type}, Supplier: ${item.supplier_name}`);
      });
    } else {
      console.log('No inventory items found');
    }

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
checkInventoryData();
