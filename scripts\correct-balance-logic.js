const mysql = require('mysql2/promise');
require('dotenv').config();

async function correctBalanceLogic() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jewellery_wholesale_software'
    });

    console.log('Connected to database');

    // Find the chain record
    const [records] = await connection.execute(`
      SELECT i.*, s.name as supplier_name
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      WHERE i.product_name LIKE '%chain%' 
      AND s.name LIKE '%emerald%'
    `);

    if (records.length === 0) {
      console.log('❌ Record not found');
      return;
    }

    const record = records[0];
    console.log('✅ Found record:', record.product_name);

    console.log('\n=== ANALYZING PHYSICAL RECORD LOGIC ===');
    console.log('Physical Record Values:');
    console.log('- Procured in 24K: 113.195g');
    console.log('- Sold Gold Weight in 24K: 9.754g');
    console.log('- Sold Gold Weight in 22K: 10.160g');
    console.log('- Balance in Stock (22K): 110.260g');

    console.log('\n=== UNDERSTANDING THE BUSINESS LOGIC ===');
    console.log('From your physical record, I can deduce:');
    
    // The balance should be the remaining stock after sales
    // If procured 113.195g in 24K and sold 9.754g in 24K
    // The remaining should be: 113.195 - 9.754 = 103.441g in 24K
    // But your physical record shows balance as 110.260g in 22K
    
    // This suggests that the remaining gold is stored/measured in 22K purity
    // Let's check: 103.441g (24K) = ? in 22K
    const remaining24k = 113.195 - 9.754;
    const remaining22k = remaining24k * 0.916; // Convert 24K to 22K
    
    console.log(`Calculated remaining 24K: ${remaining24k.toFixed(3)}g`);
    console.log(`Calculated remaining 22K: ${remaining22k.toFixed(3)}g`);
    console.log(`Physical record balance 22K: 110.260g`);
    
    const difference = Math.abs(remaining22k - 110.260);
    console.log(`Difference: ${difference.toFixed(3)}g`);

    if (difference > 1) {
      console.log('\n⚠️  SIGNIFICANT DIFFERENCE DETECTED');
      console.log('This suggests one of the following:');
      console.log('1. There might be additional sales not recorded');
      console.log('2. The conversion factor might be different');
      console.log('3. There might be wastage/loss during processing');
      console.log('4. The physical record uses different calculation method');
    }

    // Let's update with the exact physical record values
    console.log('\n=== UPDATING WITH EXACT PHYSICAL VALUES ===');
    await connection.execute(`
      UPDATE inventory 
      SET 
        sold_value_24k = 9.754,
        sold_value_22k = 10.160,
        balance_weight_24k = ?,
        balance_weight_22k = 110.260,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      remaining24k, // Calculated remaining in 24K
      record.id
    ]);

    console.log('✅ Updated with exact physical record values');

    // Verify final values
    const [finalRecord] = await connection.execute(`
      SELECT * FROM inventory WHERE id = ?
    `, [record.id]);

    const final = finalRecord[0];
    console.log('\n=== FINAL DATABASE VALUES ===');
    console.log(`Procured in 24K: ${final.procured_in_24k}g`);
    console.log(`Sold in 24K: ${final.sold_value_24k}g`);
    console.log(`Sold in 22K: ${final.sold_value_22k}g`);
    console.log(`Balance 24K: ${final.balance_weight_24k}g`);
    console.log(`Balance 22K: ${final.balance_weight_22k}g`);

    console.log('\n=== RECOMMENDATION ===');
    console.log('The database now matches your physical record exactly.');
    console.log('For future entries, please clarify:');
    console.log('1. How is the balance calculated in your business?');
    console.log('2. Is there wastage/processing loss to account for?');
    console.log('3. What is the exact conversion factor used?');

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
correctBalanceLogic();
