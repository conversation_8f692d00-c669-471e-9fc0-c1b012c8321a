const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function checkCurrentWastageRates() {
  let connection;

  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME
  };

  try {
    console.log('🔌 Connecting to database...');
    connection = await mysql.createConnection(config);
    console.log(`📊 Connected to database: ${process.env.DB_NAME}`);

    console.log('\n=== CURRENT WASTAGE RATES ===\n');

    // Check current wastage rates in database
    const [wastageSettings] = await connection.execute(`
      SELECT setting_key, setting_value, description, updated_at, updated_by
      FROM settings 
      WHERE category = 'wastage'
      ORDER BY setting_key
    `);

    console.log('📊 Wastage Settings in Database:');
    wastageSettings.forEach(setting => {
      let value;
      try {
        value = typeof setting.setting_value === 'string' 
          ? JSON.parse(setting.setting_value) 
          : setting.setting_value;
      } catch (error) {
        value = setting.setting_value;
      }
      
      console.log(`   ${setting.setting_key}: ${value}%`);
      console.log(`      Description: ${setting.description}`);
      console.log(`      Last Updated: ${setting.updated_at} by ${setting.updated_by || 'system'}`);
      console.log('');
    });

    // Test the API endpoint
    console.log('🌐 Testing API Response:');
    try {
      const response = await fetch('http://localhost:3000/api/settings?business=true');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          console.log('✅ Business Settings from API:');
          console.log(`   wastage_rate_bar: ${data.data.wastage_rate_bar}%`);
          console.log(`   wastage_rate_jewel: ${data.data.wastage_rate_jewel}%`);
          console.log(`   wastage_rate_old_jewel: ${data.data.wastage_rate_old_jewel}%`);
        } else {
          console.log('❌ API Error:', data.error);
        }
      } else {
        console.log('❌ API Request failed:', response.status);
      }
    } catch (error) {
      console.log('❌ API Request error:', error.message);
    }

    // Check if there are any user-specific overrides
    console.log('\n👤 Checking User-Specific Overrides:');
    const [userSettings] = await connection.execute(`
      SELECT user_id, setting_key, setting_value, updated_at
      FROM user_settings 
      WHERE category = 'wastage'
      ORDER BY user_id, setting_key
    `);

    if (userSettings.length > 0) {
      console.log('Found user-specific wastage rate overrides:');
      userSettings.forEach(setting => {
        let value;
        try {
          value = typeof setting.setting_value === 'string' 
            ? JSON.parse(setting.setting_value) 
            : setting.setting_value;
        } catch (error) {
          value = setting.setting_value;
        }
        
        console.log(`   User ${setting.user_id}: ${setting.setting_key} = ${value}%`);
      });
    } else {
      console.log('No user-specific wastage rate overrides found');
    }

    console.log('\n=== RECOMMENDATIONS ===');
    
    // Check if any rates are 0 or unusual
    const rates = {};
    wastageSettings.forEach(setting => {
      let value;
      try {
        value = typeof setting.setting_value === 'string' 
          ? JSON.parse(setting.setting_value) 
          : setting.setting_value;
      } catch (error) {
        value = setting.setting_value;
      }
      rates[setting.setting_key] = value;
    });

    if (rates.wastage_rate_bar === 0) {
      console.log('⚠️  wastage_rate_bar is 0% - this might be too low for gold bars');
      console.log('   Recommended: 0.5% - 1.0%');
    }
    
    if (rates.wastage_rate_jewel === 0) {
      console.log('⚠️  wastage_rate_jewel is 0% - this might be too low for jewellery');
      console.log('   Recommended: 2.0% - 3.0%');
    }
    
    if (rates.wastage_rate_old_jewel === 0) {
      console.log('⚠️  wastage_rate_old_jewel is 0% - this might be too low for old jewellery');
      console.log('   Recommended: 3.0% - 5.0%');
    }

    console.log('\n✅ To update wastage rates:');
    console.log('   1. Go to Business Settings page in the application');
    console.log('   2. Update the wastage rates in the Wastage tab');
    console.log('   3. Click Save Settings');
    console.log('   4. Refresh the inventory page to see updated rates');

  } catch (error) {
    console.error('❌ Error checking wastage rates:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the check
checkCurrentWastageRates();
