"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from "recharts"
import { 
  TrendingUp, 
  TrendingDown, 
  Package, 
  DollarSign, 
  AlertTriangle,
  FileText,
  Download
} from "lucide-react"

interface InventoryReport {
  totalItems: number
  totalProcured24k: number
  totalSold24k: number
  totalWastage24k: number
  totalBalance24k: number
  totalValue: number
  byMetalType: Array<{
    metal_type: string
    count: number
    procured: number
    balance: number
    value: number
  }>
  bySupplier: Array<{
    supplier_name: string
    count: number
    procured: number
    balance: number
    value: number
  }>
  lowStockItems: Array<{
    id: number
    product_name: string
    supplier_name: string
    balance_weight_24k: number
    balance_weight_22k: number
  }>
}

interface SalesReport {
  totalTransactions: number
  totalRevenue: number
  totalWeight24k: number
  totalWeight22k: number
  avgRate24k: number
  avgRate22k: number
  byMonth: Array<{
    month: string
    transactions: number
    revenue: number
    weight: number
  }>
  topCustomers: Array<{
    customer_name: string
    transactions: number
    revenue: number
    weight: number
  }>
}

interface WastageReport {
  totalWastage24k: number
  totalWastage22k: number
  totalRecovered: number
  wastageValue: number
  byType: Array<{
    wastage_type: string
    count: number
    weight_24k: number
    weight_22k: number
    percentage: number
  }>
  byMonth: Array<{
    month: string
    wastage: number
    recovered: number
  }>
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

export default function AdvancedReports() {
  const [inventoryReport, setInventoryReport] = useState<InventoryReport | null>(null)
  const [salesReport, setSalesReport] = useState<SalesReport | null>(null)
  const [wastageReport, setWastageReport] = useState<WastageReport | null>(null)
  const [loading, setLoading] = useState(false)
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  })

  useEffect(() => {
    generateReports()
  }, [])

  const generateReports = async () => {
    setLoading(true)
    try {
      // Generate inventory report
      await generateInventoryReport()
      
      // Generate sales report
      await generateSalesReport()
      
      // Generate wastage report
      await generateWastageReport()
      
    } catch (error) {
      console.error("Error generating reports:", error)
    } finally {
      setLoading(false)
    }
  }

  const generateInventoryReport = async () => {
    try {
      const response = await fetch("/api/inventory")
      const inventory = await response.json()

      const report: InventoryReport = {
        totalItems: inventory.length,
        totalProcured24k: inventory.reduce((sum: number, item: any) => sum + (item.procured_in_24k || 0), 0),
        totalSold24k: inventory.reduce((sum: number, item: any) => sum + (item.sold_value_24k || 0), 0),
        totalWastage24k: 0, // This would come from wastage records
        totalBalance24k: inventory.reduce((sum: number, item: any) => sum + (item.balance_weight_24k || 0), 0),
        totalValue: 0, // This would be calculated based on current gold rates
        byMetalType: [],
        bySupplier: [],
        lowStockItems: inventory.filter((item: any) => item.balance_weight_24k < 10)
      }

      // Group by metal type
      const metalGroups = inventory.reduce((acc: any, item: any) => {
        const key = item.metal_type || 'Unknown'
        if (!acc[key]) {
          acc[key] = { metal_type: key, count: 0, procured: 0, balance: 0, value: 0 }
        }
        acc[key].count++
        acc[key].procured += item.procured_in_24k || 0
        acc[key].balance += item.balance_weight_24k || 0
        return acc
      }, {})
      report.byMetalType = Object.values(metalGroups)

      // Group by supplier
      const supplierGroups = inventory.reduce((acc: any, item: any) => {
        const key = item.supplier_name || 'Unknown'
        if (!acc[key]) {
          acc[key] = { supplier_name: key, count: 0, procured: 0, balance: 0, value: 0 }
        }
        acc[key].count++
        acc[key].procured += item.procured_in_24k || 0
        acc[key].balance += item.balance_weight_24k || 0
        return acc
      }, {})
      report.bySupplier = Object.values(supplierGroups)

      setInventoryReport(report)
    } catch (error) {
      console.error("Error generating inventory report:", error)
    }
  }

  const generateSalesReport = async () => {
    try {
      const response = await fetch(`/api/sales?summary=true&start_date=${dateRange.startDate}&end_date=${dateRange.endDate}`)
      const salesData = await response.json()

      const report: SalesReport = {
        totalTransactions: salesData?.total_transactions || 0,
        totalRevenue: salesData?.total_revenue || 0,
        totalWeight24k: salesData?.total_weight_24k || 0,
        totalWeight22k: salesData?.total_weight_22k || 0,
        avgRate24k: salesData?.avg_rate_24k || 0,
        avgRate22k: salesData?.avg_rate_22k || 0,
        byMonth: [], // This would need more detailed data
        topCustomers: [] // This would need more detailed data
      }

      setSalesReport(report)
    } catch (error) {
      console.error("Error generating sales report:", error)
    }
  }

  const generateWastageReport = async () => {
    // This would fetch wastage data from the API
    const report: WastageReport = {
      totalWastage24k: 0,
      totalWastage22k: 0,
      totalRecovered: 0,
      wastageValue: 0,
      byType: [],
      byMonth: []
    }
    setWastageReport(report)
  }

  const exportReport = (reportType: string) => {
    // This would generate and download a PDF or Excel report
    console.log(`Exporting ${reportType} report...`)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Advanced Reports</h2>
          <p className="text-muted-foreground">
            Comprehensive analytics for inventory, sales, and wastage management
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Label htmlFor="startDate">From:</Label>
            <Input
              id="startDate"
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange({ ...dateRange, startDate: e.target.value })}
              className="w-auto"
            />
          </div>
          <div className="flex items-center gap-2">
            <Label htmlFor="endDate">To:</Label>
            <Input
              id="endDate"
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange({ ...dateRange, endDate: e.target.value })}
              className="w-auto"
            />
          </div>
          <Button onClick={generateReports} disabled={loading}>
            {loading ? "Generating..." : "Generate Reports"}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="inventory" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="inventory">Inventory Analysis</TabsTrigger>
          <TabsTrigger value="sales">Sales Performance</TabsTrigger>
          <TabsTrigger value="wastage">Wastage Analysis</TabsTrigger>
          <TabsTrigger value="financial">Financial Summary</TabsTrigger>
        </TabsList>

        <TabsContent value="inventory" className="space-y-4">
          {inventoryReport && (
            <>
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Items</CardTitle>
                    <Package className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{inventoryReport.totalItems}</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Procured (24K)</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{inventoryReport.totalProcured24k.toFixed(3)}g</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Current Balance (24K)</CardTitle>
                    <Package className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{inventoryReport.totalBalance24k.toFixed(3)}g</div>
                    <div className="text-xs text-muted-foreground">
                      {((inventoryReport.totalBalance24k / inventoryReport.totalProcured24k) * 100).toFixed(1)}% of procured
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
                    <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-orange-600">{inventoryReport.lowStockItems.length}</div>
                  </CardContent>
                </Card>
              </div>

              {/* Charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Inventory by Metal Type</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={inventoryReport.byMetalType}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ metal_type, count }) => `${metal_type}: ${count}`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="count"
                        >
                          {inventoryReport.byMetalType.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Balance by Supplier</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={inventoryReport.bySupplier}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="supplier_name" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="balance" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>

              {/* Low Stock Alert */}
              {inventoryReport.lowStockItems.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-orange-500" />
                      Low Stock Alert
                    </CardTitle>
                    <CardDescription>Items with balance less than 10g (24K)</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {inventoryReport.lowStockItems.map((item) => (
                        <div key={item.id} className="flex justify-between items-center p-2 border rounded">
                          <div>
                            <div className="font-medium">{item.product_name}</div>
                            <div className="text-sm text-muted-foreground">{item.supplier_name}</div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium text-orange-600">{item.balance_weight_24k.toFixed(3)}g (24K)</div>
                            <div className="text-sm text-muted-foreground">{item.balance_weight_22k.toFixed(3)}g (22K)</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="sales" className="space-y-4">
          {salesReport && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{salesReport.totalTransactions}</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">₹{salesReport.totalRevenue.toFixed(2)}</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Weight Sold (24K)</CardTitle>
                  <TrendingDown className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{salesReport.totalWeight24k.toFixed(3)}g</div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="wastage" className="space-y-4">
          <div className="text-center py-8">
            <p className="text-muted-foreground">Wastage analysis will be available once wastage records are created.</p>
          </div>
        </TabsContent>

        <TabsContent value="financial" className="space-y-4">
          <div className="text-center py-8">
            <p className="text-muted-foreground">Financial summary will be available with gold rate integration.</p>
          </div>
        </TabsContent>
      </Tabs>

      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Reports
          </CardTitle>
          <CardDescription>Download detailed reports in various formats</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => exportReport('inventory')}>
              Export Inventory Report
            </Button>
            <Button variant="outline" onClick={() => exportReport('sales')}>
              Export Sales Report
            </Button>
            <Button variant="outline" onClick={() => exportReport('wastage')}>
              Export Wastage Report
            </Button>
            <Button variant="outline" onClick={() => exportReport('financial')}>
              Export Financial Summary
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
