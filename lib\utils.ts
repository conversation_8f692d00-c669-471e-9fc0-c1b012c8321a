import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: 2,
  }).format(amount)
}

export function formatWeight(weight: number): string {
  return `${weight.toFixed(3)}g`
}

export function calculateGoldValue(weight24k: number, goldRate: number): number {
  return weight24k * goldRate
}

export function convert22kTo24k(weight22k: number): number {
  return weight22k * 0.916
}

export function convert24kTo22k(weight24k: number): number {
  return weight24k / 0.916
}
