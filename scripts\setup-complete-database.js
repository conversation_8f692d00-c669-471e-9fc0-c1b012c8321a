const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function setupCompleteDatabase() {
  let connection;

  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    multipleStatements: true
  };

  try {
    console.log('🔌 Connecting to MySQL server...');
    connection = await mysql.createConnection(config);

    // Create database if it doesn't exist
    const dbName = process.env.DB_NAME;
    if (!dbName) {
      throw new Error('DB_NAME environment variable is required');
    }

    console.log(`📊 Creating database: ${dbName}`);
    await connection.query(`CREATE DATABASE IF NOT EXISTS \`${dbName}\``);
    await connection.query(`USE \`${dbName}\``);

    console.log('🗑️  Dropping existing tables (if any)...');
    // Drop in correct order due to foreign keys
    const dropTables = [
      'sales_transactions',
      'wastage_records',
      'bills',
      'inventory',
      'customers',
      'suppliers',
      'gold_rates',
      'users'
    ];

    for (const table of dropTables) {
      await connection.query(`DROP TABLE IF EXISTS ${table}`);
    }

    console.log('🏗️  Creating suppliers table...');
    await connection.query(`
      CREATE TABLE suppliers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        location VARCHAR(255),
        contact_person VARCHAR(255),
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        speciality VARCHAR(255),
        total_purchases DECIMAL(15,2) DEFAULT 0.00,
        last_purchase_date DATE,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_suppliers_name (name),
        INDEX idx_suppliers_status (status),
        INDEX idx_suppliers_location (location)
      )
    `);

    console.log('👥 Creating customers table...');
    await connection.execute(`
      CREATE TABLE customers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        location VARCHAR(255),
        contact_person VARCHAR(255),
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        total_purchases DECIMAL(15,2) DEFAULT 0.00,
        last_purchase_date DATE,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_customers_name (name),
        INDEX idx_customers_status (status),
        INDEX idx_customers_location (location)
      )
    `);

    console.log('📦 Creating inventory table...');
    await connection.execute(`
      CREATE TABLE inventory (
        id INT AUTO_INCREMENT PRIMARY KEY,
        supplier_id INT,
        product_name VARCHAR(255) NOT NULL,
        product_type VARCHAR(255),
        metal_type ENUM('Gold', 'Silver', 'Platinum') DEFAULT 'Gold',
        form_type ENUM('Bar', 'Jewel', 'Old Jewel') DEFAULT 'Jewel',
        jewel_type ENUM('With Stone', 'Without Stone') NULL,
        jewel_category VARCHAR(100) NULL,
        with_stone_weight DECIMAL(10,3) DEFAULT 0.000,
        without_stone_weight DECIMAL(10,3) DEFAULT 0.000,
        stone_weight DECIMAL(10,3) DEFAULT 0.000,
        with_stone_cost DECIMAL(10,2) DEFAULT 0.00,
        without_stone_cost DECIMAL(10,2) DEFAULT 0.00,
        procured_in_24k DECIMAL(10,3) DEFAULT 0.000,
        wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
        expected_processing_loss DECIMAL(10,3) DEFAULT 0.000,
        sold_value_with_stone DECIMAL(10,3) DEFAULT 0.000,
        sold_value_without_stone DECIMAL(10,3) DEFAULT 0.000,
        sold_value_24k DECIMAL(10,3) DEFAULT 0.000,
        sold_value_22k DECIMAL(10,3) DEFAULT 0.000,
        sold_value_18k DECIMAL(10,3) DEFAULT 0.000,
        balance_weight_24k DECIMAL(10,3) DEFAULT 0.000,
        balance_weight_22k DECIMAL(10,3) DEFAULT 0.000,
        making_charges DECIMAL(10,2) DEFAULT 0.00,
        status ENUM('Available', 'Low Stock', 'Out of Stock') DEFAULT 'Available',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
        INDEX idx_inventory_supplier_id (supplier_id),
        INDEX idx_inventory_product_name (product_name),
        INDEX idx_inventory_status (status),
        INDEX idx_inventory_metal_type (metal_type),
        INDEX idx_inventory_form_type (form_type),
        INDEX idx_inventory_jewel_type (jewel_type)
      )
    `);

    console.log('🧾 Creating bills table...');
    await connection.execute(`
      CREATE TABLE bills (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT,
        inventory_item_id INT,
        customer_name VARCHAR(255),
        customer_location VARCHAR(255),
        bill_number VARCHAR(50) UNIQUE NOT NULL,
        product_name VARCHAR(255) NOT NULL,
        product_type VARCHAR(255),
        with_stone DECIMAL(10,3) DEFAULT 0.000,
        without_stone DECIMAL(10,3) DEFAULT 0.000,
        gross_weight DECIMAL(10,3) DEFAULT 0.000,
        stone_weight DECIMAL(10,3) DEFAULT 0.000,
        net_weight DECIMAL(10,3) DEFAULT 0.000,
        tunch_with_stone INT DEFAULT 0,
        tunch_without_stone INT DEFAULT 0,
        weight_in_24k DECIMAL(10,3) DEFAULT 0.000,
        gold_24k_price DECIMAL(10,2) DEFAULT 0.00,
        stone_price DECIMAL(10,2) DEFAULT 0.00,
        making_charges DECIMAL(10,2) DEFAULT 0.00,
        total_amount DECIMAL(15,2) DEFAULT 0.00,
        status ENUM('Pending', 'Paid', 'Cancelled') DEFAULT 'Pending',
        bill_date DATE DEFAULT (CURRENT_DATE),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
        FOREIGN KEY (inventory_item_id) REFERENCES inventory(id) ON DELETE SET NULL,
        INDEX idx_bills_customer_id (customer_id),
        INDEX idx_bills_inventory_item_id (inventory_item_id),
        INDEX idx_bills_bill_number (bill_number),
        INDEX idx_bills_bill_date (bill_date),
        INDEX idx_bills_status (status)
      )
    `);

    console.log('💰 Creating gold_rates table...');
    await connection.execute(`
      CREATE TABLE gold_rates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        rate_24k DECIMAL(10,2) NOT NULL,
        rate_22k DECIMAL(10,2) NOT NULL,
        rate_18k DECIMAL(10,2) NOT NULL,
        rate_date DATE DEFAULT (CURRENT_DATE),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_gold_rates_date (rate_date),
        UNIQUE KEY unique_rate_date (rate_date)
      )
    `);

    console.log('👤 Creating users table...');
    await connection.execute(`
      CREATE TABLE users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255),
        role ENUM('Admin', 'Manager', 'Staff', 'Viewer') DEFAULT 'Staff',
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        last_login TIMESTAMP NULL,
        permissions JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_users_email (email),
        INDEX idx_users_role (role),
        INDEX idx_users_status (status)
      )
    `);

    console.log('💼 Creating sales_transactions table...');
    await connection.execute(`
      CREATE TABLE sales_transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        inventory_id INT NOT NULL,
        customer_id INT,
        transaction_type ENUM('Sale', 'Return', 'Exchange', 'Wastage') DEFAULT 'Sale',
        weight_24k DECIMAL(10,3) DEFAULT 0.000,
        weight_22k DECIMAL(10,3) DEFAULT 0.000,
        weight_18k DECIMAL(10,3) DEFAULT 0.000,
        stone_weight DECIMAL(10,3) DEFAULT 0.000,
        stone_value DECIMAL(10,2) DEFAULT 0.00,
        rate_24k DECIMAL(10,2) DEFAULT 0.00,
        rate_22k DECIMAL(10,2) DEFAULT 0.00,
        making_charges DECIMAL(10,2) DEFAULT 0.00,
        total_amount DECIMAL(15,2) DEFAULT 0.00,
        transaction_date DATE DEFAULT (CURRENT_DATE),
        bill_number VARCHAR(50),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by VARCHAR(100),
        FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
        INDEX idx_sales_inventory_id (inventory_id),
        INDEX idx_sales_customer_id (customer_id),
        INDEX idx_sales_transaction_date (transaction_date),
        INDEX idx_sales_transaction_type (transaction_type),
        INDEX idx_sales_bill_number (bill_number)
      )
    `);

    console.log('🗑️  Creating wastage_records table...');
    await connection.execute(`
      CREATE TABLE wastage_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        inventory_id INT NOT NULL,
        wastage_type ENUM('Processing', 'Manufacturing', 'Refining', 'Loss', 'Other') DEFAULT 'Processing',
        weight_24k DECIMAL(10,3) DEFAULT 0.000,
        weight_22k DECIMAL(10,3) DEFAULT 0.000,
        weight_18k DECIMAL(10,3) DEFAULT 0.000,
        wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
        reason TEXT,
        process_stage VARCHAR(100),
        recovered_weight DECIMAL(10,3) DEFAULT 0.000,
        recovered_purity ENUM('24K', '22K', '18K', 'Mixed') DEFAULT '24K',
        recorded_date DATE DEFAULT (CURRENT_DATE),
        recorded_by VARCHAR(100),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE,
        INDEX idx_wastage_inventory_id (inventory_id),
        INDEX idx_wastage_type (wastage_type),
        INDEX idx_wastage_date (recorded_date)
      )
    `);

    console.log('📊 Inserting sample data...');
    
    // Sample gold rates
    await connection.execute(`
      INSERT INTO gold_rates (rate_24k, rate_22k, rate_18k, rate_date) VALUES 
      (7200.00, 6595.00, 5400.00, CURDATE())
    `);

    // Sample admin user
    await connection.execute(`
      INSERT INTO users (name, email, role, permissions) VALUES 
      ('System Admin', '<EMAIL>', 'Admin', 
      '{"inventory": true, "billing": true, "reports": true, "suppliers": true, "goldRates": true, "backup": true}')
    `);

    console.log('✅ Database setup completed successfully!');
    console.log(`📊 Database: ${dbName}`);
    console.log('🏗️  Tables created: suppliers, customers, inventory, bills, gold_rates, users, sales_transactions, wastage_records');
    console.log('📊 Sample data inserted: gold rates, admin user');

  } catch (error) {
    console.error('❌ Error setting up database:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the setup
setupCompleteDatabase();
