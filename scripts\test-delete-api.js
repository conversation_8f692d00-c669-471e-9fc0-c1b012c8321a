const http = require('http');

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/api/inventory/11',
  method: 'DELETE',
  headers: {
    'Content-Type': 'application/json'
  }
};

console.log('Testing DELETE /api/inventory/11...');

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers:`, res.headers);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('Response:', data);
    try {
      const parsed = JSON.parse(data);
      console.log('Parsed response:', JSON.stringify(parsed, null, 2));
    } catch (e) {
      console.log('Could not parse JSON response');
    }
  });
});

req.on('error', (e) => {
  console.error(`Problem with request: ${e.message}`);
});

req.end();
