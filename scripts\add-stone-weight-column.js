const mysql = require('mysql2/promise');
require('dotenv').config();

async function addStoneWeightColumn() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jewellery_wholesale'
    });

    console.log('Connected to database');

    // Check if stone_weight column exists
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory' AND COLUMN_NAME = 'stone_weight'
    `, [process.env.DB_NAME || 'jewellery_wholesale']);

    if (columns.length > 0) {
      console.log('stone_weight column already exists');
      return;
    }

    // Add stone_weight column
    await connection.execute(`
      ALTER TABLE inventory 
      ADD COLUMN stone_weight DECIMAL(10,3) DEFAULT 0.000 AFTER without_stone_weight
    `);

    console.log('Successfully added stone_weight column to inventory table');

  } catch (error) {
    console.error('Error adding stone_weight column:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
addStoneWeightColumn();
