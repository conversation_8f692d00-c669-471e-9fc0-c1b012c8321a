const fs = require('fs');

console.log('🔍 VERIFYING VARIABLE REFERENCES');
console.log('================================\n');

// Check billing-system.tsx for undefined variable references
const filePath = 'components/billing-system.tsx';

try {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  console.log('📋 CHECKING BILLING SYSTEM VARIABLES:\n');
  
  // Variables that should be defined
  const definedVariables = [
    'searchTerm', 'setSearchTerm',
    'isAddDialogOpen', 'setIsAddDialogOpen', 
    'newBill', 'setNewBill',
    'currentGoldRates', 'setCurrentGoldRates',
    'bills', 'loading', 'error', 'refetch',
    'customers', 'allInventory', 'refetchInventory',
    'goldRates', 'mutate', 'mutating',
    'businessSettings', 'settingsLoading',
    'toast'
  ];
  
  // Variables that should NOT be used (old/undefined)
  const undefinedVariables = [
    'goldRate', 'setGoldRate'
  ];
  
  // Check for undefined variable usage
  let issuesFound = 0;
  
  console.log('🔍 Checking for undefined variable usage:');
  undefinedVariables.forEach(variable => {
    const regex = new RegExp(`\\b${variable}\\b`, 'g');
    const matches = content.match(regex);
    
    if (matches) {
      console.log(`❌ Found ${matches.length} references to undefined variable: ${variable}`);
      
      // Show line numbers where it's used
      lines.forEach((line, index) => {
        if (line.includes(variable)) {
          console.log(`   Line ${index + 1}: ${line.trim()}`);
        }
      });
      issuesFound++;
    } else {
      console.log(`✅ No references to undefined variable: ${variable}`);
    }
  });
  
  console.log('\n🔍 Checking for proper variable definitions:');
  definedVariables.forEach(variable => {
    if (content.includes(variable)) {
      console.log(`✅ ${variable}: Found in code`);
    } else {
      console.log(`⚠️  ${variable}: Not found (may not be used)`);
    }
  });
  
  // Check for proper currentGoldRates usage
  console.log('\n🔍 Checking currentGoldRates usage:');
  const goldRateUsages = [
    'currentGoldRates.rate_24k',
    'setCurrentGoldRates'
  ];
  
  goldRateUsages.forEach(usage => {
    if (content.includes(usage)) {
      console.log(`✅ ${usage}: Properly used`);
    } else {
      console.log(`❌ ${usage}: Not found`);
      issuesFound++;
    }
  });
  
  // Summary
  console.log('\n📊 VERIFICATION SUMMARY');
  console.log('======================');
  
  if (issuesFound === 0) {
    console.log('\n🎉 SUCCESS: All variable references are correct!');
    console.log('✅ No undefined variables found');
    console.log('✅ All gold rate references use currentGoldRates');
    console.log('✅ The "goldRate is not defined" error has been fixed');
  } else {
    console.log('\n❌ ISSUES FOUND: Some variables need attention');
    console.log(`❌ ${issuesFound} undefined variable references detected`);
    console.log('❌ May cause "variable is not defined" errors');
  }
  
} catch (error) {
  console.log(`❌ Error reading file: ${error.message}`);
}

// Test the application API to ensure it's working
console.log('\n🌐 TESTING APPLICATION STATUS:');

async function testApplication() {
  try {
    const response = await fetch('http://localhost:3000/api/settings?business=true');
    if (response.ok) {
      const data = await response.json();
      if (data.success) {
        console.log('✅ Application API: Working');
        console.log('✅ Server is running correctly');
        console.log('✅ Database connections are active');
      } else {
        console.log('❌ Application API: Error in response');
      }
    } else {
      console.log('❌ Application API: HTTP error');
    }
  } catch (error) {
    console.log('❌ Application API: Connection failed');
  }
  
  console.log('\n🚀 NEXT STEPS:');
  console.log('1. Open http://localhost:3000 in your browser');
  console.log('2. Navigate to the billing system');
  console.log('3. Check that gold rate inputs work correctly');
  console.log('4. Verify no "goldRate is not defined" errors appear');
  console.log('5. Test adding a new bill');
  
  console.log('\n✅ VARIABLE REFERENCE VERIFICATION COMPLETE');
}

testApplication();
