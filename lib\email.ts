import nodemailer from "nodemailer"
import { config } from "./config"

interface EmailOptions {
  to: string
  subject: string
  html: string
  text?: string
}

interface LowStockAlert {
  itemName: string
  currentStock: number
  minimumStock: number
  category: string
}

interface BillNotification {
  billNumber: string
  customerName: string
  amount: number
  dueDate: string
}

let transporter: nodemailer.Transporter | null = null

function createTransporter(): nodemailer.Transporter {
  if (!transporter) {
    transporter = nodemailer.createTransporter({
      host: config.email.host,
      port: config.email.port,
      secure: config.email.port === 465,
      auth:
        config.email.user && config.email.password
          ? {
              user: config.email.user,
              pass: config.email.password,
            }
          : undefined,
    })
  }
  return transporter
}

export async function sendEmail(options: EmailOptions): Promise<boolean> {
  try {
    if (!config.email.user || !config.email.password) {
      console.warn("Email configuration not complete, skipping email send")
      return false
    }

    const transporter = createTransporter()

    const mailOptions = {
      from: config.email.from,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text || options.html.replace(/<[^>]*>/g, ""), // Strip HTML for text version
    }

    const result = await transporter.sendMail(mailOptions)

    if (config.debug) {
      console.log("Email sent successfully:", result.messageId)
    }

    return true
  } catch (error) {
    console.error("Failed to send email:", error)
    return false
  }
}

export async function sendLowStockAlert(items: LowStockAlert[], recipientEmail: string): Promise<boolean> {
  const itemsList = items
    .map(
      (item) => `
    <tr>
      <td style="padding: 8px; border: 1px solid #ddd;">${item.itemName}</td>
      <td style="padding: 8px; border: 1px solid #ddd;">${item.category}</td>
      <td style="padding: 8px; border: 1px solid #ddd;">${item.currentStock}</td>
      <td style="padding: 8px; border: 1px solid #ddd;">${item.minimumStock}</td>
    </tr>
  `,
    )
    .join("")

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #d97706;">Low Stock Alert</h2>
      <p>The following items are running low in stock and need to be restocked:</p>
      
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <thead>
          <tr style="background-color: #f3f4f6;">
            <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">Item Name</th>
            <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">Category</th>
            <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">Current Stock</th>
            <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">Minimum Stock</th>
          </tr>
        </thead>
        <tbody>
          ${itemsList}
        </tbody>
      </table>
      
      <p style="margin-top: 20px;">Please restock these items as soon as possible to avoid stockouts.</p>
      
      <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
      <p style="font-size: 12px; color: #6b7280;">
        This is an automated alert from ${config.app.companyName}<br>
        ${config.app.companyAddress}<br>
        ${config.app.companyPhone}
      </p>
    </div>
  `

  return await sendEmail({
    to: recipientEmail,
    subject: `Low Stock Alert - ${items.length} items need restocking`,
    html,
  })
}

export async function sendBillNotification(bill: BillNotification, recipientEmail: string): Promise<boolean> {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #059669;">Bill Created Successfully</h2>
      
      <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="margin: 0 0 15px 0; color: #0369a1;">Bill Details</h3>
        <p><strong>Bill Number:</strong> ${bill.billNumber}</p>
        <p><strong>Customer:</strong> ${bill.customerName}</p>
        <p><strong>Amount:</strong> ₹${bill.amount.toLocaleString("en-IN")}</p>
        <p><strong>Due Date:</strong> ${bill.dueDate}</p>
      </div>
      
      <p>A new bill has been created and is ready for processing. Please ensure timely payment collection.</p>
      
      <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
      <p style="font-size: 12px; color: #6b7280;">
        This is an automated notification from ${config.app.companyName}<br>
        ${config.app.companyAddress}<br>
        ${config.app.companyPhone}
      </p>
    </div>
  `

  return await sendEmail({
    to: recipientEmail,
    subject: `New Bill Created - ${bill.billNumber}`,
    html,
  })
}

export async function sendBackupNotification(
  backupFile: string,
  backupSize: string,
  recipientEmail: string,
): Promise<boolean> {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #059669;">Database Backup Completed</h2>
      
      <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="margin: 0 0 15px 0; color: #0369a1;">Backup Details</h3>
        <p><strong>File:</strong> ${backupFile}</p>
        <p><strong>Size:</strong> ${backupSize}</p>
        <p><strong>Date:</strong> ${new Date().toLocaleString("en-IN")}</p>
      </div>
      
      <p>Your database backup has been completed successfully. The backup file is stored securely and can be used for data recovery if needed.</p>
      
      <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <p style="margin: 0; color: #92400e;"><strong>Important:</strong> Store backup files in a secure location and test restore procedures regularly.</p>
      </div>
      
      <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
      <p style="font-size: 12px; color: #6b7280;">
        This is an automated notification from ${config.app.companyName}<br>
        ${config.app.companyAddress}<br>
        ${config.app.companyPhone}
      </p>
    </div>
  `

  return await sendEmail({
    to: recipientEmail,
    subject: `Database Backup Completed - ${new Date().toLocaleDateString("en-IN")}`,
    html,
  })
}

export async function testEmailConfiguration(): Promise<{ success: boolean; error?: string }> {
  try {
    if (!config.email.user || !config.email.password) {
      return {
        success: false,
        error: "Email configuration incomplete - missing user or password",
      }
    }

    const transporter = createTransporter()
    await transporter.verify()

    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown email configuration error",
    }
  }
}

export async function sendTestEmail(recipientEmail: string): Promise<boolean> {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #059669;">Email Configuration Test</h2>
      
      <p>This is a test email to verify that your email configuration is working correctly.</p>
      
      <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="margin: 0 0 15px 0; color: #0369a1;">Configuration Details</h3>
        <p><strong>SMTP Host:</strong> ${config.email.host}</p>
        <p><strong>SMTP Port:</strong> ${config.email.port}</p>
        <p><strong>From Address:</strong> ${config.email.from}</p>
        <p><strong>Test Time:</strong> ${new Date().toLocaleString("en-IN")}</p>
      </div>
      
      <p>If you received this email, your email configuration is working properly!</p>
      
      <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
      <p style="font-size: 12px; color: #6b7280;">
        This is a test email from ${config.app.companyName}<br>
        ${config.app.companyAddress}<br>
        ${config.app.companyPhone}
      </p>
    </div>
  `

  return await sendEmail({
    to: recipientEmail,
    subject: `Email Configuration Test - ${config.app.companyName}`,
    html,
  })
}
