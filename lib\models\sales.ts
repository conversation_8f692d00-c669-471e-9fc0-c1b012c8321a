import { executeQuery, executeSingle, executeUpdate } from "../database"

export interface SalesTransaction {
  id: number
  inventory_id: number
  customer_id?: number
  transaction_type: "Sale" | "Return" | "Exchange" | "Wastage"
  weight_24k: number
  weight_22k: number
  weight_18k: number
  stone_weight: number
  stone_value: number
  rate_24k: number
  rate_22k: number
  making_charges: number
  total_amount: number
  transaction_date: string
  bill_number?: string
  notes?: string
  created_at: string
  updated_at: string
  created_by?: string
}

export interface CreateSalesTransactionData {
  inventory_id: number
  customer_id?: number
  transaction_type?: "Sale" | "Return" | "Exchange" | "Wastage"
  weight_24k?: number
  weight_22k?: number
  weight_18k?: number
  stone_weight?: number
  stone_value?: number
  rate_24k?: number
  rate_22k?: number
  making_charges?: number
  bill_number?: string
  notes?: string
  created_by?: string
}

export interface WastageRecord {
  id: number
  inventory_id: number
  wastage_type: "Processing" | "Manufacturing" | "Refining" | "Loss" | "Other"
  weight_24k: number
  weight_22k: number
  weight_18k: number
  wastage_percentage: number
  reason?: string
  process_stage?: string
  recovered_weight: number
  recovered_purity: "24K" | "22K" | "18K" | "Mixed"
  recorded_date: string
  recorded_by?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface CreateWastageRecordData {
  inventory_id: number
  wastage_type?: "Processing" | "Manufacturing" | "Refining" | "Loss" | "Other"
  weight_24k?: number
  weight_22k?: number
  weight_18k?: number
  wastage_percentage?: number
  reason?: string
  process_stage?: string
  recovered_weight?: number
  recovered_purity?: "24K" | "22K" | "18K" | "Mixed"
  recorded_by?: string
  notes?: string
}

export class SalesModel {
  // Create new sales transaction and update inventory balance
  static async createSalesTransaction(data: CreateSalesTransactionData): Promise<number> {
    // Calculate total amount if not provided
    const totalAmount = data.rate_24k && data.weight_24k 
      ? (data.weight_24k * data.rate_24k) + (data.weight_22k || 0) * (data.rate_22k || 0) + (data.making_charges || 0) + (data.stone_value || 0)
      : 0

    const query = `
      INSERT INTO sales_transactions (
        inventory_id, customer_id, transaction_type, weight_24k, weight_22k, weight_18k,
        stone_weight, stone_value, rate_24k, rate_22k, making_charges, total_amount,
        bill_number, notes, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    
    const params = [
      data.inventory_id,
      data.customer_id || null,
      data.transaction_type || 'Sale',
      data.weight_24k || 0,
      data.weight_22k || 0,
      data.weight_18k || 0,
      data.stone_weight || 0,
      data.stone_value || 0,
      data.rate_24k || 0,
      data.rate_22k || 0,
      data.making_charges || 0,
      totalAmount,
      data.bill_number || null,
      data.notes || null,
      data.created_by || null
    ]

    const result = await executeUpdate(query, params)
    
    // Update inventory balances
    await this.updateInventoryBalances(data.inventory_id)
    
    return result.insertId!
  }

  // Create wastage record and update inventory balance
  static async createWastageRecord(data: CreateWastageRecordData): Promise<number> {
    const query = `
      INSERT INTO wastage_records (
        inventory_id, wastage_type, weight_24k, weight_22k, weight_18k,
        wastage_percentage, reason, process_stage, recovered_weight, recovered_purity,
        recorded_by, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    
    const params = [
      data.inventory_id,
      data.wastage_type || 'Processing',
      data.weight_24k || 0,
      data.weight_22k || 0,
      data.weight_18k || 0,
      data.wastage_percentage || 0,
      data.reason || null,
      data.process_stage || null,
      data.recovered_weight || 0,
      data.recovered_purity || '24K',
      data.recorded_by || null,
      data.notes || null
    ]

    const result = await executeUpdate(query, params)
    
    // Update inventory balances
    await this.updateInventoryBalances(data.inventory_id)
    
    return result.insertId!
  }

  // Update inventory balances based on all transactions and wastage
  static async updateInventoryBalances(inventoryId: number): Promise<void> {
    // Get total sold amounts
    const [salesData] = await executeQuery(`
      SELECT 
        COALESCE(SUM(CASE WHEN transaction_type = 'Sale' THEN weight_24k ELSE -weight_24k END), 0) as total_sold_24k,
        COALESCE(SUM(CASE WHEN transaction_type = 'Sale' THEN weight_22k ELSE -weight_22k END), 0) as total_sold_22k,
        COALESCE(SUM(CASE WHEN transaction_type = 'Sale' THEN weight_18k ELSE -weight_18k END), 0) as total_sold_18k
      FROM sales_transactions 
      WHERE inventory_id = ?
    `, [inventoryId])

    // Get total wastage amounts
    const [wastageData] = await executeQuery(`
      SELECT 
        COALESCE(SUM(weight_24k), 0) as total_wastage_24k,
        COALESCE(SUM(weight_22k), 0) as total_wastage_22k,
        COALESCE(SUM(weight_18k), 0) as total_wastage_18k,
        COALESCE(SUM(recovered_weight), 0) as total_recovered
      FROM wastage_records 
      WHERE inventory_id = ?
    `, [inventoryId])

    // Get current inventory data
    const inventory = await executeSingle(`
      SELECT procured_in_24k FROM inventory WHERE id = ?
    `, [inventoryId])

    if (!inventory) return

    const sales = salesData[0] || { total_sold_24k: 0, total_sold_22k: 0, total_sold_18k: 0 }
    const wastage = wastageData[0] || { total_wastage_24k: 0, total_wastage_22k: 0, total_wastage_18k: 0, total_recovered: 0 }

    // Calculate new balances
    const balance24k = Math.max(0,
      inventory.procured_in_24k - sales.total_sold_24k - wastage.total_wastage_24k + wastage.total_recovered
    )

    // For 22K balance, we need to calculate based on the original 22K balance minus sold 22K
    // Get the original 22K balance from when the item was created
    const original22kBalance = inventory.balance_weight_22k || (inventory.procured_in_24k * 0.916)
    const balance22k = Math.max(0, original22kBalance - sales.total_sold_22k - wastage.total_wastage_22k)

    // Update inventory record
    await executeUpdate(`
      UPDATE inventory
      SET
        sold_value_24k = ?,
        sold_value_22k = ?,
        sold_value_18k = ?,
        sold_gold_weight_24k = ?,
        sold_gold_weight_22k = ?,
        balance_weight_24k = ?,
        balance_weight_22k = ?,
        balance_gold_weight_22k = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      sales.total_sold_24k,
      sales.total_sold_22k,
      sales.total_sold_18k,
      sales.total_sold_24k, // sold_gold_weight_24k
      sales.total_sold_22k, // sold_gold_weight_22k
      balance24k,
      balance22k,
      balance22k, // balance_gold_weight_22k (available stock)
      inventoryId
    ])
  }

  // Get all sales transactions for an inventory item
  static async getSalesForInventory(inventoryId: number): Promise<SalesTransaction[]> {
    const query = `
      SELECT st.*, c.name as customer_name
      FROM sales_transactions st
      LEFT JOIN customers c ON st.customer_id = c.id
      WHERE st.inventory_id = ?
      ORDER BY st.created_at DESC
    `
    return executeQuery<SalesTransaction>(query, [inventoryId])
  }

  // Get all wastage records for an inventory item
  static async getWastageForInventory(inventoryId: number): Promise<WastageRecord[]> {
    const query = `
      SELECT * FROM wastage_records
      WHERE inventory_id = ?
      ORDER BY created_at DESC
    `
    return executeQuery<WastageRecord>(query, [inventoryId])
  }

  // Get sales summary
  static async getSalesSummary(startDate?: string, endDate?: string): Promise<any> {
    let whereClause = "WHERE transaction_type = 'Sale'"
    const params: any[] = []

    if (startDate && endDate) {
      whereClause += " AND transaction_date BETWEEN ? AND ?"
      params.push(startDate, endDate)
    }

    const query = `
      SELECT 
        COUNT(*) as total_transactions,
        SUM(weight_24k) as total_weight_24k,
        SUM(weight_22k) as total_weight_22k,
        SUM(total_amount) as total_revenue,
        AVG(rate_24k) as avg_rate_24k,
        AVG(rate_22k) as avg_rate_22k
      FROM sales_transactions 
      ${whereClause}
    `
    
    return executeSingle(query, params)
  }
}
