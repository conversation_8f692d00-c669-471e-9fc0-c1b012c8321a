import { type NextRequest, NextResponse } from "next/server"
import { config } from "@/lib/config"
import { testConnection } from "@/lib/database"
import { testEmailConfiguration, sendTestEmail } from "@/lib/email"

export async function GET() {
  try {
    // Test database connection
    const dbTest = await testConnection()

    // Test email configuration
    const emailTest = await testEmailConfiguration()

    // Return configuration status (without sensitive data)
    return NextResponse.json({
      status: "success",
      timestamp: new Date().toISOString(),
      application: {
        name: config.app.name,
        version: config.app.version,
        environment: config.isDevelopment ? "development" : "production",
        debug: config.debug,
      },
      company: {
        name: config.app.companyName,
        address: config.app.companyAddress,
        phone: config.app.companyPhone,
        email: config.app.companyEmail,
      },
      database: {
        host: config.database.host,
        port: config.database.port,
        database: config.database.database,
        ssl: config.database.ssl,
        connected: dbTest.success,
        error: dbTest.error,
      },
      email: {
        host: config.email.host,
        port: config.email.port,
        from: config.email.from,
        configured: !!config.email.user && !!config.email.password,
        working: emailTest.success,
        error: emailTest.error,
      },
      business: {
        currency: config.business.currency,
        goldUnit: config.business.goldUnit,
        taxRate: config.business.taxRate,
        makingCharges: config.business.makingCharges,
      },
      files: {
        maxFileSize: config.files.maxFileSize,
        uploadDir: config.files.uploadDir,
        backupDir: config.files.backupDir,
      },
    })
  } catch (error) {
    console.error("Configuration check error:", error)

    return NextResponse.json(
      {
        status: "error",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Unknown configuration error",
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, email } = body

    if (action === "test-email" && email) {
      const success = await sendTestEmail(email)

      return NextResponse.json({
        status: success ? "success" : "error",
        message: success ? `Test email sent successfully to ${email}` : "Failed to send test email",
        timestamp: new Date().toISOString(),
      })
    }

    if (action === "test-db") {
      const dbTest = await testConnection()

      return NextResponse.json({
        status: dbTest.success ? "success" : "error",
        message: dbTest.success ? "Database connection successful" : `Database connection failed: ${dbTest.error}`,
        timestamp: new Date().toISOString(),
      })
    }

    return NextResponse.json(
      {
        status: "error",
        message: "Invalid action or missing parameters",
        timestamp: new Date().toISOString(),
      },
      { status: 400 },
    )
  } catch (error) {
    console.error("Configuration action error:", error)

    return NextResponse.json(
      {
        status: "error",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}
