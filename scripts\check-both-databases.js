const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkBothDatabases() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || ''
    });

    console.log('Connected to MySQL server');

    // Check configured database and common alternatives
    const configuredDb = process.env.DB_NAME;
    const databases = [configuredDb, 'jewellery_wholesale', 'jewellery_wholesale_software'].filter(Boolean);
    
    for (const dbName of databases) {
      console.log(`\n=== Checking database: ${dbName} ===`);
      
      try {
        // Check if database exists
        const [dbExists] = await connection.execute(`
          SELECT SCHEMA_NAME 
          FROM INFORMATION_SCHEMA.SCHEMATA 
          WHERE SCHEMA_NAME = ?
        `, [dbName]);

        if (dbExists.length === 0) {
          console.log(`Database ${dbName} does not exist`);
          continue;
        }

        // Check inventory table schema
        const [columns] = await connection.execute(`
          SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory'
          ORDER BY ORDINAL_POSITION
        `, [dbName]);

        if (columns.length === 0) {
          console.log(`No inventory table found in ${dbName}`);
        } else {
          console.log(`Inventory table columns in ${dbName}:`);
          columns.forEach(col => {
            console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'} ${col.COLUMN_DEFAULT ? `DEFAULT ${col.COLUMN_DEFAULT}` : ''}`);
          });
        }

      } catch (error) {
        console.log(`Error checking ${dbName}:`, error.message);
      }
    }

    console.log(`\nApplication is configured to use: ${process.env.DB_NAME}`);

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
checkBothDatabases();
