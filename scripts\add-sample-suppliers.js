const mysql = require('mysql2/promise');
require('dotenv').config();

async function addSampleSuppliers() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME
    });

    console.log('Connected to database');

    // Check if suppliers already exist
    const [existing] = await connection.execute('SELECT COUNT(*) as count FROM suppliers');
    if (existing[0].count > 0) {
      console.log('Suppliers already exist, skipping...');
      return;
    }

    // Add sample suppliers
    const suppliers = [
      {
        name: 'Golden Jewels Pvt Ltd',
        location: 'Mumbai',
        contact_person: '<PERSON><PERSON>',
        phone: '+91-9876543210',
        email: 'r<PERSON><PERSON>@goldenjewels.com',
        speciality: 'Gold Chains and Bangles'
      },
      {
        name: 'Diamond Palace',
        location: 'Surat',
        contact_person: '<PERSON><PERSON> <PERSON>',
        phone: '+91-9876543211',
        email: '<EMAIL>',
        speciality: 'Diamond Jewelry'
      },
      {
        name: 'Silver Craft Industries',
        location: 'Jaipur',
        contact_person: 'Amit Sharma',
        phone: '+91-9876543212',
        email: '<EMAIL>',
        speciality: 'Silver Ornaments'
      },
      {
        name: 'Traditional Jewelers',
        location: 'Chennai',
        contact_person: 'Lakshmi Narayanan',
        phone: '+91-9876543213',
        email: '<EMAIL>',
        speciality: 'Temple Jewelry'
      },
      {
        name: 'Modern Gold Works',
        location: 'Delhi',
        contact_person: 'Vikram Singh',
        phone: '+91-9876543214',
        email: '<EMAIL>',
        speciality: 'Contemporary Designs'
      }
    ];

    for (const supplier of suppliers) {
      await connection.execute(`
        INSERT INTO suppliers (name, location, contact_person, phone, email, speciality)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [supplier.name, supplier.location, supplier.contact_person, supplier.phone, supplier.email, supplier.speciality]);
    }

    console.log(`Successfully added ${suppliers.length} sample suppliers`);

  } catch (error) {
    console.error('Error adding suppliers:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
addSampleSuppliers();
