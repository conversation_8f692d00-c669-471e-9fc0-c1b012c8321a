const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkInventoryFields() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jewellery_wholesale_software'
    });

    console.log('Connected to database');

    // Get inventory table schema
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME || 'jewellery_wholesale_software']);

    console.log('\n=== INVENTORY TABLE SCHEMA ===');
    columns.forEach(col => {
      console.log(`${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'} ${col.COLUMN_DEFAULT ? `DEFAULT ${col.COLUMN_DEFAULT}` : ''}`);
    });

    // Check if all expected fields exist
    const expectedFields = [
      'id', 'supplier_id', 'product_name', 'product_type', 'metal_type', 
      'form_type', 'jewel_type', 'jewel_category', 'with_stone_weight', 
      'without_stone_weight', 'stone_weight', 'with_stone_cost', 
      'without_stone_cost', 'procured_in_24k', 'sold_value_with_stone', 
      'sold_value_without_stone', 'sold_value_24k', 'sold_value_22k', 
      'sold_value_18k', 'balance_weight_24k', 'balance_weight_22k', 
      'status', 'created_at', 'updated_at'
    ];

    const existingFields = columns.map(col => col.COLUMN_NAME);
    const missingFields = expectedFields.filter(field => !existingFields.includes(field));
    const extraFields = existingFields.filter(field => !expectedFields.includes(field));

    console.log('\n=== FIELD ANALYSIS ===');
    if (missingFields.length > 0) {
      console.log('❌ Missing fields:', missingFields.join(', '));
    } else {
      console.log('✅ All expected fields present');
    }

    if (extraFields.length > 0) {
      console.log('ℹ️  Extra fields:', extraFields.join(', '));
    }

    // Test a sample update query
    console.log('\n=== TESTING UPDATE QUERY ===');
    try {
      const testQuery = `
        UPDATE inventory 
        SET product_name = ?, metal_type = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = 999999
      `;
      await connection.execute(testQuery, ['Test Product', 'Gold']);
      console.log('✅ Update query syntax is valid');
    } catch (error) {
      console.log('❌ Update query error:', error.message);
    }

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
checkInventoryFields();
