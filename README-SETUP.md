# Jewellery Wholesale Software - Setup Guide

## Quick Start

1. **Clone and Install**
   \`\`\`bash
   git clone <repository-url>
   cd jewellery-wholesale-software
   npm install
   \`\`\`

2. **Environment Configuration**
   \`\`\`bash
   # Copy environment template
   cp .env.example .env.local
   
   # Edit .env.local with your settings
   nano .env.local
   \`\`\`

3. **Database Setup**
   \`\`\`bash
   # Create database and tables
   npm run db:create
   
   # Add sample data
   npm run db:seed
   \`\`\`

4. **Start Development Server**
   \`\`\`bash
   npm run dev
   \`\`\`

## Environment Variables

### Required Variables

\`\`\`env
# Database (Required)
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=jewellery_wholesale

# Security (Required - Change in Production!)
JWT_SECRET=your_super_secure_jwt_secret_key_here
SESSION_SECRET=your_session_secret_key_here
\`\`\`

### Optional Variables

\`\`\`env
# Company Information
NEXT_PUBLIC_COMPANY_NAME=Your Jewellery Company
NEXT_PUBLIC_COMPANY_ADDRESS=Your Business Address
NEXT_PUBLIC_COMPANY_PHONE=Your Phone Number
NEXT_PUBLIC_COMPANY_EMAIL=<EMAIL>

# Email Notifications (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=<EMAIL>

# Business Settings
DEFAULT_CURRENCY=INR
DEFAULT_TAX_RATE=3.0
DEFAULT_MAKING_CHARGES=10.0
\`\`\`

## Database Setup

### Prerequisites
- MySQL 8.0 or higher
- Database user with CREATE, INSERT, UPDATE, DELETE privileges

### Setup Commands

\`\`\`bash
# Test database connection
npm run test:db

# Create database schema
npm run db:create

# Insert sample data
npm run db:seed

# Reset database (drop and recreate)
npm run db:reset
\`\`\`

### Manual Database Setup

If npm scripts don't work, run SQL files manually:

\`\`\`bash
mysql -u root -p < scripts/mysql-schema.sql
mysql -u root -p < scripts/mysql-seed-data.sql
\`\`\`

## Email Configuration

### Gmail Setup (Recommended)

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. **Update .env.local**:
   \`\`\`env
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your_16_character_app_password
   SMTP_FROM=<EMAIL>
   \`\`\`

### Test Email Configuration

\`\`\`bash
# Check email configuration
curl -X POST http://localhost:3000/api/config \
  -H "Content-Type: application/json" \
  -d '{"action": "test-email", "email": "<EMAIL>"}'
\`\`\`

## Production Deployment

### Security Checklist

- [ ] Change all default passwords
- [ ] Generate strong JWT and session secrets (32+ characters)
- [ ] Enable SSL for database connections
- [ ] Set NODE_ENV=production
- [ ] Disable debug logging
- [ ] Configure proper backup strategy

### Environment Variables for Production

\`\`\`env
NODE_ENV=production
DEBUG=false
DB_SSL=true
DB_SSL_CA=/path/to/ca-cert.pem

# Strong secrets (generate with: openssl rand -base64 32)
JWT_SECRET=your_production_jwt_secret_32_chars_minimum
SESSION_SECRET=your_production_session_secret_32_chars_minimum
\`\`\`

### Build and Deploy

\`\`\`bash
# Build for production
npm run build

# Start production server
npm start
\`\`\`

## Troubleshooting

### Database Connection Issues

1. **Check MySQL is running**:
   \`\`\`bash
   sudo systemctl status mysql
   \`\`\`

2. **Verify credentials**:
   \`\`\`bash
   mysql -u root -p -h localhost
   \`\`\`

3. **Check database exists**:
   \`\`\`sql
   SHOW DATABASES;
   USE jewellery_wholesale;
   SHOW TABLES;
   \`\`\`

### Common Errors

**Error: ER_ACCESS_DENIED_ERROR**
- Check DB_USER and DB_PASSWORD in .env.local
- Ensure MySQL user has proper privileges

**Error: ER_BAD_DB_ERROR**
- Database doesn't exist, run: `npm run db:create`

**Error: ECONNREFUSED**
- MySQL server not running
- Check DB_HOST and DB_PORT settings

**Error: Module not found**
- Run: `npm install`
- Check Node.js version (requires 18+)

### Email Issues

**Error: Invalid login**
- Use App Password for Gmail (not regular password)
- Check SMTP_USER and SMTP_PASSWORD

**Error: Connection timeout**
- Check firewall settings
- Verify SMTP_HOST and SMTP_PORT

## System Requirements

- **Node.js**: 18.0 or higher
- **MySQL**: 8.0 or higher
- **RAM**: 2GB minimum, 4GB recommended
- **Storage**: 10GB minimum for database and backups

## Support

For issues and questions:
1. Check this README first
2. Review error logs in console
3. Test configuration: `http://localhost:3000/api/config`
4. Check database connectivity: `npm run test:db`

## Features Overview

- **Inventory Management**: Track gold, silver, and diamond inventory
- **Billing System**: Create and manage customer bills
- **Customer Management**: Maintain customer database
- **Supplier Management**: Track supplier information and purchases
- **Gold Rate Tracking**: Monitor and update gold rates
- **Reports**: Generate sales, inventory, and financial reports
- **User Management**: Role-based access control
- **Backup & Restore**: Database backup and recovery tools
- **Print Invoices**: Professional invoice printing
- **Email Notifications**: Automated alerts and notifications

## Default Login

After setup, use these default credentials:
- **Username**: admin
- **Password**: admin123

**⚠️ Change default password immediately after first login!**
