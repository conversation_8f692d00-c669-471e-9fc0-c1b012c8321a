"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Plus, Search, Edit, Trash2 } from "lucide-react"

interface InventoryItem {
  id: number
  supplierName: string
  location: string
  productName: string
  productType: string
  withStoneWeight: number
  withoutStoneWeight: number
  withStoneCost: number
  withoutStoneCost: number
  procuredIn24k: number
  soldValueWithStone: number
  soldValueWithoutStone: number
  balanceWeight24k: number
  balanceWeight22k: number
}

export default function InventoryManagement() {
  const [suppliers] = useState([
    {
      id: 1,
      name: "Emerald Jewel Industry",
      location: "Coimbatore",
      contactPerson: "Rajesh Kumar",
      phone: "+91 98765 43210",
      speciality: "Gold Chains, Necklaces",
    },
    {
      id: 2,
      name: "Nala Gold",
      location: "Mumbai",
      contactPerson: "Priya Sharma",
      phone: "+91 87654 32109",
      speciality: "Gold Bangles, Rings",
    },
    {
      id: 3,
      name: "SSJ",
      location: "Surat",
      contactPerson: "Amit Patel",
      phone: "+91 76543 21098",
      speciality: "Diamond Jewelry, Studs",
    },
    {
      id: 4,
      name: "Chennai Gold House",
      location: "Chennai",
      contactPerson: "Suresh Kumar",
      phone: "+91 98765 11111",
      speciality: "Temple Jewelry, Antique",
    },
    {
      id: 5,
      name: "Mysore Jewels",
      location: "Mysore",
      contactPerson: "Ravi Shankar",
      phone: "+91 87654 22222",
      speciality: "Traditional Jewelry",
    },
  ])

  const [inventory, setInventory] = useState<InventoryItem[]>([
    {
      id: 1,
      supplierName: "Emerald Jewel Industry",
      location: "Coimbatore",
      productName: "Chain",
      productType: "Gold Chain",
      withStoneWeight: 0,
      withoutStoneWeight: 120.42,
      withStoneCost: 93,
      withoutStoneCost: 94,
      procuredIn24k: 113.195,
      soldValueWithStone: 0.0,
      soldValueWithoutStone: 10.16,
      balanceWeight24k: 9.754,
      balanceWeight22k: 110.26,
    },
    {
      id: 2,
      supplierName: "Nala Gold",
      location: "Mumbai",
      productName: "Bangles",
      productType: "Gold Bangles",
      withStoneWeight: 0,
      withoutStoneWeight: 246.34,
      withStoneCost: 94,
      withoutStoneCost: 96,
      procuredIn24k: 236.486,
      soldValueWithStone: 0.0,
      soldValueWithoutStone: 32.12,
      balanceWeight24k: 31.478,
      balanceWeight22k: 214.22,
    },
    {
      id: 3,
      supplierName: "SSJ",
      location: "Surat",
      productName: "Studs",
      productType: "Diamond Studs",
      withStoneWeight: 110.325,
      withoutStoneWeight: 89.12,
      withStoneCost: 95,
      withoutStoneCost: 99,
      procuredIn24k: 193.038,
      soldValueWithStone: 1.56,
      soldValueWithoutStone: 2.01,
      balanceWeight24k: 3.388,
      balanceWeight22k: 195.875,
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newItem, setNewItem] = useState<Partial<InventoryItem>>({})

  const filteredInventory = inventory.filter(
    (item) =>
      item.supplierName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.location.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleAddItem = () => {
    if (newItem.supplierName && newItem.productName) {
      const item: InventoryItem = {
        id: inventory.length + 1,
        supplierName: newItem.supplierName || "",
        location: newItem.location || "",
        productName: newItem.productName || "",
        productType: newItem.productType || "",
        withStoneWeight: newItem.withStoneWeight || 0,
        withoutStoneWeight: newItem.withoutStoneWeight || 0,
        withStoneCost: newItem.withStoneCost || 0,
        withoutStoneCost: newItem.withoutStoneCost || 0,
        procuredIn24k: newItem.procuredIn24k || 0,
        soldValueWithStone: 0,
        soldValueWithoutStone: 0,
        balanceWeight24k: newItem.procuredIn24k || 0,
        balanceWeight22k: (newItem.procuredIn24k || 0) * 0.916,
      }
      setInventory([...inventory, item])
      setNewItem({})
      setIsAddDialogOpen(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Inventory Management</CardTitle>
              <CardDescription>Manage your jewellery inventory with detailed tracking</CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Item
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add New Inventory Item</DialogTitle>
                  <DialogDescription>Select supplier and enter product details</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="space-y-2">
                    <Label>Select Supplier</Label>
                    <Select
                      onValueChange={(value) => {
                        const supplier = suppliers.find((s) => s.id === Number.parseInt(value))
                        if (supplier) {
                          setNewItem({
                            ...newItem,
                            supplierName: supplier.name,
                            location: supplier.location,
                          })
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Choose existing supplier" />
                      </SelectTrigger>
                      <SelectContent>
                        {suppliers.map((supplier) => (
                          <SelectItem key={supplier.id} value={supplier.id.toString()}>
                            <div className="flex flex-col">
                              <span className="font-medium">{supplier.name}</span>
                              <span className="text-sm text-muted-foreground">
                                {supplier.location} • {supplier.speciality}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {newItem.supplierName && (
                    <Card className="border-blue-200 bg-blue-50">
                      <CardHeader>
                        <CardTitle className="text-lg">Selected Supplier</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm font-medium">Supplier: {newItem.supplierName}</p>
                            <p className="text-sm text-muted-foreground">Location: {newItem.location}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">
                              Speciality: {suppliers.find((s) => s.name === newItem.supplierName)?.speciality}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              Contact: {suppliers.find((s) => s.name === newItem.supplierName)?.contactPerson}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="productName">Product Name</Label>
                      <Input
                        id="productName"
                        value={newItem.productName || ""}
                        onChange={(e) => setNewItem({ ...newItem, productName: e.target.value })}
                        placeholder="Enter product name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="productType">Product Type</Label>
                      <Select onValueChange={(value) => setNewItem({ ...newItem, productType: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select product type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Gold Chain">Gold Chain</SelectItem>
                          <SelectItem value="Gold Bangles">Gold Bangles</SelectItem>
                          <SelectItem value="Diamond Studs">Diamond Studs</SelectItem>
                          <SelectItem value="Gold Ring">Gold Ring</SelectItem>
                          <SelectItem value="Necklace">Necklace</SelectItem>
                          <SelectItem value="Earrings">Earrings</SelectItem>
                          <SelectItem value="Pendant">Pendant</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="withStoneWeight">With Stone Weight (g)</Label>
                      <Input
                        id="withStoneWeight"
                        type="number"
                        step="0.001"
                        value={newItem.withStoneWeight || ""}
                        onChange={(e) =>
                          setNewItem({ ...newItem, withStoneWeight: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="0.000"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="withoutStoneWeight">Without Stone Weight (g)</Label>
                      <Input
                        id="withoutStoneWeight"
                        type="number"
                        step="0.001"
                        value={newItem.withoutStoneWeight || ""}
                        onChange={(e) =>
                          setNewItem({ ...newItem, withoutStoneWeight: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="0.000"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="procuredIn24k">Procured in 24K (g)</Label>
                      <Input
                        id="procuredIn24k"
                        type="number"
                        step="0.001"
                        value={newItem.procuredIn24k || ""}
                        onChange={(e) =>
                          setNewItem({ ...newItem, procuredIn24k: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="0.000"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="withStoneCost">With Stone Cost (%)</Label>
                      <Input
                        id="withStoneCost"
                        type="number"
                        value={newItem.withStoneCost || ""}
                        onChange={(e) =>
                          setNewItem({ ...newItem, withStoneCost: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="0"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="withoutStoneCost">Without Stone Cost (%)</Label>
                      <Input
                        id="withoutStoneCost"
                        type="number"
                        value={newItem.withoutStoneCost || ""}
                        onChange={(e) =>
                          setNewItem({ ...newItem, withoutStoneCost: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="0"
                      />
                    </div>
                  </div>

                  {newItem.supplierName && newItem.productName && (
                    <div className="p-4 bg-green-50 rounded-lg space-y-2">
                      <h4 className="font-semibold text-green-800">Inventory Summary</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p>
                            <span className="font-medium">Supplier:</span> {newItem.supplierName}
                          </p>
                          <p>
                            <span className="font-medium">Product:</span> {newItem.productName}
                          </p>
                          <p>
                            <span className="font-medium">Type:</span> {newItem.productType}
                          </p>
                        </div>
                        <div>
                          <p>
                            <span className="font-medium">Total Weight:</span>{" "}
                            {((newItem.withStoneWeight || 0) + (newItem.withoutStoneWeight || 0)).toFixed(3)}g
                          </p>
                          <p>
                            <span className="font-medium">24K Equivalent:</span>{" "}
                            {(newItem.procuredIn24k || 0).toFixed(3)}g
                          </p>
                          <p>
                            <span className="font-medium">22K Equivalent:</span>{" "}
                            {((newItem.procuredIn24k || 0) * 0.916).toFixed(3)}g
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddItem} disabled={!newItem.supplierName || !newItem.productName}>
                    Add Item
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search inventory..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Badge variant="secondary">{filteredInventory.length} items</Badge>
          </div>

          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Sl.No</TableHead>
                  <TableHead>Supplier Details</TableHead>
                  <TableHead>Product Weight (g)</TableHead>
                  <TableHead>Cost Price (%)</TableHead>
                  <TableHead>Procured 24K (g)</TableHead>
                  <TableHead>Sold Value</TableHead>
                  <TableHead>Balance Stock</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInventory.map((item, index) => (
                  <TableRow key={item.id}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{item.supplierName}</p>
                        <p className="text-sm text-muted-foreground">{item.location}</p>
                        <p className="text-sm font-medium text-amber-600">{item.productName}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm">With Stone: {item.withStoneWeight.toFixed(3)}</p>
                        <p className="text-sm">Without: {item.withoutStoneWeight.toFixed(3)}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm">With Stone: {item.withStoneCost}%</p>
                        <p className="text-sm">Without: {item.withoutStoneCost}%</p>
                      </div>
                    </TableCell>
                    <TableCell>{item.procuredIn24k.toFixed(3)}</TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm">22K Stone: {item.soldValueWithStone.toFixed(3)}</p>
                        <p className="text-sm">22K Plain: {item.soldValueWithoutStone.toFixed(3)}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm">24K: {item.balanceWeight24k.toFixed(3)}g</p>
                        <p className="text-sm">22K: {item.balanceWeight22k.toFixed(3)}g</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700 bg-transparent">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
