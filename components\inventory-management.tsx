"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Plus, Search, Edit, Trash2 } from "lucide-react"
import { useDatabase } from "@/hooks/use-database"
import { useDatabaseMutation } from "@/hooks/use-database-mutation"

interface InventoryItem {
  id: number
  supplier_name: string
  supplier_location: string
  product_name: string
  product_type: string
  with_stone_weight: number
  without_stone_weight: number
  with_stone_cost: number
  without_stone_cost: number
  procured_in_24k: number
  sold_value_with_stone: number
  sold_value_without_stone: number
  balance_weight_24k: number
  balance_weight_22k: number
  status: "Available" | "Low Stock" | "Out of Stock"
}

interface Supplier {
  id: number
  name: string
  location: string
  contact_person: string
  phone: string
  email: string
  speciality: string
}

export default function InventoryManagement() {
  const { data: inventory, loading, error, refetch } = useDatabase<InventoryItem>("inventory")
  const { data: suppliers } = useDatabase<Supplier>("suppliers")
  const { mutate, loading: mutating } = useDatabaseMutation<InventoryItem>()



  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newItem, setNewItem] = useState<Partial<InventoryItem>>({})

  const filteredInventory = inventory?.filter(
    (item) =>
      item.supplier_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.product_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.supplier_location?.toLowerCase().includes(searchTerm.toLowerCase()),
  ) || []

  const handleAddItem = async () => {
    if (newItem.supplier_id && newItem.product_name) {
      const itemData = {
        supplier_id: newItem.supplier_id,
        product_name: newItem.product_name,
        product_type: newItem.product_type || "",
        with_stone_weight: newItem.with_stone_weight || 0,
        without_stone_weight: newItem.without_stone_weight || 0,
        with_stone_cost: newItem.with_stone_cost || 0,
        without_stone_cost: newItem.without_stone_cost || 0,
        procured_in_24k: newItem.procured_in_24k || 0,
      }

      const result = await mutate("inventory", "POST", itemData)
      if (result) {
        refetch()
        setNewItem({})
        setIsAddDialogOpen(false)
      }
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading inventory...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600">Error loading inventory: {error}</p>
          <button onClick={() => refetch()} className="mt-2 text-blue-600 hover:underline">
            Try again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Inventory Management</CardTitle>
              <CardDescription>Manage your jewellery inventory with detailed tracking</CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Item
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add New Inventory Item</DialogTitle>
                  <DialogDescription>Select supplier and enter product details</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="space-y-2">
                    <Label>Select Supplier</Label>
                    <Select
                      onValueChange={(value) => {
                        const supplier = suppliers?.find((s) => s.id === Number.parseInt(value))
                        if (supplier) {
                          setNewItem({
                            ...newItem,
                            supplier_id: supplier.id,
                            supplier_name: supplier.name,
                            supplier_location: supplier.location,
                          })
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Choose existing supplier" />
                      </SelectTrigger>
                      <SelectContent>
                        {suppliers?.map((supplier) => (
                          <SelectItem key={supplier.id} value={supplier.id.toString()}>
                            <div className="flex flex-col">
                              <span className="font-medium">{supplier.name}</span>
                              <span className="text-sm text-muted-foreground">
                                {supplier.location} • {supplier.speciality}
                              </span>
                            </div>
                          </SelectItem>
                        )) || []}
                      </SelectContent>
                    </Select>
                  </div>

                  {newItem.supplier_name && (
                    <Card className="border-blue-200 bg-blue-50">
                      <CardHeader>
                        <CardTitle className="text-lg">Selected Supplier</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm font-medium">Supplier: {newItem.supplier_name}</p>
                            <p className="text-sm text-muted-foreground">Location: {newItem.supplier_location}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">
                              Speciality: {suppliers?.find((s) => s.id === newItem.supplier_id)?.speciality}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              Contact: {suppliers?.find((s) => s.id === newItem.supplier_id)?.contact_person}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="productName">Product Name</Label>
                      <Input
                        id="productName"
                        value={newItem.product_name || ""}
                        onChange={(e) => setNewItem({ ...newItem, product_name: e.target.value })}
                        placeholder="Enter product name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="productType">Product Type</Label>
                      <Select onValueChange={(value) => setNewItem({ ...newItem, product_type: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select product type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Gold Chain">Gold Chain</SelectItem>
                          <SelectItem value="Gold Bangles">Gold Bangles</SelectItem>
                          <SelectItem value="Diamond Studs">Diamond Studs</SelectItem>
                          <SelectItem value="Gold Ring">Gold Ring</SelectItem>
                          <SelectItem value="Necklace">Necklace</SelectItem>
                          <SelectItem value="Earrings">Earrings</SelectItem>
                          <SelectItem value="Pendant">Pendant</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="withStoneWeight">With Stone Weight (g)</Label>
                      <Input
                        id="withStoneWeight"
                        type="number"
                        step="0.001"
                        value={newItem.with_stone_weight || ""}
                        onChange={(e) =>
                          setNewItem({ ...newItem, with_stone_weight: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="0.000"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="withoutStoneWeight">Without Stone Weight (g)</Label>
                      <Input
                        id="withoutStoneWeight"
                        type="number"
                        step="0.001"
                        value={newItem.without_stone_weight || ""}
                        onChange={(e) =>
                          setNewItem({ ...newItem, without_stone_weight: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="0.000"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="procuredIn24k">Procured in 24K (g)</Label>
                      <Input
                        id="procuredIn24k"
                        type="number"
                        step="0.001"
                        value={newItem.procured_in_24k || ""}
                        onChange={(e) =>
                          setNewItem({ ...newItem, procured_in_24k: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="0.000"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="withStoneCost">With Stone Cost (%)</Label>
                      <Input
                        id="withStoneCost"
                        type="number"
                        value={newItem.with_stone_cost || ""}
                        onChange={(e) =>
                          setNewItem({ ...newItem, with_stone_cost: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="0"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="withoutStoneCost">Without Stone Cost (%)</Label>
                      <Input
                        id="withoutStoneCost"
                        type="number"
                        value={newItem.without_stone_cost || ""}
                        onChange={(e) =>
                          setNewItem({ ...newItem, without_stone_cost: Number.parseFloat(e.target.value) || 0 })
                        }
                        placeholder="0"
                      />
                    </div>
                  </div>

                  {newItem.supplier_name && newItem.product_name && (
                    <div className="p-4 bg-green-50 rounded-lg space-y-2">
                      <h4 className="font-semibold text-green-800">Inventory Summary</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p>
                            <span className="font-medium">Supplier:</span> {newItem.supplier_name}
                          </p>
                          <p>
                            <span className="font-medium">Product:</span> {newItem.product_name}
                          </p>
                          <p>
                            <span className="font-medium">Type:</span> {newItem.product_type}
                          </p>
                        </div>
                        <div>
                          <p>
                            <span className="font-medium">Total Weight:</span>{" "}
                            {((newItem.with_stone_weight || 0) + (newItem.without_stone_weight || 0)).toFixed(3)}g
                          </p>
                          <p>
                            <span className="font-medium">24K Equivalent:</span>{" "}
                            {(newItem.procured_in_24k || 0).toFixed(3)}g
                          </p>
                          <p>
                            <span className="font-medium">22K Equivalent:</span>{" "}
                            {((newItem.procured_in_24k || 0) * 0.916).toFixed(3)}g
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddItem} disabled={!newItem.supplier_id || !newItem.product_name || mutating}>
                    {mutating ? "Adding..." : "Add Item"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search inventory..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Badge variant="secondary">{filteredInventory.length} items</Badge>
          </div>

          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Sl.No</TableHead>
                  <TableHead>Supplier Details</TableHead>
                  <TableHead>Product Weight (g)</TableHead>
                  <TableHead>Cost Price (%)</TableHead>
                  <TableHead>Procured 24K (g)</TableHead>
                  <TableHead>Sold Value</TableHead>
                  <TableHead>Balance Stock</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInventory.map((item, index) => (
                  <TableRow key={item.id}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{item.supplier_name}</p>
                        <p className="text-sm text-muted-foreground">{item.supplier_location}</p>
                        <p className="text-sm font-medium text-amber-600">{item.product_name}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm">With Stone: {(Number(item.with_stone_weight) || 0).toFixed(3)}</p>
                        <p className="text-sm">Without: {(Number(item.without_stone_weight) || 0).toFixed(3)}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm">With Stone: {Number(item.with_stone_cost) || 0}%</p>
                        <p className="text-sm">Without: {Number(item.without_stone_cost) || 0}%</p>
                      </div>
                    </TableCell>
                    <TableCell>{(Number(item.procured_in_24k) || 0).toFixed(3)}</TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm">22K Stone: {(Number(item.sold_value_with_stone) || 0).toFixed(3)}</p>
                        <p className="text-sm">22K Plain: {(Number(item.sold_value_without_stone) || 0).toFixed(3)}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm">24K: {(Number(item.balance_weight_24k) || 0).toFixed(3)}g</p>
                        <p className="text-sm">22K: {(Number(item.balance_weight_22k) || 0).toFixed(3)}g</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700 bg-transparent">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
