const mysql = require('mysql2/promise');
require('dotenv').config();

async function createSalesTransactionsTable() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jewellery_wholesale_software'
    });

    console.log('Connected to database');

    // Check if sales_transactions table exists
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'sales_transactions'
    `, [process.env.DB_NAME || 'jewellery_wholesale_software']);

    if (tables.length === 0) {
      console.log('Creating sales_transactions table...');
      
      await connection.execute(`
        CREATE TABLE sales_transactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            inventory_id INT NOT NULL,
            customer_id INT,
            transaction_type ENUM('Sale', 'Return', 'Exchange', 'Wastage') DEFAULT 'Sale',
            
            -- Weight sold in different purities
            weight_24k DECIMAL(10,3) DEFAULT 0.000,
            weight_22k DECIMAL(10,3) DEFAULT 0.000,
            weight_18k DECIMAL(10,3) DEFAULT 0.000,
            
            -- Stone details if applicable
            stone_weight DECIMAL(10,3) DEFAULT 0.000,
            stone_value DECIMAL(10,2) DEFAULT 0.00,
            
            -- Pricing details
            rate_24k DECIMAL(10,2) DEFAULT 0.00,
            rate_22k DECIMAL(10,2) DEFAULT 0.00,
            making_charges DECIMAL(10,2) DEFAULT 0.00,
            total_amount DECIMAL(15,2) DEFAULT 0.00,
            
            -- Transaction details
            transaction_date DATE DEFAULT (CURRENT_DATE),
            bill_number VARCHAR(50),
            notes TEXT,
            
            -- Audit fields
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_by VARCHAR(100),
            
            -- Foreign keys and indexes
            FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
            INDEX idx_sales_inventory_id (inventory_id),
            INDEX idx_sales_customer_id (customer_id),
            INDEX idx_sales_transaction_date (transaction_date),
            INDEX idx_sales_transaction_type (transaction_type),
            INDEX idx_sales_bill_number (bill_number)
        )
      `);
      
      console.log('✅ Sales transactions table created successfully');
    } else {
      console.log('✅ Sales transactions table already exists');
    }

    // Check if wastage_records table exists
    const [wastageTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'wastage_records'
    `, [process.env.DB_NAME || 'jewellery_wholesale_software']);

    if (wastageTables.length === 0) {
      console.log('Creating wastage_records table...');
      
      await connection.execute(`
        CREATE TABLE wastage_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            inventory_id INT NOT NULL,
            wastage_type ENUM('Processing', 'Manufacturing', 'Refining', 'Loss', 'Other') DEFAULT 'Processing',
            
            -- Wastage amounts
            weight_24k DECIMAL(10,3) DEFAULT 0.000,
            weight_22k DECIMAL(10,3) DEFAULT 0.000,
            weight_18k DECIMAL(10,3) DEFAULT 0.000,
            
            -- Wastage details
            wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
            reason TEXT,
            process_stage VARCHAR(100),
            
            -- Recovery details (if any metal recovered)
            recovered_weight DECIMAL(10,3) DEFAULT 0.000,
            recovered_purity ENUM('24K', '22K', '18K', 'Mixed') DEFAULT '24K',
            
            -- Record details
            recorded_date DATE DEFAULT (CURRENT_DATE),
            recorded_by VARCHAR(100),
            notes TEXT,
            
            -- Audit fields
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            -- Foreign keys and indexes
            FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE,
            INDEX idx_wastage_inventory_id (inventory_id),
            INDEX idx_wastage_type (wastage_type),
            INDEX idx_wastage_date (recorded_date)
        )
      `);
      
      console.log('✅ Wastage records table created successfully');
    } else {
      console.log('✅ Wastage records table already exists');
    }

    console.log('\n🎉 Sales and wastage tracking system created successfully!');

  } catch (error) {
    console.error('Error creating tables:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
createSalesTransactionsTable();
