import { type NextRequest, NextResponse } from "next/server"
import { CustomerModel } from "@/lib/models/customer"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search")

    let customers
    if (search) {
      customers = await CustomerModel.search(search)
    } else {
      customers = await CustomerModel.getAll()
    }

    return NextResponse.json({ success: true, data: customers })
  } catch (error) {
    console.error("Error fetching customers:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch customers" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.name || !data.contact_person) {
      return NextResponse.json({ success: false, error: "Name and contact person are required" }, { status: 400 })
    }

    const customerId = await CustomerModel.create(data)
    const customer = await CustomerModel.getById(customerId)

    return NextResponse.json({ success: true, data: customer }, { status: 201 })
  } catch (error) {
    console.error("Error creating customer:", error)
    return NextResponse.json({ success: false, error: "Failed to create customer" }, { status: 500 })
  }
}
