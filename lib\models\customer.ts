import { executeQuery, executeSingle, executeUpdate } from "../database"

export interface Customer {
  id: number
  name: string
  location: string
  contact_person: string
  phone: string
  email: string
  address: string
  total_purchases: number
  last_purchase_date: string
  status: "Active" | "Inactive"
  created_at: string
  updated_at: string
}

export interface CreateCustomerData {
  name: string
  location?: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
}

export class CustomerModel {
  // Get all customers
  static async getAll(): Promise<Customer[]> {
    const query = `
      SELECT * FROM customers 
      ORDER BY created_at DESC
    `
    return executeQuery<Customer>(query)
  }

  // Get customer by ID
  static async getById(id: number): Promise<Customer | null> {
    const query = "SELECT * FROM customers WHERE id = ?"
    const results = await executeQuery<Customer>(query, [id])
    return results[0] || null
  }

  // Create new customer
  static async create(data: CreateCustomerData): Promise<number> {
    const query = `
      INSERT INTO customers (name, location, contact_person, phone, email, address)
      VALUES (?, ?, ?, ?, ?, ?)
    `
    const params = [
      data.name,
      data.location || null,
      data.contact_person || null,
      data.phone || null,
      data.email || null,
      data.address || null,
    ]
    const result = await executeUpdate(query, params)
    return result.insertId!
  }

  // Update customer
  static async update(id: number, data: Partial<CreateCustomerData>): Promise<boolean> {
    const fields = Object.keys(data)
      .map((key) => `${key} = ?`)
      .join(", ")
    const values = Object.values(data)

    const query = `UPDATE customers SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`
    const result = await executeUpdate(query, [...values, id])
    return result.affectedRows > 0
  }

  // Delete customer
  static async delete(id: number): Promise<boolean> {
    const query = "DELETE FROM customers WHERE id = ?"
    const result = await executeUpdate(query, [id])
    return result.affectedRows > 0
  }

  // Search customers
  static async search(searchTerm: string): Promise<Customer[]> {
    const query = `
      SELECT * FROM customers 
      WHERE name LIKE ? OR location LIKE ? OR contact_person LIKE ?
      ORDER BY name
    `
    const searchPattern = `%${searchTerm}%`
    return executeQuery<Customer>(query, [searchPattern, searchPattern, searchPattern])
  }

  // Update total purchases
  static async updateTotalPurchases(id: number, amount: number): Promise<boolean> {
    const query = `
      UPDATE customers 
      SET total_purchases = total_purchases + ?, 
          last_purchase_date = CURRENT_DATE,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `
    const result = await executeUpdate(query, [amount, id])
    return result.affectedRows > 0
  }

  // Get top customers
  static async getTopCustomers(limit = 10): Promise<Customer[]> {
    const query = `
      SELECT * FROM customers 
      WHERE status = 'Active'
      ORDER BY total_purchases DESC 
      LIMIT ?
    `
    return executeQuery<Customer>(query, [limit])
  }
}
