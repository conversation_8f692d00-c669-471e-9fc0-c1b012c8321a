import { executeQuery, executeSingle } from "../database"

export interface User {
  id: number
  name: string
  email: string
  password_hash?: string
  role: "<PERSON><PERSON>" | "Manager" | "Staff" | "Viewer"
  status: "Active" | "Inactive"
  last_login: string | null
  permissions: {
    inventory: boolean
    billing: boolean
    reports: boolean
    suppliers: boolean
    goldRates: boolean
    backup: boolean
  }
  created_at: string
  updated_at: string
}

export interface CreateUserData {
  name: string
  email: string
  password_hash?: string
  role: "Admin" | "Manager" | "Staff" | "Viewer"
  permissions: {
    inventory: boolean
    billing: boolean
    reports: boolean
    suppliers: boolean
    goldRates: boolean
    backup: boolean
  }
}

export class UserModel {
  // Get all users
  static async getAll(): Promise<User[]> {
    const query =
      "SELECT id, name, email, role, status, last_login, permissions, created_at, updated_at FROM users ORDER BY created_at DESC"
    const results = await executeQuery<any>(query)
    return results.map((user) => ({
      ...user,
      permissions: typeof user.permissions === "string" ? JSON.parse(user.permissions) : user.permissions,
    }))
  }

  // Get user by ID
  static async getById(id: number): Promise<User | null> {
    const query =
      "SELECT id, name, email, role, status, last_login, permissions, created_at, updated_at FROM users WHERE id = ?"
    const results = await executeQuery<any>(query, [id])
    if (!results[0]) return null

    return {
      ...results[0],
      permissions:
        typeof results[0].permissions === "string" ? JSON.parse(results[0].permissions) : results[0].permissions,
    }
  }

  // Get user by email
  static async getByEmail(email: string): Promise<User | null> {
    const query = "SELECT * FROM users WHERE email = ?"
    const results = await executeQuery<any>(query, [email])
    if (!results[0]) return null

    return {
      ...results[0],
      permissions:
        typeof results[0].permissions === "string" ? JSON.parse(results[0].permissions) : results[0].permissions,
    }
  }

  // Create new user
  static async create(data: CreateUserData): Promise<number> {
    const query = `
      INSERT INTO users (name, email, password_hash, role, permissions)
      VALUES (?, ?, ?, ?, ?)
    `
    const params = [data.name, data.email, data.password_hash || null, data.role, JSON.stringify(data.permissions)]
    const result = await executeSingle(query, params)
    return result.insertId!
  }

  // Update user
  static async update(id: number, data: Partial<CreateUserData>): Promise<boolean> {
    const updateData = { ...data }
    if (updateData.permissions) {
      ;(updateData as any).permissions = JSON.stringify(updateData.permissions)
    }

    const fields = Object.keys(updateData)
      .map((key) => `${key} = ?`)
      .join(", ")
    const values = Object.values(updateData)

    const query = `UPDATE users SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`
    const result = await executeSingle(query, [...values, id])
    return result.affectedRows > 0
  }

  // Update user status
  static async updateStatus(id: number, status: "Active" | "Inactive"): Promise<boolean> {
    const query = "UPDATE users SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
    const result = await executeSingle(query, [status, id])
    return result.affectedRows > 0
  }

  // Update last login
  static async updateLastLogin(id: number): Promise<boolean> {
    const query = "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?"
    const result = await executeSingle(query, [id])
    return result.affectedRows > 0
  }

  // Delete user
  static async delete(id: number): Promise<boolean> {
    const query = "DELETE FROM users WHERE id = ?"
    const result = await executeSingle(query, [id])
    return result.affectedRows > 0
  }

  // Search users
  static async search(searchTerm: string): Promise<User[]> {
    const query = `
      SELECT id, name, email, role, status, last_login, permissions, created_at, updated_at 
      FROM users 
      WHERE name LIKE ? OR email LIKE ? OR role LIKE ?
      ORDER BY name
    `
    const searchPattern = `%${searchTerm}%`
    const results = await executeQuery<any>(query, [searchPattern, searchPattern, searchPattern])
    return results.map((user) => ({
      ...user,
      permissions: typeof user.permissions === "string" ? JSON.parse(user.permissions) : user.permissions,
    }))
  }
}
