/**
 * Server-Side Business Logic Utilities
 * Centralized business calculations using database settings
 * FOR SERVER-SIDE USE ONLY (API routes, server components)
 */

import "server-only"
import { SettingsModel } from "@/lib/models/settings"

export interface BusinessCalculations {
  // Conversion calculations
  convertPurity(weight: number, fromPurity: string, toPurity: string, userId?: number): Promise<number>
  
  // Wastage calculations
  calculateWastage(weight: number, formType: string, userId?: number): Promise<number>
  calculateExpectedYield(procuredWeight: number, formType: string, processingLoss?: number, userId?: number): Promise<number>
  
  // Pricing calculations
  calculateGoldValue(weight: number, purity: string, goldRates: any): number
  calculateMakingCharges(weight: number, userId?: number): Promise<number>
  calculateGST(amount: number, userId?: number): Promise<number>
  calculateTotalAmount(goldValue: number, makingCharges: number, stoneValue: number, userId?: number): Promise<number>
  
  // Weight calculations
  calculateNetWeight(grossWeight: number, stoneWeight: number): number
  calculate24KEquivalent(weight: number, tunchPercentage: number): number
  
  // Validation utilities
  validateWeight(weight: number): boolean
  validateTunch(tunch: number): boolean
  validateRate(rate: number): boolean
}

export class BusinessLogic implements BusinessCalculations {
  
  // =====================================================
  // CONVERSION CALCULATIONS
  // =====================================================
  
  static async convertPurity(
    weight: number, 
    fromPurity: string, 
    toPurity: string, 
    userId?: number
  ): Promise<number> {
    if (weight <= 0) return 0
    if (fromPurity === toPurity) return weight
    
    try {
      const factor = await SettingsModel.getConversionFactor(fromPurity, toPurity, userId)
      return weight * factor
    } catch (error) {
      console.error('Error in purity conversion:', error)
      // Fallback to default conversion factors
      const defaultFactors: { [key: string]: number } = {
        '24k_to_22k': 0.916,
        '24k_to_18k': 0.750,
        '22k_to_24k': 1.092,
        '18k_to_24k': 1.333
      }
      const key = `${fromPurity.toLowerCase()}_to_${toPurity.toLowerCase()}`
      return weight * (defaultFactors[key] || 1.0)
    }
  }
  
  // =====================================================
  // WASTAGE CALCULATIONS
  // =====================================================
  
  static async calculateWastage(
    weight: number, 
    formType: string, 
    userId?: number
  ): Promise<number> {
    if (weight <= 0) return 0
    
    try {
      const wastageRate = await SettingsModel.getWastageRate(formType, userId)
      return (weight * wastageRate) / 100
    } catch (error) {
      console.error('Error calculating wastage:', error)
      // Fallback to default rates
      const defaultRates: { [key: string]: number } = {
        'Bar': 0.5,
        'Jewel': 2.0,
        'Old Jewel': 3.0
      }
      const rate = defaultRates[formType] || 2.0
      return (weight * rate) / 100
    }
  }
  
  static async calculateExpectedYield(
    procuredWeight: number, 
    formType: string, 
    processingLoss: number = 0, 
    userId?: number
  ): Promise<number> {
    if (procuredWeight <= 0) return 0
    
    const wastage = await this.calculateWastage(procuredWeight, formType, userId)
    return Math.max(0, procuredWeight - wastage - processingLoss)
  }
  
  // =====================================================
  // PRICING CALCULATIONS
  // =====================================================
  
  static calculateGoldValue(weight: number, purity: string, goldRates: any): number {
    if (weight <= 0) return 0
    
    const rate = goldRates[`rate_${purity.toLowerCase()}`] || goldRates.rate_24k || 0
    return weight * rate
  }
  
  static async calculateMakingCharges(weight: number, userId?: number): Promise<number> {
    if (weight <= 0) return 0
    
    try {
      const makingChargeRate = await SettingsModel.getValue('pricing', 'default_making_charges', userId)
      return weight * (makingChargeRate || 500)
    } catch (error) {
      console.error('Error calculating making charges:', error)
      return weight * 500 // Default fallback
    }
  }
  
  static async calculateGST(amount: number, userId?: number): Promise<number> {
    if (amount <= 0) return 0
    
    try {
      const applyGST = await SettingsModel.getValue('pricing', 'apply_gst', userId)
      if (!applyGST) return 0
      
      const gstRate = await SettingsModel.getValue('pricing', 'gst_rate', userId)
      return (amount * (gstRate || 3)) / 100
    } catch (error) {
      console.error('Error calculating GST:', error)
      return (amount * 3) / 100 // Default 3% GST
    }
  }
  
  static async calculateTotalAmount(
    goldValue: number, 
    makingCharges: number, 
    stoneValue: number = 0, 
    userId?: number
  ): Promise<number> {
    const subtotal = goldValue + makingCharges + stoneValue
    const gst = await this.calculateGST(subtotal, userId)
    return subtotal + gst
  }
  
  // =====================================================
  // WEIGHT CALCULATIONS
  // =====================================================
  
  static calculateNetWeight(grossWeight: number, stoneWeight: number = 0): number {
    return Math.max(0, grossWeight - stoneWeight)
  }
  
  static calculate24KEquivalent(weight: number, tunchPercentage: number): number {
    if (weight <= 0 || tunchPercentage <= 0) return 0
    return (weight * tunchPercentage) / 100
  }

  // =====================================================
  // JEWELLERY WHOLESALE SPECIFIC CALCULATIONS
  // =====================================================

  /**
   * Calculate 24K weight from 22K weight using tunch percentage
   * Based on CSV sample: 10.160g (22k) with 96% tunch = 9.754g (24k)
   */
  static calculate24KFromTunch(weight22k: number, tunchPercentage: number): number {
    if (weight22k <= 0 || tunchPercentage <= 0) return 0
    return Number(((weight22k * tunchPercentage) / 100).toFixed(3))
  }

  /**
   * Calculate 22K weight from 24K weight using tunch percentage
   * Reverse calculation for inventory management
   */
  static calculate22KFromTunch(weight24k: number, tunchPercentage: number): number {
    if (weight24k <= 0 || tunchPercentage <= 0) return 0
    return Number(((weight24k * 100) / tunchPercentage).toFixed(3))
  }

  /**
   * Calculate total bill amount using CSV sample logic
   * Formula: (24K weight × 24K price per gram) + stone amount = total
   */
  static calculateBillTotal(
    weight24k: number, 
    goldRate24k: number, 
    stoneAmount: number = 0
  ): number {
    if (weight24k <= 0 || goldRate24k <= 0) return stoneAmount
    const goldValue = weight24k * goldRate24k
    return Number((goldValue + stoneAmount).toFixed(2))
  }

  /**
   * Calculate balance stock after sale
   * Formula: Procured - Sold = Balance
   */
  static calculateStockBalance(
    procuredWeight: number, 
    soldWeight: number
  ): number {
    const balance = procuredWeight - soldWeight
    return Math.max(0, Number(balance.toFixed(3)))
  }

  /**
   * Validate tunch percentage based on typical jewellery ranges
   * Typical ranges: 85% - 101% for different purities
   */
  static validateTunchPercentage(tunch: number): boolean {
    return typeof tunch === 'number' && tunch >= 85 && tunch <= 101
  }

  /**
   * Get suggested tunch percentage based on product type
   * Based on CSV sample data patterns
   */
  static getSuggestedTunchPercentage(productType: string, hasStone: boolean): number {
    const baseRates: { [key: string]: number } = {
      'Chain': hasStone ? 93 : 96,
      'Bangles': hasStone ? 94 : 98,
      'Studs': hasStone ? 95 : 99,
      'Ring': hasStone ? 94 : 97,
      'Necklace': hasStone ? 93 : 96,
      'Earrings': hasStone ? 95 : 98,
      'Pendant': hasStone ? 94 : 97
    }
    
    return baseRates[productType] || (hasStone ? 94 : 97)
  }
  
  // =====================================================
  // VALIDATION UTILITIES
  // =====================================================
  
  static validateWeight(weight: number): boolean {
    return typeof weight === 'number' && weight > 0 && weight < 100000 // Max 100kg
  }
  
  static validateTunch(tunch: number): boolean {
    return typeof tunch === 'number' && tunch > 0 && tunch <= 100
  }
  
  static validateRate(rate: number): boolean {
    return typeof rate === 'number' && rate > 0 && rate < 1000000 // Max ₹10L per gram
  }
  
  // =====================================================
  // ROUNDING UTILITIES
  // =====================================================
  
  static async roundToSettingsPrecision(value: number, userId?: number): Promise<number> {
    try {
      const precision = await SettingsModel.getValue('calculation', 'rounding_precision', userId)
      return Number(value.toFixed(precision || 3))
    } catch (error) {
      return Number(value.toFixed(3)) // Default 3 decimal places
    }
  }
  
  // =====================================================
  // STOCK MANAGEMENT
  // =====================================================
  
  static async isLowStock(currentStock: number, userId?: number): Promise<boolean> {
    try {
      const threshold = await SettingsModel.getValue('business', 'low_stock_threshold', userId)
      return currentStock <= (threshold || 10)
    } catch (error) {
      return currentStock <= 10 // Default 10g threshold
    }
  }
  
  static async shouldTriggerWastageAlert(wastagePercentage: number, userId?: number): Promise<boolean> {
    try {
      const threshold = await SettingsModel.getValue('alerts', 'high_wastage_threshold', userId)
      return wastagePercentage >= (threshold || 5)
    } catch (error) {
      return wastagePercentage >= 5 // Default 5% threshold
    }
  }
  
  // =====================================================
  // HELPER METHODS FOR API
  // =====================================================

  static async getWastageRate(formType: string, userId?: number): Promise<number> {
    return await SettingsModel.getWastageRate(formType, userId)
  }

  static async getConversionFactor(fromPurity: string, toPurity: string, userId?: number): Promise<number> {
    return await SettingsModel.getConversionFactor(fromPurity, toPurity, userId)
  }

  // =====================================================
  // BATCH CALCULATIONS
  // =====================================================
  
  static async calculateBatchTotals(
    items: Array<{
      weight: number
      purity: string
      formType: string
      stoneValue?: number
    }>,
    goldRates: any,
    userId?: number
  ): Promise<{
    totalWeight: number
    totalGoldValue: number
    totalMakingCharges: number
    totalStoneValue: number
    totalWastage: number
    grandTotal: number
  }> {
    let totalWeight = 0
    let totalGoldValue = 0
    let totalMakingCharges = 0
    let totalStoneValue = 0
    let totalWastage = 0
    
    for (const item of items) {
      totalWeight += item.weight
      totalGoldValue += this.calculateGoldValue(item.weight, item.purity, goldRates)
      totalMakingCharges += await this.calculateMakingCharges(item.weight, userId)
      totalStoneValue += item.stoneValue || 0
      totalWastage += await this.calculateWastage(item.weight, item.formType, userId)
    }
    
    const grandTotal = await this.calculateTotalAmount(
      totalGoldValue, 
      totalMakingCharges, 
      totalStoneValue, 
      userId
    )
    
    return {
      totalWeight: await this.roundToSettingsPrecision(totalWeight, userId),
      totalGoldValue: await this.roundToSettingsPrecision(totalGoldValue, userId),
      totalMakingCharges: await this.roundToSettingsPrecision(totalMakingCharges, userId),
      totalStoneValue: await this.roundToSettingsPrecision(totalStoneValue, userId),
      totalWastage: await this.roundToSettingsPrecision(totalWastage, userId),
      grandTotal: await this.roundToSettingsPrecision(grandTotal, userId)
    }
  }
}

// Export both class and individual functions for flexibility
export const {
  convertPurity,
  calculateWastage,
  calculateExpectedYield,
  calculateGoldValue,
  calculateMakingCharges,
  calculateGST,
  calculateTotalAmount,
  calculateNetWeight,
  calculate24KEquivalent,
  calculate24KFromTunch,
  calculate22KFromTunch,
  calculateBillTotal,
  calculateStockBalance,
  validateTunchPercentage,
  getSuggestedTunchPercentage,
  validateWeight,
  validateTunch,
  validateRate,
  roundToSettingsPrecision,
  isLowStock,
  shouldTriggerWastageAlert,
  calculateBatchTotals
} = BusinessLogic
