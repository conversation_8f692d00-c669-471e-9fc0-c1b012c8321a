#!/usr/bin/env node

require("dotenv").config({ path: ".env.local" })
const mysql = require("mysql2/promise")

async function setupTables() {
  console.log("🗄️  Setting up database tables...\n")

  const config = {
    host: process.env.DB_HOST || "localhost",
    port: Number.parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "jewellery_wholesale_software"
  }

  console.log("📋 Connection Details:")
  console.log(`   Host: ${config.host}`)
  console.log(`   Port: ${config.port}`)
  console.log(`   User: ${config.user}`)
  console.log(`   Database: ${config.database}`)
  console.log("")

  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(config)
    console.log("✅ Connected to database")

    // Drop existing tables
    console.log("🧹 Cleaning up existing tables...")
    const dropTables = [
      "DROP TABLE IF EXISTS bills",
      "DROP TABLE IF EXISTS inventory", 
      "DROP TABLE IF EXISTS customers",
      "DROP TABLE IF EXISTS suppliers",
      "DROP TABLE IF EXISTS gold_rates",
      "DROP TABLE IF EXISTS users"
    ]

    for (const dropSQL of dropTables) {
      await connection.execute(dropSQL)
    }

    // Create suppliers table
    console.log("📊 Creating suppliers table...")
    await connection.execute(`
      CREATE TABLE suppliers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        location VARCHAR(255),
        contact_person VARCHAR(255),
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        speciality VARCHAR(255),
        total_purchases DECIMAL(15,2) DEFAULT 0,
        last_purchase_date DATE,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_suppliers_name (name),
        INDEX idx_suppliers_status (status)
      )
    `)

    // Create customers table
    console.log("📊 Creating customers table...")
    await connection.execute(`
      CREATE TABLE customers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        location VARCHAR(255),
        contact_person VARCHAR(255),
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        total_purchases DECIMAL(15,2) DEFAULT 0,
        last_purchase_date DATE,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_customers_name (name),
        INDEX idx_customers_status (status)
      )
    `)

    // Create inventory table
    console.log("📊 Creating inventory table...")
    await connection.execute(`
      CREATE TABLE inventory (
        id INT AUTO_INCREMENT PRIMARY KEY,
        supplier_id INT,
        product_name VARCHAR(255) NOT NULL,
        product_type VARCHAR(255),
        with_stone_weight DECIMAL(10,3) DEFAULT 0,
        without_stone_weight DECIMAL(10,3) DEFAULT 0,
        with_stone_cost DECIMAL(10,2) DEFAULT 0,
        without_stone_cost DECIMAL(10,2) DEFAULT 0,
        procured_in_24k DECIMAL(10,3) DEFAULT 0,
        sold_value_with_stone DECIMAL(10,2) DEFAULT 0,
        sold_value_without_stone DECIMAL(10,2) DEFAULT 0,
        balance_weight_24k DECIMAL(10,3) DEFAULT 0,
        balance_weight_22k DECIMAL(10,3) DEFAULT 0,
        status ENUM('Available', 'Sold', 'Reserved') DEFAULT 'Available',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
        INDEX idx_inventory_supplier_id (supplier_id),
        INDEX idx_inventory_product_name (product_name),
        INDEX idx_inventory_status (status)
      )
    `)

    // Create bills table
    console.log("📊 Creating bills table...")
    await connection.execute(`
      CREATE TABLE bills (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT,
        bill_number VARCHAR(50) UNIQUE,
        product_name VARCHAR(255),
        product_type VARCHAR(255),
        with_stone DECIMAL(10,3) DEFAULT 0,
        without_stone DECIMAL(10,3) DEFAULT 0,
        gross_weight DECIMAL(10,3) DEFAULT 0,
        stone_weight DECIMAL(10,3) DEFAULT 0,
        net_weight DECIMAL(10,3) DEFAULT 0,
        tunch_with_stone INT DEFAULT 0,
        tunch_without_stone INT DEFAULT 0,
        weight_in_24k DECIMAL(10,3) DEFAULT 0,
        gold_24k_price DECIMAL(10,2) DEFAULT 0,
        stone_price DECIMAL(10,2) DEFAULT 0,
        making_charges DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(15,2) DEFAULT 0,
        status ENUM('Pending', 'Paid', 'Cancelled') DEFAULT 'Pending',
        bill_date DATE DEFAULT (CURRENT_DATE),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
        INDEX idx_bills_customer_id (customer_id),
        INDEX idx_bills_bill_date (bill_date),
        INDEX idx_bills_status (status)
      )
    `)

    // Create gold_rates table
    console.log("📊 Creating gold_rates table...")
    await connection.execute(`
      CREATE TABLE gold_rates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        rate_24k DECIMAL(10,2) NOT NULL,
        rate_22k DECIMAL(10,2) NOT NULL,
        rate_date DATE DEFAULT (CURRENT_DATE),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_gold_rates_date (rate_date)
      )
    `)

    // Create users table
    console.log("📊 Creating users table...")
    await connection.execute(`
      CREATE TABLE users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        full_name VARCHAR(255),
        role ENUM('admin', 'manager', 'user') DEFAULT 'user',
        permissions JSON,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_users_username (username),
        INDEX idx_users_role (role),
        INDEX idx_users_status (status)
      )
    `)

    // Verify tables were created
    console.log("🔍 Verifying tables...")
    const [tables] = await connection.execute("SHOW TABLES")
    
    if (tables.length > 0) {
      console.log(`✅ Created ${tables.length} tables:`)
      tables.forEach((table) => {
        const tableName = Object.values(table)[0]
        console.log(`   - ${tableName}`)
      })
    } else {
      console.log("⚠️  No tables found")
    }

    await connection.end()
    console.log("\n🎉 Database tables setup completed successfully!")
    
  } catch (error) {
    console.log("\n❌ Database setup failed:")
    console.log(`   Error: ${error.message}`)
    process.exit(1)
  }
}

setupTables()
