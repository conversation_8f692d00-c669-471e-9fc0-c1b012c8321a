async function checkApiVsDb() {
  console.log('🔍 CHECKING API VS DATABASE DISCREPANCY');
  console.log('=======================================\n');

  console.log('📡 CHECKING API RESPONSE:');
  console.log('=========================');
  
  try {
    const response = await fetch('http://localhost:3000/api/inventory');
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ API Response: ${data.success ? 'Success' : 'Failed'}`);
      console.log(`📊 Items returned: ${data.data ? data.data.length : 0}`);
      
      if (data.data && data.data.length > 0) {
        const item = data.data[0];
        console.log('\n📋 FIRST ITEM FROM API:');
        console.log('======================');
        console.log(`ID: ${item.id}`);
        console.log(`Product: "${item.product_name}"`);
        console.log(`Type: "${item.product_type}"`);
        console.log(`Procured 24K: ${item.procured_in_24k}`);
        console.log(`Balance 24K: ${item.balance_weight_24k}`);
        console.log(`Balance 22K: ${item.balance_weight_22k}`);
        console.log(`Without Stone Weight: ${item.without_stone_weight}`);
        console.log(`Without Stone Cost: ${item.without_stone_cost}`);
      }
    } else {
      console.log('❌ API request failed');
    }
  } catch (error) {
    console.log(`❌ API Error: ${error.message}`);
  }

  console.log('\n🔍 POSSIBLE CAUSES:');
  console.log('==================');
  console.log('1. Different database connection in API vs script');
  console.log('2. Different database name or credentials');
  console.log('3. API using SQLite while script uses MySQL');
  console.log('4. Environment variables not loaded in script');

  console.log('\n💡 SOLUTION APPROACH:');
  console.log('=====================');
  console.log('Since API has the data, we can:');
  console.log('1. Use the API to update the item');
  console.log('2. Or add new item with correct balance weights');
  console.log('3. Or use the edit functionality in the UI');

  console.log('\n📝 RECOMMENDED ACTION:');
  console.log('======================');
  console.log('1. Go to the inventory page in browser');
  console.log('2. Click the Edit button on the Chain item');
  console.log('3. Update the balance weights:');
  console.log('   - Balance Weight 24K: 9.754');
  console.log('   - Balance Weight 22K: 110.260');
  console.log('4. Save the changes');

  console.log('\n🎯 ALTERNATIVE: ADD NEW ITEM');
  console.log('============================');
  console.log('If editing doesn\'t work, add a new item with:');
  console.log('- All the same data as before');
  console.log('- But fill in the balance weight fields:');
  console.log('  * Balance Weight 24K: 9.754');
  console.log('  * Balance Weight 22K: 110.260');
  console.log('- Then delete the old incorrect item');

  console.log('\n🔧 FORM FIELD REMINDER:');
  console.log('=======================');
  console.log('When adding/editing inventory:');
  console.log('1. Fill all the basic fields (supplier, product, etc.)');
  console.log('2. In "Business Parameters" section:');
  console.log('   - Balance Weight 24K: 9.754');
  console.log('   - Balance Weight 22K: 110.260');
  console.log('3. These fields override the calculated values');
  console.log('4. Submit the form');

  console.log('\n🎉 EXPECTED RESULT:');
  console.log('==================');
  console.log('After updating/adding with correct balance weights:');
  console.log('- 24K: 9.754g (not 113.195g)');
  console.log('- 22K: 110.260g (not 103.687g)');
  console.log('- Procured: 113.195g (unchanged)');
  console.log('- Weight: 120.420g (unchanged)');
}

checkApiVsDb();
