import { NextRequest, NextResponse } from "next/server"
import { SalesModel, CreateSalesTransactionData } from "@/lib/models/sales"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const inventoryId = searchParams.get("inventory_id")
    const startDate = searchParams.get("start_date")
    const endDate = searchParams.get("end_date")
    const summary = searchParams.get("summary")

    if (summary === "true") {
      const summaryData = await SalesModel.getSalesSummary(startDate || undefined, endDate || undefined)
      return NextResponse.json(summaryData)
    }

    if (inventoryId) {
      const sales = await SalesModel.getSalesForInventory(parseInt(inventoryId))
      return NextResponse.json(sales)
    }

    // Get all sales transactions (you might want to add pagination here)
    const sales = await SalesModel.getSalesForInventory(0) // This would need to be modified to get all
    return NextResponse.json(sales)

  } catch (error) {
    console.error("Error fetching sales:", error)
    return NextResponse.json(
      { error: "Failed to fetch sales data" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data: CreateSalesTransactionData = await request.json()

    // Validate required fields
    if (!data.inventory_id) {
      return NextResponse.json(
        { error: "Inventory ID is required" },
        { status: 400 }
      )
    }

    const salesId = await SalesModel.createSalesTransaction(data)

    return NextResponse.json(
      { 
        message: "Sales transaction created successfully", 
        id: salesId 
      },
      { status: 201 }
    )

  } catch (error) {
    console.error("Error creating sales transaction:", error)
    return NextResponse.json(
      { error: "Failed to create sales transaction" },
      { status: 500 }
    )
  }
}
