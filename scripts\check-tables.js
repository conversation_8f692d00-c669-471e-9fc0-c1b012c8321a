#!/usr/bin/env node

require("dotenv").config({ path: ".env.local" })

const mysql = require("mysql2/promise")

async function checkTables() {
  console.log("🔍 Checking database tables...\n")

  const config = {
    host: process.env.DB_HOST || "localhost",
    port: Number.parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME,
  }

  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(config)

    // Check tables
    const [tables] = await connection.execute("SHOW TABLES")
    
    if (tables.length === 0) {
      console.log("⚠️  No tables found in database")
    } else {
      console.log(`✅ Found ${tables.length} tables:`)
      for (const table of tables) {
        const tableName = Object.values(table)[0]
        console.log(`   - ${tableName}`)
        
        // Check row count
        const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`)
        console.log(`     Rows: ${rows[0].count}`)
      }
    }

    await connection.end()
    
  } catch (error) {
    console.log("\n❌ Database check failed:")
    console.log(`   Error: ${error.message}`)
    process.exit(1)
  }
}

checkTables()
