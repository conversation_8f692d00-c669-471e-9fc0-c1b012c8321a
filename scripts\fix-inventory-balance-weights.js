const mysql = require('mysql2/promise');

async function fixInventoryBalanceWeights() {
  console.log('🔧 FIXING INVENTORY BALANCE WEIGHTS');
  console.log('===================================\n');

  let connection;
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'jewellery_wholesale'
    });

    console.log('✅ Connected to database');

    // Get the current Chain item
    const [currentData] = await connection.execute(`
      SELECT * FROM inventory 
      WHERE product_name = 'Chain' OR product_name = 'Fancy Chain'
      ORDER BY id DESC LIMIT 1
    `);

    if (currentData.length === 0) {
      console.log('❌ No Chain item found in database');
      return;
    }

    const item = currentData[0];
    console.log('\n📊 CURRENT VALUES:');
    console.log('==================');
    console.log(`ID: ${item.id}`);
    console.log(`Product: ${item.product_name}`);
    console.log(`Procured 24K: ${item.procured_in_24k}`);
    console.log(`Balance 24K: ${item.balance_weight_24k} ← WRONG (should be 9.754)`);
    console.log(`Balance 22K: ${item.balance_weight_22k} ← WRONG (should be 110.260)`);

    console.log('\n🎯 UPDATING TO CORRECT VALUES:');
    console.log('==============================');
    console.log('Based on your actual data:');
    console.log('- Balance Weight 24K: 9.754 (from Gold Weight in 24k column)');
    console.log('- Balance Weight 22K: 110.260 (from final Gold Weight in 22k column)');

    // Update with correct balance weights
    await connection.execute(`
      UPDATE inventory 
      SET 
        balance_weight_24k = 9.754,
        balance_weight_22k = 110.260,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [item.id]);

    console.log('\n✅ UPDATED SUCCESSFULLY!');

    // Verify the update
    const [updatedData] = await connection.execute(`
      SELECT * FROM inventory WHERE id = ?
    `, [item.id]);

    const updatedItem = updatedData[0];
    console.log('\n📊 UPDATED VALUES:');
    console.log('==================');
    console.log(`Balance 24K: ${updatedItem.balance_weight_24k} ✅`);
    console.log(`Balance 22K: ${updatedItem.balance_weight_22k} ✅`);
    console.log(`Procured 24K: ${updatedItem.procured_in_24k} (unchanged)`);

    console.log('\n🎯 EXPECTED APP DISPLAY:');
    console.log('========================');
    console.log('After refresh, the app should show:');
    console.log('- 24K: 9.754g ✅');
    console.log('- 22K: 110.260g ✅');
    console.log('- Procured: 113.195g ✅');
    console.log('- Weight: 120.420g ✅');

    console.log('\n📝 NEXT STEPS:');
    console.log('==============');
    console.log('1. Refresh the browser page');
    console.log('2. Check the inventory table');
    console.log('3. Verify the weights now match your actual data');
    console.log('4. For future items, fill balance weight fields manually');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the fix
fixInventoryBalanceWeights();
