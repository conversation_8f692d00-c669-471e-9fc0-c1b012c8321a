/**
 * Client-Side Business Logic Utilities
 * Uses API calls instead of direct database access
 * Safe for client-side components and Next.js build
 */

export interface BusinessCalculations {
  // Conversion calculations
  convertPurity(weight: number, fromPurity: string, toPurity: string): Promise<number>
  
  // Wastage calculations
  calculateWastage(weight: number, formType: string): Promise<number>
  calculateExpectedYield(procuredWeight: number, formType: string, processingLoss?: number): Promise<number>
  
  // Pricing calculations
  calculateGoldValue(weight: number, purity: string, goldRates: any): number
  calculateMakingCharges(weight: number): Promise<number>
  calculateGST(amount: number): Promise<number>
  calculateTotalAmount(goldValue: number, makingCharges: number, stoneValue: number): Promise<number>
  
  // Weight calculations
  calculateNetWeight(grossWeight: number, stoneWeight: number): number
  calculate24KEquivalent(weight: number, tunchPercentage: number): number
  
  // Validation utilities
  validateWeight(weight: number): boolean
  validateTunch(tunch: number): boolean
  validateRate(rate: number): boolean
}

export class ClientBusinessLogic implements BusinessCalculations {
  
  // =====================================================
  // CONVERSION CALCULATIONS (CLIENT-SAFE)
  // =====================================================
  
  static async convertPurity(
    weight: number, 
    fromPurity: string, 
    toPurity: string
  ): Promise<number> {
    if (weight <= 0) return 0
    if (fromPurity === toPurity) return weight
    
    try {
      // Get conversion factor from settings API
      const response = await fetch(`/api/settings?category=conversion&key=conversion_${fromPurity.toLowerCase()}_to_${toPurity.toLowerCase()}`)
      const data = await response.json()
      
      if (data.success && data.data.value) {
        return weight * data.data.value
      }
      
      // Fallback to default conversion factors
      const defaultFactors: { [key: string]: number } = {
        '24k_to_22k': 0.916,
        '24k_to_18k': 0.750,
        '22k_to_24k': 1.092,
        '18k_to_24k': 1.333
      }
      const key = `${fromPurity.toLowerCase()}_to_${toPurity.toLowerCase()}`
      return weight * (defaultFactors[key] || 1.0)
    } catch (error) {
      console.error('Error in purity conversion:', error)
      // Use default factors as fallback
      const defaultFactors: { [key: string]: number } = {
        '24k_to_22k': 0.916,
        '24k_to_18k': 0.750,
        '22k_to_24k': 1.092,
        '18k_to_24k': 1.333
      }
      const key = `${fromPurity.toLowerCase()}_to_${toPurity.toLowerCase()}`
      return weight * (defaultFactors[key] || 1.0)
    }
  }
  
  // =====================================================
  // WASTAGE CALCULATIONS (CLIENT-SAFE)
  // =====================================================
  
  static async calculateWastage(weight: number, formType: string): Promise<number> {
    if (weight <= 0) return 0
    
    try {
      // Get wastage rate from settings API
      const key = `wastage_rate_${formType.toLowerCase().replace(' ', '_')}`
      const response = await fetch(`/api/settings?category=wastage&key=${key}`)
      const data = await response.json()
      
      if (data.success && typeof data.data.value === 'number') {
        return (weight * data.data.value) / 100
      }
      
      // Fallback to default rates
      const defaultRates: { [key: string]: number } = {
        'Bar': 0.5,
        'Jewel': 2.0,
        'Old Jewel': 3.0
      }
      const rate = defaultRates[formType] || 2.0
      return (weight * rate) / 100
    } catch (error) {
      console.error('Error calculating wastage:', error)
      // Fallback to default rates
      const defaultRates: { [key: string]: number } = {
        'Bar': 0.5,
        'Jewel': 2.0,
        'Old Jewel': 3.0
      }
      const rate = defaultRates[formType] || 2.0
      return (weight * rate) / 100
    }
  }
  
  static async calculateExpectedYield(
    procuredWeight: number, 
    formType: string, 
    processingLoss: number = 0
  ): Promise<number> {
    if (procuredWeight <= 0) return 0
    
    const wastage = await this.calculateWastage(procuredWeight, formType)
    return Math.max(0, procuredWeight - wastage - processingLoss)
  }
  
  // =====================================================
  // PRICING CALCULATIONS (CLIENT-SAFE)
  // =====================================================
  
  static calculateGoldValue(weight: number, purity: string, goldRates: any): number {
    if (weight <= 0) return 0
    
    const rate = goldRates[`rate_${purity.toLowerCase()}`] || goldRates.rate_24k || 0
    return weight * rate
  }
  
  static async calculateMakingCharges(weight: number): Promise<number> {
    if (weight <= 0) return 0
    
    try {
      const response = await fetch('/api/settings?category=pricing&key=default_making_charges')
      const data = await response.json()
      
      if (data.success && typeof data.data.value === 'number') {
        return weight * data.data.value
      }
      
      return weight * 500 // Default fallback
    } catch (error) {
      console.error('Error calculating making charges:', error)
      return weight * 500 // Default fallback
    }
  }
  
  static async calculateGST(amount: number): Promise<number> {
    if (amount <= 0) return 0
    
    try {
      // Check if GST should be applied
      const applyGSTResponse = await fetch('/api/settings?category=pricing&key=apply_gst')
      const applyGSTData = await applyGSTResponse.json()
      
      if (!applyGSTData.success || !applyGSTData.data.value) {
        return 0
      }
      
      // Get GST rate
      const gstRateResponse = await fetch('/api/settings?category=pricing&key=gst_rate')
      const gstRateData = await gstRateResponse.json()
      
      if (gstRateData.success && typeof gstRateData.data.value === 'number') {
        return (amount * gstRateData.data.value) / 100
      }
      
      return (amount * 3) / 100 // Default 3% GST
    } catch (error) {
      console.error('Error calculating GST:', error)
      return (amount * 3) / 100 // Default 3% GST
    }
  }
  
  static async calculateTotalAmount(
    goldValue: number, 
    makingCharges: number, 
    stoneValue: number = 0
  ): Promise<number> {
    const subtotal = goldValue + makingCharges + stoneValue
    const gst = await this.calculateGST(subtotal)
    return subtotal + gst
  }
  
  // =====================================================
  // WEIGHT CALCULATIONS (CLIENT-SAFE - NO API NEEDED)
  // =====================================================
  
  static calculateNetWeight(grossWeight: number, stoneWeight: number = 0): number {
    return Math.max(0, grossWeight - stoneWeight)
  }
  
  static calculate24KEquivalent(weight: number, tunchPercentage: number): number {
    if (weight <= 0 || tunchPercentage <= 0) return 0
    return (weight * tunchPercentage) / 100
  }

  // =====================================================
  // JEWELLERY WHOLESALE SPECIFIC CALCULATIONS (CLIENT-SAFE)
  // =====================================================

  /**
   * Calculate 24K weight from 22K weight using tunch percentage
   * Based on CSV sample: 10.160g (22k) with 96% tunch = 9.754g (24k)
   */
  static calculate24KFromTunch(weight22k: number, tunchPercentage: number): number {
    if (weight22k <= 0 || tunchPercentage <= 0) return 0
    return Number(((weight22k * tunchPercentage) / 100).toFixed(3))
  }

  /**
   * Calculate 22K weight from 24K weight using tunch percentage
   * Reverse calculation for inventory management
   */
  static calculate22KFromTunch(weight24k: number, tunchPercentage: number): number {
    if (weight24k <= 0 || tunchPercentage <= 0) return 0
    return Number(((weight24k * 100) / tunchPercentage).toFixed(3))
  }

  /**
   * Calculate total bill amount using CSV sample logic
   * Formula: (24K weight × 24K price per gram) + stone amount = total
   */
  static calculateBillTotal(
    weight24k: number, 
    goldRate24k: number, 
    stoneAmount: number = 0
  ): number {
    if (weight24k <= 0 || goldRate24k <= 0) return stoneAmount
    const goldValue = weight24k * goldRate24k
    return Number((goldValue + stoneAmount).toFixed(2))
  }

  /**
   * Calculate balance stock after sale
   * Formula: Procured - Sold = Balance
   */
  static calculateStockBalance(
    procuredWeight: number, 
    soldWeight: number
  ): number {
    const balance = procuredWeight - soldWeight
    return Math.max(0, Number(balance.toFixed(3)))
  }

  /**
   * Validate tunch percentage based on typical jewellery ranges
   * Typical ranges: 85% - 101% for different purities
   */
  static validateTunchPercentage(tunch: number): boolean {
    return typeof tunch === 'number' && tunch >= 85 && tunch <= 101
  }

  /**
   * Get suggested tunch percentage based on product type
   * Based on CSV sample data patterns
   */
  static getSuggestedTunchPercentage(productType: string, hasStone: boolean): number {
    const baseRates: { [key: string]: number } = {
      'Chain': hasStone ? 93 : 96,
      'Bangles': hasStone ? 94 : 98,
      'Studs': hasStone ? 95 : 99,
      'Ring': hasStone ? 94 : 97,
      'Necklace': hasStone ? 93 : 96,
      'Earrings': hasStone ? 95 : 98,
      'Pendant': hasStone ? 94 : 97
    }
    
    return baseRates[productType] || (hasStone ? 94 : 97)
  }
  
  // =====================================================
  // VALIDATION UTILITIES (CLIENT-SAFE - NO API NEEDED)
  // =====================================================
  
  static validateWeight(weight: number): boolean {
    return typeof weight === 'number' && weight > 0 && weight < 100000 // Max 100kg
  }
  
  static validateTunch(tunch: number): boolean {
    return typeof tunch === 'number' && tunch > 0 && tunch <= 100
  }
  
  static validateRate(rate: number): boolean {
    return typeof rate === 'number' && rate > 0 && rate < 1000000 // Max ₹10L per gram
  }
  
  // =====================================================
  // ROUNDING UTILITIES (CLIENT-SAFE)
  // =====================================================
  
  static async roundToSettingsPrecision(value: number): Promise<number> {
    try {
      const response = await fetch('/api/settings?category=calculation&key=rounding_precision')
      const data = await response.json()
      
      if (data.success && typeof data.data.value === 'number') {
        return Number(value.toFixed(data.data.value))
      }
      
      return Number(value.toFixed(3)) // Default 3 decimal places
    } catch (error) {
      return Number(value.toFixed(3)) // Default 3 decimal places
    }
  }
  
  // =====================================================
  // STOCK MANAGEMENT (CLIENT-SAFE)
  // =====================================================
  
  static async isLowStock(currentStock: number): Promise<boolean> {
    try {
      const response = await fetch('/api/settings?category=business&key=low_stock_threshold')
      const data = await response.json()
      
      if (data.success && typeof data.data.value === 'number') {
        return currentStock <= data.data.value
      }
      
      return currentStock <= 10 // Default 10g threshold
    } catch (error) {
      return currentStock <= 10 // Default 10g threshold
    }
  }
  
  static async shouldTriggerWastageAlert(wastagePercentage: number): Promise<boolean> {
    try {
      const response = await fetch('/api/settings?category=alerts&key=high_wastage_threshold')
      const data = await response.json()
      
      if (data.success && typeof data.data.value === 'number') {
        return wastagePercentage >= data.data.value
      }
      
      return wastagePercentage >= 5 // Default 5% threshold
    } catch (error) {
      return wastagePercentage >= 5 // Default 5% threshold
    }
  }
  
  // =====================================================
  // HELPER METHODS FOR SETTINGS
  // =====================================================
  
  static async getWastageRate(formType: string): Promise<number> {
    const key = `wastage_rate_${formType.toLowerCase().replace(' ', '_')}`
    
    try {
      const response = await fetch(`/api/settings?category=wastage&key=${key}`)
      const data = await response.json()
      
      if (data.success && typeof data.data.value === 'number') {
        return data.data.value
      }
    } catch (error) {
      console.error('Error getting wastage rate:', error)
    }
    
    // Fallback to default rates
    const defaultRates: { [key: string]: number } = {
      'wastage_rate_bar': 0.5,
      'wastage_rate_jewel': 2.0,
      'wastage_rate_old_jewel': 3.0
    }
    
    return defaultRates[key] || 2.0
  }
  
  static async getConversionFactor(fromPurity: string, toPurity: string): Promise<number> {
    const key = `conversion_${fromPurity.toLowerCase()}_to_${toPurity.toLowerCase()}`
    
    try {
      const response = await fetch(`/api/settings?category=conversion&key=${key}`)
      const data = await response.json()
      
      if (data.success && typeof data.data.value === 'number') {
        return data.data.value
      }
    } catch (error) {
      console.error('Error getting conversion factor:', error)
    }
    
    // Fallback to default conversion factors
    const defaultFactors: { [key: string]: number } = {
      'conversion_24k_to_22k': 0.916,
      'conversion_24k_to_18k': 0.750,
      'conversion_22k_to_24k': 1.092,
      'conversion_18k_to_24k': 1.333
    }
    
    return defaultFactors[key] || 1.0
  }
}

// Export both class and individual functions for flexibility
export const {
  convertPurity,
  calculateWastage,
  calculateExpectedYield,
  calculateGoldValue,
  calculateMakingCharges,
  calculateGST,
  calculateTotalAmount,
  calculateNetWeight,
  calculate24KEquivalent,
  calculate24KFromTunch,
  calculate22KFromTunch,
  calculateBillTotal,
  calculateStockBalance,
  validateTunchPercentage,
  getSuggestedTunchPercentage,
  validateWeight,
  validateTunch,
  validateRate,
  roundToSettingsPrecision,
  isLowStock,
  shouldTriggerWastageAlert,
  getWastageRate,
  getConversionFactor
} = ClientBusinessLogic
