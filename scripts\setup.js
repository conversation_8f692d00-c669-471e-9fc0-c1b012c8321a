#!/usr/bin/env node

const fs = require("fs")
const path = require("path")
const { execSync } = require("child_process")

console.log("🚀 Setting up Jewellery Wholesale Software...\n")

// Check if .env.local exists
const envPath = path.join(process.cwd(), ".env.local")
const envExamplePath = path.join(process.cwd(), ".env.example")

if (!fs.existsSync(envPath)) {
  if (fs.existsSync(envExamplePath)) {
    console.log("📋 Copying .env.example to .env.local...")
    fs.copyFileSync(envExamplePath, envPath)
    console.log("✅ Environment file created")
  } else {
    console.log("❌ .env.example not found")
    process.exit(1)
  }
} else {
  console.log("✅ Environment file already exists")
}

// Create required directories
const directories = ["uploads", "backups", "logs"]

directories.forEach((dir) => {
  const dirPath = path.join(process.cwd(), dir)
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
    console.log(`📁 Created directory: ${dir}`)
  } else {
    console.log(`✅ Directory already exists: ${dir}`)
  }
})

// Check Node.js version
const nodeVersion = process.version
const majorVersion = Number.parseInt(nodeVersion.slice(1).split(".")[0])

if (majorVersion < 18) {
  console.log(`❌ Node.js version ${nodeVersion} is not supported. Please use Node.js 18 or higher.`)
  process.exit(1)
} else {
  console.log(`✅ Node.js version ${nodeVersion} is supported`)
}

// Install dependencies if node_modules doesn't exist
const nodeModulesPath = path.join(process.cwd(), "node_modules")
if (!fs.existsSync(nodeModulesPath)) {
  console.log("📦 Installing dependencies...")
  try {
    execSync("npm install", { stdio: "inherit" })
    console.log("✅ Dependencies installed successfully")
  } catch (error) {
    console.log("❌ Failed to install dependencies")
    console.log("Please run: npm install")
    process.exit(1)
  }
} else {
  console.log("✅ Dependencies already installed")
}

// Run environment check
console.log("\n🔍 Checking environment configuration...")
try {
  execSync("node scripts/check-env.js", { stdio: "inherit" })
} catch (error) {
  console.log("⚠️  Environment check completed with warnings")
}

// Test database connection
console.log("\n🗄️  Testing database connection...")
try {
  execSync("node scripts/test-db.js", { stdio: "inherit" })
} catch (error) {
  console.log("⚠️  Database connection test failed")
  console.log("Please check your database configuration in .env.local")
}

console.log("\n🎉 Setup completed!")
console.log("\nNext steps:")
console.log("1. Update .env.local with your database credentials")
console.log("2. Run: npm run db:create (to create database schema)")
console.log("3. Run: npm run db:seed (to add sample data)")
console.log("4. Run: npm run dev (to start development server)")
console.log("\nFor help, see README-SETUP.md")
