<!DOCTYPE html>
<html>
<head>
    <title>Debug Business Settings</title>
</head>
<body>
    <h1>Business Settings Debug</h1>
    <button onclick="checkSettings()">Check Current Settings</button>
    <button onclick="clearSettings()">Clear Settings</button>
    <button onclick="setTestSettings()">Set Test Settings (Jewel: 0%)</button>
    
    <div id="output"></div>

    <script>
        function checkSettings() {
            const settings = localStorage.getItem('business_settings');
            const output = document.getElementById('output');
            
            if (settings) {
                const parsed = JSON.parse(settings);
                output.innerHTML = `
                    <h3>Current Settings:</h3>
                    <pre>${JSON.stringify(parsed, null, 2)}</pre>
                    <p><strong>Jewel Wastage Rate:</strong> ${parsed.wastage_rate_jewel}%</p>
                `;
            } else {
                output.innerHTML = '<p>No settings found in localStorage</p>';
            }
        }
        
        function clearSettings() {
            localStorage.removeItem('business_settings');
            document.getElementById('output').innerHTML = '<p>Settings cleared</p>';
        }
        
        function setTestSettings() {
            const testSettings = {
                wastage_rate_bar: 0,
                wastage_rate_jewel: 0,
                wastage_rate_old_jewel: 0
            };
            localStorage.setItem('business_settings', JSON.stringify(testSettings));
            document.getElementById('output').innerHTML = '<p>Test settings saved (all wastage rates set to 0%)</p>';
        }
        
        // Auto-check on load
        window.onload = checkSettings;
    </script>
</body>
</html>
