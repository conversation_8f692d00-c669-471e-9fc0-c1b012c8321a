// Type definitions for the jewellery wholesale software

export interface Supplier {
  id: number
  name: string
  location: string
  contact_person: string
  phone: string
  email: string
  address: string
  speciality: string
  total_purchases: number
  last_purchase_date: string
  status: 'Active' | 'Inactive'
  created_at: string
  updated_at: string
}

export interface Customer {
  id: number
  name: string
  location: string
  contact_person: string
  phone: string
  email: string
  address: string
  total_purchases: number
  last_purchase_date: string
  status: 'Active' | 'Inactive'
  created_at: string
  updated_at: string
}

export interface InventoryItem {
  id: number
  supplier_id: number
  product_name: string
  product_type: string
  with_stone_weight: number
  without_stone_weight: number
  with_stone_cost: number
  without_stone_cost: number
  procured_in_24k: number
  sold_value_with_stone: number
  sold_value_without_stone: number
  balance_weight_24k: number
  balance_weight_22k: number
  status: 'Available' | 'Sold' | 'Reserved'
  created_at: string
  updated_at: string
  // Additional fields for joined queries
  supplier_name?: string
  supplier_location?: string
  supplier_contact_person?: string
}

export interface Bill {
  id: number
  customer_id: number
  bill_number: string
  product_name: string
  product_type: string
  with_stone: number
  without_stone: number
  gross_weight: number
  stone_weight: number
  net_weight: number
  tunch_with_stone: number
  tunch_without_stone: number
  weight_in_24k: number
  gold_24k_price: number
  stone_price: number
  making_charges: number
  total_amount: number
  status: 'Pending' | 'Paid' | 'Cancelled'
  bill_date: string
  created_at: string
  updated_at: string
  // Additional fields for joined queries
  customer_name?: string
  customer_location?: string
}

export interface GoldRate {
  id: number
  rate_24k: number
  rate_22k: number
  rate_date: string
  created_at: string
}

export interface User {
  id: number
  username: string
  password: string
  email: string
  full_name: string
  role: 'admin' | 'manager' | 'user'
  permissions: Record<string, boolean>
  status: 'Active' | 'Inactive'
  last_login: string | null
  created_at: string
  updated_at: string
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Dashboard types
export interface DashboardStats {
  totalSales: number
  totalCustomers: number
  totalSuppliers: number
  totalInventoryValue: number
  recentBills: Bill[]
  lowStockItems: InventoryItem[]
  currentGoldRate: GoldRate
}

// Report types
export interface SalesReport {
  date: string
  total_sales: number
  bill_count: number
  average_bill_value: number
}

export interface InventoryReport {
  product_type: string
  total_weight: number
  total_value: number
  item_count: number
}

export interface CustomerReport {
  customer_name: string
  total_purchases: number
  bill_count: number
  last_purchase_date: string
}

export interface SupplierReport {
  supplier_name: string
  total_purchases: number
  item_count: number
  last_purchase_date: string
}

// Form types
export interface BillingFormData {
  customer_id: number
  product_name: string
  product_type: string
  with_stone: number
  without_stone: number
  gross_weight: number
  stone_weight: number
  net_weight: number
  tunch_with_stone: number
  tunch_without_stone: number
  weight_in_24k: number
  gold_24k_price: number
  stone_price: number
  making_charges: number
  total_amount: number
}

export interface InventoryFormData {
  supplier_id: number
  product_name: string
  product_type: string
  with_stone_weight: number
  without_stone_weight: number
  with_stone_cost: number
  without_stone_cost: number
  procured_in_24k: number
}

// Search and filter types
export interface SearchFilters {
  search?: string
  status?: string
  date_from?: string
  date_to?: string
  supplier_id?: number
  customer_id?: number
  product_type?: string
}

// Pagination types
export interface PaginationParams {
  page: number
  limit: number
  total: number
  totalPages: number
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: PaginationParams
}

// Configuration types
export interface AppConfig {
  company: {
    name: string
    address: string
    phone: string
    email: string
  }
  business: {
    currency: string
    tax_rate: number
    making_charges: number
  }
  database: {
    host: string
    port: number
    user: string
    password: string
    database: string
    ssl: boolean
    sslCa?: string
  }
  email?: {
    host: string
    port: number
    user: string
    password: string
    from: string
  }
  debug: boolean
}
