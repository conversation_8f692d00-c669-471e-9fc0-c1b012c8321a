import mysql from "mysql2/promise"
import { config } from "./config"

// Database connection pool
let pool: mysql.Pool | null = null

export function createPool(): mysql.Pool {
  if (!pool) {
    pool = mysql.createPool({
      host: config.database.host,
      port: config.database.port,
      user: config.database.user,
      password: config.database.password,
      database: config.database.database,
      waitForConnections: true,
      connectionLimit: 20, // Increased from 10 to 20
      queueLimit: 0,
      acquireTimeout: 60000,
      timeout: 60000,
      idleTimeout: 300000, // 5 minutes
      maxIdle: 10, // Maximum idle connections
      enableKeepAlive: true,
      keepAliveInitialDelay: 0,
      ssl: config.database.ssl
        ? {
            ca: config.database.sslCa ? require("fs").readFileSync(config.database.sslCa) : undefined,
            rejectUnauthorized: false,
          }
        : false,
    })

    // Handle pool errors
    pool.on("connection", (connection) => {
      if (config.debug) {
        console.log("New database connection established as id " + connection.threadId)
      }
    })

    pool.on("error", (err) => {
      console.error("Database pool error:", err)
      if (err.code === "PROTOCOL_CONNECTION_LOST") {
        createPool()
      } else {
        console.error("Unhandled pool error:", err)
      }
    })

    pool.on("acquire", (connection) => {
      if (config.debug) {
        console.log("Connection %d acquired", connection.threadId)
      }
    })

    pool.on("release", (connection) => {
      if (config.debug) {
        console.log("Connection %d released", connection.threadId)
      }
    })
  }

  return pool
}

export function getPool(): mysql.Pool {
  if (!pool) {
    return createPool()
  }
  return pool
}

// Get pool status for debugging
export function getPoolStatus() {
  if (!pool) return null
  return {
    totalConnections: (pool as any)._allConnections?.length || 0,
    freeConnections: (pool as any)._freeConnections?.length || 0,
    acquiringConnections: (pool as any)._acquiringConnections?.length || 0,
  }
}

// Database connection helper
export async function getConnection(): Promise<mysql.PoolConnection> {
  const pool = getPool()
  try {
    const connection = await pool.getConnection()
    return connection
  } catch (error) {
    console.error("Failed to get database connection:", error)
    if (config.debug) {
      console.log("Pool status:", getPoolStatus())
    }
    throw error
  }
}

// Execute query with connection management
export async function executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {
  const connection = await getConnection()

  try {
    if (config.debug) {
      console.log("Executing query:", query)
      console.log("With params:", params)
    }

    const [rows] = await connection.execute(query, params)
    return rows as T[]
  } catch (error) {
    console.error("Database query error:", error)
    console.error("Query:", query)
    console.error("Params:", params)
    throw error
  } finally {
    connection.release()
  }
}

// Execute query and return single result
export async function executeSingle<T = any>(query: string, params: any[] = []): Promise<T | null> {
  const results = await executeQuery<T>(query, params)
  return results.length > 0 ? results[0] : null
}

// Execute INSERT/UPDATE/DELETE query and return result metadata
export async function executeUpdate(query: string, params: any[] = []): Promise<{ insertId?: number; affectedRows: number }> {
  const connection = await getConnection()

  try {
    if (config.debug) {
      console.log("Executing update query:", query)
      console.log("With params:", params)
    }

    const [result] = await connection.execute(query, params)
    const metadata = result as any
    return {
      insertId: metadata.insertId,
      affectedRows: metadata.affectedRows
    }
  } catch (error) {
    console.error("Database update query error:", error)
    console.error("Query:", query)
    console.error("Params:", params)
    throw error
  } finally {
    connection.release()
  }
}

// Execute transaction
export async function executeTransaction<T>(callback: (connection: mysql.PoolConnection) => Promise<T>): Promise<T> {
  const connection = await getConnection()

  try {
    await connection.beginTransaction()

    const result = await callback(connection)

    await connection.commit()
    return result
  } catch (error) {
    await connection.rollback()
    console.error("Transaction error:", error)
    throw error
  } finally {
    connection.release()
  }
}

// Test database connection
export async function testConnection(): Promise<{ success: boolean; error?: string }> {
  try {
    const connection = await getConnection()
    await connection.ping()
    connection.release()

    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    }
  }
}

// Close all connections
export async function closePool(): Promise<void> {
  if (pool) {
    await pool.end()
    pool = null
  }
}



// Database utilities
export class DatabaseError extends Error {
  constructor(
    message: string,
    public originalError?: Error,
  ) {
    super(message)
    this.name = "DatabaseError"
  }
}

export function handleDatabaseError(error: any): never {
  if (error.code === "ER_DUP_ENTRY") {
    throw new DatabaseError("Duplicate entry found")
  } else if (error.code === "ER_NO_REFERENCED_ROW_2") {
    throw new DatabaseError("Referenced record not found")
  } else if (error.code === "ER_ROW_IS_REFERENCED_2") {
    throw new DatabaseError("Cannot delete record - it is referenced by other records")
  } else {
    throw new DatabaseError("Database operation failed", error)
  }
}

// Query builders
export function buildSelectQuery(
  table: string,
  columns: string[] = ["*"],
  where?: Record<string, any>,
  orderBy?: string,
  limit?: number,
): { query: string; params: any[] } {
  let query = `SELECT ${columns.join(", ")} FROM ${table}`
  const params: any[] = []

  if (where && Object.keys(where).length > 0) {
    const conditions = Object.keys(where).map((key) => `${key} = ?`)
    query += ` WHERE ${conditions.join(" AND ")}`
    params.push(...Object.values(where))
  }

  if (orderBy) {
    query += ` ORDER BY ${orderBy}`
  }

  if (limit) {
    query += ` LIMIT ${limit}`
  }

  return { query, params }
}

export function buildInsertQuery(table: string, data: Record<string, any>): { query: string; params: any[] } {
  const columns = Object.keys(data)
  const placeholders = columns.map(() => "?").join(", ")
  const query = `INSERT INTO ${table} (${columns.join(", ")}) VALUES (${placeholders})`
  const params = Object.values(data)

  return { query, params }
}

export function buildUpdateQuery(
  table: string,
  data: Record<string, any>,
  where: Record<string, any>,
): { query: string; params: any[] } {
  const setClause = Object.keys(data)
    .map((key) => `${key} = ?`)
    .join(", ")
  const whereClause = Object.keys(where)
    .map((key) => `${key} = ?`)
    .join(" AND ")

  const query = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`
  const params = [...Object.values(data), ...Object.values(where)]

  return { query, params }
}

export function buildDeleteQuery(table: string, where: Record<string, any>): { query: string; params: any[] } {
  const whereClause = Object.keys(where)
    .map((key) => `${key} = ?`)
    .join(" AND ")
  const query = `DELETE FROM ${table} WHERE ${whereClause}`
  const params = Object.values(where)

  return { query, params }
}
