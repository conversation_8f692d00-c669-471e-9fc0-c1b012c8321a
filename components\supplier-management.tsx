"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Plus, Search, Phone, Mail, MapPin, Edit, Trash2 } from "lucide-react"
import { useDatabase } from "@/hooks/use-database"
import { useDatabaseMutation } from "@/hooks/use-database-mutation"
import EditSupplierDialog from "@/components/edit-supplier-dialog"

interface Supplier {
  id: number
  name: string
  location: string
  contact_person: string
  phone: string
  email: string
  address: string
  speciality: string
  total_purchases: number
  last_purchase_date: string
  status: "Active" | "Inactive"
}

export default function SupplierManagement() {
  const [initialSuppliers, setInitialSuppliers] = useState<Supplier[]>([
    {
      id: 1,
      name: "Emerald Jewel Industry",
      location: "Coimbatore",
      contact_person: "Rajesh Kumar",
      phone: "+91 98765 43210",
      email: "<EMAIL>",
      address: "123, Jewelry Street, Coimbatore, Tamil Nadu",
      speciality: "Gold Chains, Necklaces",
      total_purchases: 2500000,
      last_purchase_date: "2024-01-15",
      status: "Active",
    },
    {
      id: 2,
      name: "Nala Gold",
      location: "Mumbai",
      contact_person: "Priya Sharma",
      phone: "+91 87654 32109",
      email: "<EMAIL>",
      address: "456, Gold Market, Mumbai, Maharashtra",
      speciality: "Gold Bangles, Rings",
      total_purchases: 3200000,
      last_purchase_date: "2024-01-16",
      status: "Active",
    },
    {
      id: 3,
      name: "SSJ",
      location: "Surat",
      contact_person: "Amit Patel",
      phone: "+91 76543 21098",
      email: "<EMAIL>",
      address: "789, Diamond Plaza, Surat, Gujarat",
      speciality: "Diamond Jewelry, Studs",
      total_purchases: 1800000,
      last_purchase_date: "2024-01-17",
      status: "Active",
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newSupplier, setNewSupplier] = useState<Partial<Supplier>>({})
  const [editingSupplier, setEditingSupplier] = useState<Supplier | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  const { data: fetchedSuppliers, loading, error, refetch } = useDatabase<Supplier>("suppliers", searchTerm)
  const { mutate, loading: mutating } = useDatabaseMutation<Supplier>()

  const suppliers = fetchedSuppliers || initialSuppliers

  const filteredSuppliers = suppliers?.filter(
    (supplier) =>
      supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.speciality.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleAddSupplier = async () => {
    if (newSupplier.name && newSupplier.contact_person) {
      const result = await mutate("suppliers", "POST", newSupplier)
      if (result) {
        setNewSupplier({})
        setIsAddDialogOpen(false)
        refetch()
      }
    }
  }

  const handleEditSupplier = (supplier: Supplier) => {
    setEditingSupplier(supplier)
    setIsEditDialogOpen(true)
  }

  const handleDeleteSupplier = async (id: number) => {
    if (confirm("Are you sure you want to delete this supplier?")) {
      const result = await mutate(`suppliers/${id}`, "DELETE")
      if (result !== null) {
        refetch()
      }
    }
  }

  const totalPurchases = suppliers?.reduce((sum, supplier) => sum + (Number(supplier.total_purchases) || 0), 0) || 0

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Suppliers</CardTitle>
            <Badge variant="secondary">{suppliers?.length}</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{suppliers?.filter((s) => s.status === "Active").length}</div>
            <p className="text-xs text-muted-foreground">Active suppliers</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Purchases</CardTitle>
            <Badge variant="secondary">₹</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{(totalPurchases / 100000).toFixed(1)}L</div>
            <p className="text-xs text-muted-foreground">All time purchases</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Supplier</CardTitle>
            <Badge variant="secondary">Best</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Nala Gold</div>
            <p className="text-xs text-muted-foreground">Highest purchase value</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Supplier Management</CardTitle>
              <CardDescription>Manage your jewelry suppliers and their details</CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Supplier
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Add New Supplier</DialogTitle>
                  <DialogDescription>Enter the details for the new supplier</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto px-1">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="supplierName">Supplier Name</Label>
                      <Input
                        id="supplierName"
                        value={newSupplier.name || ""}
                        onChange={(e) => setNewSupplier({ ...newSupplier, name: e.target.value })}
                        placeholder="Enter supplier name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        value={newSupplier.location || ""}
                        onChange={(e) => setNewSupplier({ ...newSupplier, location: e.target.value })}
                        placeholder="Enter location"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="contactPerson">Contact Person</Label>
                      <Input
                        id="contactPerson"
                        value={newSupplier.contact_person || ""}
                        onChange={(e) => setNewSupplier({ ...newSupplier, contact_person: e.target.value })}
                        placeholder="Enter contact person name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={newSupplier.phone || ""}
                        onChange={(e) => setNewSupplier({ ...newSupplier, phone: e.target.value })}
                        placeholder="Enter phone number"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newSupplier.email || ""}
                      onChange={(e) => setNewSupplier({ ...newSupplier, email: e.target.value })}
                      placeholder="Enter email address"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      value={newSupplier.address || ""}
                      onChange={(e) => setNewSupplier({ ...newSupplier, address: e.target.value })}
                      placeholder="Enter full address"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="speciality">Speciality</Label>
                    <Input
                      id="speciality"
                      value={newSupplier.speciality || ""}
                      onChange={(e) => setNewSupplier({ ...newSupplier, speciality: e.target.value })}
                      placeholder="Enter speciality (e.g., Gold Chains, Diamond Jewelry)"
                    />
                  </div>
                </div>
                <div className="flex justify-end gap-2 pt-4 border-t bg-white sticky bottom-0">
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddSupplier}>Add Supplier</Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search suppliers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Badge variant="secondary">{filteredSuppliers?.length} suppliers</Badge>
          </div>

          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Supplier Details</TableHead>
                    <TableHead>Contact Information</TableHead>
                    <TableHead>Speciality</TableHead>
                    <TableHead>Purchase History</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSuppliers?.map((supplier) => (
                    <TableRow key={supplier.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{supplier.name}</p>
                          <p className="text-sm text-muted-foreground flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {supplier.location}
                          </p>
                          <p className="text-sm font-medium text-blue-600">{supplier.contact_person}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="text-sm flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {supplier.phone}
                          </p>
                          <p className="text-sm flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {supplier.email}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{supplier.speciality}</Badge>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">₹{(Number(supplier.total_purchases) || 0).toLocaleString("en-IN")}</p>
                          <p className="text-sm text-muted-foreground">Last: {supplier.last_purchase_date || 'N/A'}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={supplier.status === "Active" ? "default" : "secondary"}>
                          {supplier.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm" onClick={() => handleEditSupplier(supplier)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700 bg-transparent"
                          onClick={() => handleDeleteSupplier(supplier.id)}
                          disabled={mutating}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
      <EditSupplierDialog
        supplier={editingSupplier}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSuccess={refetch}
      />
    </div>
  )
}
