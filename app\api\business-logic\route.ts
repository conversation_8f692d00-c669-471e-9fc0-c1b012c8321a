import { NextRequest, NextResponse } from "next/server"
import { BusinessLogic } from "@/lib/utils/business-logic"

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    const { operation, params, userId } = data

    switch (operation) {
      case 'convertPurity':
        const { weight, fromPurity, toPurity } = params
        const convertedWeight = await BusinessLogic.convertPurity(weight, fromPurity, toPurity, userId)
        return NextResponse.json({ success: true, data: { convertedWeight } })

      case 'calculateWastage':
        const { weight: wastageWeight, formType } = params
        const wastage = await BusinessLogic.calculateWastage(wastageWeight, formType, userId)
        return NextResponse.json({ success: true, data: { wastage } })

      case 'calculateExpectedYield':
        const { procuredWeight, formType: yieldFormType, processingLoss } = params
        const expectedYield = await BusinessLogic.calculateExpectedYield(procuredWeight, yieldFormType, processingLoss, userId)
        return NextResponse.json({ success: true, data: { expectedYield } })

      case 'calculateMakingCharges':
        const { weight: makingWeight } = params
        const makingCharges = await BusinessLogic.calculateMakingCharges(makingWeight, userId)
        return NextResponse.json({ success: true, data: { makingCharges } })

      case 'calculateGST':
        const { amount } = params
        const gst = await BusinessLogic.calculateGST(amount, userId)
        return NextResponse.json({ success: true, data: { gst } })

      case 'calculateTotalAmount':
        const { goldValue, makingCharges: charges, stoneValue } = params
        const totalAmount = await BusinessLogic.calculateTotalAmount(goldValue, charges, stoneValue, userId)
        return NextResponse.json({ success: true, data: { totalAmount } })

      case 'roundToSettingsPrecision':
        const { value } = params
        const roundedValue = await BusinessLogic.roundToSettingsPrecision(value, userId)
        return NextResponse.json({ success: true, data: { roundedValue } })

      case 'isLowStock':
        const { currentStock } = params
        const isLow = await BusinessLogic.isLowStock(currentStock, userId)
        return NextResponse.json({ success: true, data: { isLowStock: isLow } })

      case 'shouldTriggerWastageAlert':
        const { wastagePercentage } = params
        const shouldAlert = await BusinessLogic.shouldTriggerWastageAlert(wastagePercentage, userId)
        return NextResponse.json({ success: true, data: { shouldTriggerWastageAlert: shouldAlert } })

      case 'calculateBatchTotals':
        const { items, goldRates } = params
        const batchTotals = await BusinessLogic.calculateBatchTotals(items, goldRates, userId)
        return NextResponse.json({ success: true, data: batchTotals })

      default:
        return NextResponse.json(
          { success: false, error: "Unknown operation" },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error("Business logic API error:", error)
    return NextResponse.json(
      { success: false, error: "Business logic calculation failed" },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const operation = searchParams.get("operation")
    const userId = searchParams.get("userId")

    switch (operation) {
      case 'getWastageRate':
        const formType = searchParams.get("formType")
        if (!formType) {
          return NextResponse.json(
            { success: false, error: "formType is required" },
            { status: 400 }
          )
        }
        const wastageRate = await BusinessLogic.getWastageRate(formType, userId ? parseInt(userId) : undefined)
        return NextResponse.json({ success: true, data: { wastageRate } })

      case 'getConversionFactor':
        const fromPurity = searchParams.get("fromPurity")
        const toPurity = searchParams.get("toPurity")
        if (!fromPurity || !toPurity) {
          return NextResponse.json(
            { success: false, error: "fromPurity and toPurity are required" },
            { status: 400 }
          )
        }
        const conversionFactor = await BusinessLogic.getConversionFactor(fromPurity, toPurity, userId ? parseInt(userId) : undefined)
        return NextResponse.json({ success: true, data: { conversionFactor } })

      default:
        return NextResponse.json(
          { success: false, error: "Unknown operation" },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error("Business logic API error:", error)
    return NextResponse.json(
      { success: false, error: "Business logic operation failed" },
      { status: 500 }
    )
  }
}
