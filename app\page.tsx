"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Gem, Package, Receipt, Users, TrendingUp, FileText, Database, Calculator, TrendingDown, Settings } from "lucide-react"
import InventoryManagementImproved from "@/components/inventory-management-improved"
import BillingSystem from "@/components/billing-system"
import SupplierManagement from "@/components/supplier-management"
import Dashboard from "@/components/dashboard"
import Reports from "@/components/reports"
import AdvancedReports from "@/components/advanced-reports"
import SalesWastageManagement from "@/components/sales-wastage-management"
import BusinessSettings from "@/components/business-settings"
import GoldRateTracker from "@/components/gold-rate-tracker"
import BackupRestore from "@/components/backup-restore"
import CalculationVerification from "@/components/calculation-verification"
import CustomerManagement from "@/components/customer-management"
import UserManagement from "@/components/user-management"

export default function JewelleryWholesaleSoftware() {
  const [activeTab, setActiveTab] = useState("dashboard")

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-50">
      <div className="container mx-auto p-4">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-gradient-to-r from-amber-500 to-orange-500 rounded-lg">
              <Gem className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">Jewellery Wholesale Management</h1>
            <Badge variant="secondary" className="ml-auto">
              Pro Version
            </Badge>
          </div>
          <p className="text-gray-600">Complete solution for Indian jewellery wholesale business</p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-11 lg:w-auto lg:grid-cols-11">
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Dashboard
            </TabsTrigger>
            <TabsTrigger value="inventory" className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              Inventory
            </TabsTrigger>
            <TabsTrigger value="sales-wastage" className="flex items-center gap-2">
              <TrendingDown className="h-4 w-4" />
              Sales & Wastage
            </TabsTrigger>
            <TabsTrigger value="billing" className="flex items-center gap-2">
              <Receipt className="h-4 w-4" />
              Billing
            </TabsTrigger>
            <TabsTrigger value="customers" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Customers
            </TabsTrigger>
            <TabsTrigger value="suppliers" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Suppliers
            </TabsTrigger>
            <TabsTrigger value="reports" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Reports
            </TabsTrigger>
            <TabsTrigger value="advanced-reports" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="goldrates" className="flex items-center gap-2">
              <Gem className="h-4 w-4" />
              Gold Rates
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </TabsTrigger>
            <TabsTrigger value="backup" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              Backup
            </TabsTrigger>
            <TabsTrigger value="calculations" className="flex items-center gap-2">
              <Calculator className="h-4 w-4" />
              Calculations
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard">
            <Dashboard />
          </TabsContent>

          <TabsContent value="inventory">
            <InventoryManagementImproved />
          </TabsContent>

          <TabsContent value="sales-wastage">
            <SalesWastageManagement />
          </TabsContent>

          <TabsContent value="billing">
            <BillingSystem />
          </TabsContent>

          <TabsContent value="customers">
            <CustomerManagement />
          </TabsContent>

          <TabsContent value="suppliers">
            <SupplierManagement />
          </TabsContent>

          <TabsContent value="reports">
            <Reports />
          </TabsContent>

          <TabsContent value="advanced-reports">
            <AdvancedReports />
          </TabsContent>

          <TabsContent value="goldrates">
            <GoldRateTracker />
          </TabsContent>

          <TabsContent value="settings">
            <BusinessSettings />
          </TabsContent>

          <TabsContent value="backup">
            <BackupRestore />
          </TabsContent>

          <TabsContent value="calculations">
            <CalculationVerification />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
