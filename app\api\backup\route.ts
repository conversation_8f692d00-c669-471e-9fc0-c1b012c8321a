import { type NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/database"

export async function POST(request: NextRequest) {
  try {
    const { type } = await request.json()

    // Get all table data
    const tables = ["suppliers", "customers", "inventory", "bills", "gold_rates", "users"]
    let sqlDump = `-- Jewellery Wholesale Database Backup\n-- Generated on ${new Date().toISOString()}\n\n`

    for (const table of tables) {
      if (type === "partial" && !["bills", "gold_rates"].includes(table)) {
        continue // Skip non-essential tables for partial backup
      }

      const rows = await executeQuery(`SELECT * FROM ${table}`)

      if (rows.length > 0) {
        sqlDump += `-- Table: ${table}\n`
        sqlDump += `DELETE FROM ${table};\n`

        const columns = Object.keys(rows[0])
        const values = rows
          .map(
            (row) =>
              `(${columns
                .map((col) => {
                  const val = row[col]
                  if (val === null) return "NULL"
                  if (typeof val === "string") return `'${val.replace(/'/g, "''")}'`
                  if (val instanceof Date) return `'${val.toISOString().slice(0, 19).replace("T", " ")}'`
                  return val
                })
                .join(", ")})`,
          )
          .join(",\n")

        sqlDump += `INSERT INTO ${table} (${columns.join(", ")}) VALUES\n${values};\n\n`
      }
    }

    return new NextResponse(sqlDump, {
      headers: {
        "Content-Type": "application/sql",
        "Content-Disposition": `attachment; filename="jewellery_backup_${new Date().toISOString().split("T")[0]}.sql"`,
      },
    })
  } catch (error) {
    console.error("Backup error:", error)
    return NextResponse.json({ success: false, error: "Backup failed" }, { status: 500 })
  }
}
