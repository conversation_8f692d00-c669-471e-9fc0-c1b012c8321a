#!/usr/bin/env node

require("dotenv").config({ path: ".env.local" })

const mysql = require("mysql2/promise")

async function addMakingChargesColumn() {
  console.log("🔧 Adding making_charges column to bills table...\n")

  const config = {
    host: process.env.DB_HOST || "localhost",
    port: Number.parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "jewellery_wholesale_software",
  }

  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(config)

    // Check if column already exists
    const [columns] = await connection.execute(
      "SHOW COLUMNS FROM bills LIKE 'making_charges'"
    )

    if (columns.length > 0) {
      console.log("✅ making_charges column already exists")
    } else {
      console.log("➕ Adding making_charges column...")
      await connection.execute(
        "ALTER TABLE bills ADD COLUMN making_charges DECIMAL(10,2) DEFAULT 0 AFTER stone_price"
      )
      console.log("✅ making_charges column added successfully")
    }

    await connection.end()
    console.log("\n🎉 Database update completed!")
    
  } catch (error) {
    console.log("\n❌ Database update failed:")
    console.log(`   Error: ${error.message}`)
    process.exit(1)
  }
}

addMakingChargesColumn()
