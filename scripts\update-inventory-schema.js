const mysql = require('mysql2/promise');
require('dotenv').config();

async function updateInventorySchema() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME
    });

    console.log('Connected to database');

    // Drop the existing inventory table and recreate with proper schema
    console.log('Dropping existing inventory table...');
    await connection.execute('DROP TABLE IF EXISTS inventory');

    // Create the proper inventory table
    console.log('Creating new inventory table with proper schema...');
    await connection.execute(`
      CREATE TABLE inventory (
          id INT AUTO_INCREMENT PRIMARY KEY,
          supplier_id INT,
          product_name VARCHAR(255) NOT NULL,
          product_type VARCHAR(255),
          metal_type ENUM('Gold', 'Silver', 'Platinum') DEFAULT 'Gold',
          form_type ENUM('Bar', 'Jewel', 'Old Jewel') DEFAULT 'Jewel',
          jewel_type ENUM('With Stone', 'Without Stone') NULL,
          jewel_category VARCHAR(100) NULL,
          with_stone_weight DECIMAL(10,3) DEFAULT 0,
          without_stone_weight DECIMAL(10,3) DEFAULT 0,
          stone_weight DECIMAL(10,3) DEFAULT 0,
          with_stone_cost DECIMAL(5,2) DEFAULT 0,
          without_stone_cost DECIMAL(5,2) DEFAULT 0,
          procured_in_24k DECIMAL(10,3) DEFAULT 0,
          sold_value_with_stone DECIMAL(10,3) DEFAULT 0,
          sold_value_without_stone DECIMAL(10,3) DEFAULT 0,
          balance_weight_24k DECIMAL(10,3) DEFAULT 0,
          balance_weight_22k DECIMAL(10,3) DEFAULT 0,
          status ENUM('Available', 'Low Stock', 'Out of Stock') DEFAULT 'Available',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_inventory_supplier_id (supplier_id),
          INDEX idx_inventory_product_name (product_name),
          INDEX idx_inventory_status (status),
          INDEX idx_inventory_metal_type (metal_type),
          INDEX idx_inventory_form_type (form_type)
      )
    `);

    console.log('Successfully updated inventory table schema');

    // Check if suppliers table exists, if not create it
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'suppliers'
    `, [process.env.DB_NAME || 'jewellery_wholesale']);

    if (tables.length === 0) {
      console.log('Creating suppliers table...');
      await connection.execute(`
        CREATE TABLE suppliers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            location VARCHAR(255),
            contact_person VARCHAR(255),
            phone VARCHAR(20),
            email VARCHAR(255),
            address TEXT,
            speciality VARCHAR(255),
            total_purchases DECIMAL(15,2) DEFAULT 0,
            last_purchase_date DATE,
            status ENUM('Active', 'Inactive') DEFAULT 'Active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_suppliers_name (name),
            INDEX idx_suppliers_status (status)
        )
      `);
      console.log('Successfully created suppliers table');
    }

    // Add foreign key constraint
    await connection.execute(`
      ALTER TABLE inventory 
      ADD CONSTRAINT fk_inventory_supplier 
      FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL
    `);

    console.log('Successfully added foreign key constraint');

  } catch (error) {
    console.error('Error updating schema:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
updateInventorySchema();
