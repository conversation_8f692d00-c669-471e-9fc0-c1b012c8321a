"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Download, FileText, TrendingUp, Package, Users, IndianRupee } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from "recharts"
import { useDatabase } from "@/lib/hooks/use-database"
import type { Bill, InventoryItem, Supplier, Customer } from "@/lib/types"

export default function Reports() {
  const [dateFrom, setDateFrom] = useState<Date>()
  const [dateTo, setDateTo] = useState<Date>()
  const [reportType, setReportType] = useState("sales")

  const { data: bills } = useDatabase<Bill>("bills")
  const { data: inventory } = useDatabase<InventoryItem>("inventory")
  const { data: suppliers } = useDatabase<Supplier>("suppliers")
  const { data: customers } = useDatabase<Customer>("customers")

  // Calculate real report data
  const salesReport = bills
    .filter((bill) => bill.status === "Paid")
    .map((bill) => {
      const amount = Number(bill.total_amount) || 0
      return {
        customer: bill.customer_name || "Unknown",
        location: bill.customer_location || "Unknown",
        items: 1,
        amount: amount,
        profit: amount * 0.15, // Estimated 15% profit margin
      }
    })

  const inventoryReport = inventory.map((item) => {
    const weight = Number(item.balance_weight_24k) || 0
    return {
      product: item.product_name,
      stock: `${weight.toFixed(1)}kg`,
      value: weight * 6890,
      turnover: weight > 50 ? "High" : weight > 20 ? "Medium" : "Low",
    }
  })

  const supplierReport = [
    { supplier: "Emerald Jewel Industry", orders: 15, amount: 2500000, rating: "Excellent" },
    { supplier: "Nala Gold", orders: 12, amount: 3200000, rating: "Good" },
    { supplier: "SSJ", orders: 8, amount: 1800000, rating: "Average" },
  ]

  const profitAnalysis = [
    { month: "Sep", revenue: 450000, cost: 380000, profit: 70000 },
    { month: "Oct", revenue: 520000, cost: 420000, profit: 100000 },
    { month: "Nov", revenue: 680000, cost: 550000, profit: 130000 },
    { month: "Dec", revenue: 750000, cost: 600000, profit: 150000 },
    { month: "Jan", revenue: 890000, cost: 710000, profit: 180000 },
  ]

  const productDistribution = [
    { name: "Gold Chains", value: 35, color: "#f59e0b" },
    { name: "Gold Bangles", value: 25, color: "#10b981" },
    { name: "Diamond Studs", value: 20, color: "#3b82f6" },
    { name: "Gold Rings", value: 12, color: "#8b5cf6" },
    { name: "Necklaces", value: 8, color: "#ef4444" },
  ]

  const topCustomers = [
    { name: "Krishna Jewels", purchases: 15, amount: 850000 },
    { name: "VS Jewellery", purchases: 12, amount: 650000 },
    { name: "Dhanapal Jewels", purchases: 8, amount: 420000 },
    { name: "Ravi Gold", purchases: 6, amount: 380000 },
    { name: "Sree Jewellers", purchases: 5, amount: 290000 },
  ]

  const handleExportPDF = () => {
    // Create CSV data for now (PDF generation would require additional library)
    const csvData = salesReport
      .map((row) => `${row.customer},${row.location},${row.items},${row.amount},${row.profit}`)
      .join("\n")

    const blob = new Blob([`Customer,Location,Items,Amount,Profit\n${csvData}`], { type: "text/csv" })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `sales-report-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Business Reports & Analytics
          </CardTitle>
          <CardDescription>Comprehensive business insights and performance metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>From Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-[240px] justify-start text-left font-normal",
                        !dateFrom && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateFrom ? format(dateFrom, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar mode="single" selected={dateFrom} onSelect={setDateFrom} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label>To Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-[240px] justify-start text-left font-normal",
                        !dateTo && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateTo ? format(dateTo, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar mode="single" selected={dateTo} onSelect={setDateTo} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <Button className="flex items-center gap-2" onClick={handleExportPDF}>
              <Download className="h-4 w-4" />
              Export PDF
            </Button>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sales">Sales Report</TabsTrigger>
          <TabsTrigger value="inventory">Inventory Report</TabsTrigger>
          <TabsTrigger value="suppliers">Supplier Report</TabsTrigger>
          <TabsTrigger value="profit">Profit Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <IndianRupee className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">₹12,45,890</div>
                <p className="text-xs text-muted-foreground">+15.2% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                <Package className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">156</div>
                <p className="text-xs text-muted-foreground">+8.1% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
                <Users className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">48</div>
                <p className="text-xs text-muted-foreground">+12.5% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Profit Margin</CardTitle>
                <TrendingUp className="h-4 w-4 text-amber-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">18.5%</div>
                <p className="text-xs text-muted-foreground">+2.1% from last month</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Profit Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={profitAnalysis}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `₹${(value / 1000).toFixed(0)}K`} />
                    <Tooltip formatter={(value) => [`₹${value.toLocaleString("en-IN")}`, ""]} />
                    <Line type="monotone" dataKey="profit" stroke="#10b981" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Product Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={productDistribution}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {productDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Top Customers</CardTitle>
              <CardDescription>Highest value customers this month</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topCustomers.map((customer, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center text-white font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{customer.name}</p>
                        <p className="text-sm text-muted-foreground">{customer.purchases} purchases</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold">₹{(Number(customer.amount) || 0).toLocaleString("en-IN")}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sales" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Sales Performance Report</CardTitle>
              <CardDescription>Detailed sales analysis by customer and location</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto max-h-[500px] overflow-y-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Customer</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Items Sold</TableHead>
                        <TableHead>Total Amount</TableHead>
                        <TableHead>Estimated Profit</TableHead>
                        <TableHead>Performance</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {salesReport.map((sale, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{sale.customer}</TableCell>
                          <TableCell>{sale.location}</TableCell>
                          <TableCell>{sale.items}</TableCell>
                          <TableCell>₹{(Number(sale.amount) || 0).toLocaleString("en-IN")}</TableCell>
                          <TableCell className="text-green-600">₹{(Number(sale.profit) || 0).toLocaleString("en-IN")}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                sale.amount > 200000 ? "default" : sale.amount > 100000 ? "secondary" : "outline"
                              }
                            >
                              {sale.amount > 200000 ? "Excellent" : sale.amount > 100000 ? "Good" : "Average"}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inventory" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Analysis Report</CardTitle>
              <CardDescription>Stock levels and inventory turnover analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto max-h-[500px] overflow-y-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product Category</TableHead>
                        <TableHead>Current Stock</TableHead>
                        <TableHead>Stock Value</TableHead>
                        <TableHead>Turnover Rate</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {inventoryReport.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{item.product}</TableCell>
                          <TableCell>{item.stock}</TableCell>
                          <TableCell>₹{(Number(item.value) || 0).toLocaleString("en-IN")}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                item.turnover === "High"
                                  ? "default"
                                  : item.turnover === "Medium"
                                    ? "secondary"
                                    : "outline"
                              }
                            >
                              {item.turnover}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={item.turnover === "Low" ? "destructive" : "default"}>
                              {item.turnover === "Low" ? "Reorder Soon" : "Optimal"}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="suppliers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Supplier Performance Report</CardTitle>
              <CardDescription>Supplier analysis and performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto max-h-[500px] overflow-y-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Supplier Name</TableHead>
                        <TableHead>Total Orders</TableHead>
                        <TableHead>Purchase Amount</TableHead>
                        <TableHead>Performance Rating</TableHead>
                        <TableHead>Reliability</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {supplierReport.map((supplier, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{supplier.supplier}</TableCell>
                          <TableCell>{supplier.orders}</TableCell>
                          <TableCell>₹{(Number(supplier.amount) || 0).toLocaleString("en-IN")}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                supplier.rating === "Excellent"
                                  ? "default"
                                  : supplier.rating === "Good"
                                    ? "secondary"
                                    : "outline"
                              }
                            >
                              {supplier.rating}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <div className="w-16 bg-gray-200 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${supplier.rating === "Excellent" ? "bg-green-500" : supplier.rating === "Good" ? "bg-yellow-500" : "bg-red-500"}`}
                                  style={{
                                    width:
                                      supplier.rating === "Excellent"
                                        ? "90%"
                                        : supplier.rating === "Good"
                                          ? "70%"
                                          : "50%",
                                  }}
                                ></div>
                              </div>
                              <span className="text-sm text-muted-foreground">
                                {supplier.rating === "Excellent" ? "90%" : supplier.rating === "Good" ? "70%" : "50%"}
                              </span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="profit" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Profit & Loss Analysis</CardTitle>
              <CardDescription>Monthly profit trends and margin analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={profitAnalysis}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `₹${(value / 1000).toFixed(0)}K`} />
                  <Tooltip formatter={(value) => [`₹${value.toLocaleString("en-IN")}`, ""]} />
                  <Bar dataKey="revenue" fill="#10b981" name="Revenue" />
                  <Bar dataKey="cost" fill="#f59e0b" name="Cost" />
                  <Bar dataKey="profit" fill="#3b82f6" name="Profit" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Average Profit Margin</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">18.5%</div>
                <p className="text-sm text-muted-foreground">Industry average: 15%</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Best Performing Month</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">January</div>
                <p className="text-sm text-muted-foreground">₹1,80,000 profit</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Growth Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-purple-600">+25%</div>
                <p className="text-sm text-muted-foreground">Year over year</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
