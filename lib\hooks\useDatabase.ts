"use client"

import { useState, useEffect } from "react"

interface DatabaseHook<T> {
  data: T[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useDatabase<T>(endpoint: string, searchTerm?: string): DatabaseHook<T> {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      const url = new URL(`/api/${endpoint}`, window.location.origin)
      if (searchTerm) {
        url.searchParams.set("search", searchTerm)
      }

      const response = await fetch(url.toString())
      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch data")
      }

      setData(result.data || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
      setData([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [endpoint, searchTerm])

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  }
}

export function useDatabaseMutation<T>() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const mutate = async (endpoint: string, method: "POST" | "PUT" | "DELETE", data?: any): Promise<T | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/${endpoint}`, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: data ? JSON.stringify(data) : undefined,
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || "Operation failed")
      }

      return result.data || null
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
      return null
    } finally {
      setLoading(false)
    }
  }

  return {
    mutate,
    loading,
    error,
  }
}
