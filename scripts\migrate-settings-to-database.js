const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function migrateSettingsToDatabase() {
  let connection;

  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME
  };

  try {
    console.log('🔌 Connecting to database...');
    connection = await mysql.createConnection(config);
    console.log(`📊 Connected to database: ${process.env.DB_NAME}`);

    console.log('\n=== SETTINGS MIGRATION GUIDE ===\n');

    // Check if settings tables exist
    const [settingsTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME IN ('settings', 'user_settings')
    `, [process.env.DB_NAME]);

    if (settingsTables.length < 2) {
      console.log('❌ Settings tables not found. Please run:');
      console.log('   node scripts/create-settings-table.js');
      return;
    }

    console.log('✅ Settings tables found');

    // Check current settings in database
    const [currentSettings] = await connection.execute(`
      SELECT category, setting_key, setting_value 
      FROM settings 
      ORDER BY category, setting_key
    `);

    console.log(`📊 Found ${currentSettings.length} settings in database:`);
    
    const settingsByCategory = {};
    currentSettings.forEach(setting => {
      if (!settingsByCategory[setting.category]) {
        settingsByCategory[setting.category] = [];
      }

      let value;
      try {
        value = typeof setting.setting_value === 'string'
          ? JSON.parse(setting.setting_value)
          : setting.setting_value;
      } catch (error) {
        value = setting.setting_value; // Use raw value if JSON parsing fails
      }

      settingsByCategory[setting.category].push({
        key: setting.setting_key,
        value: value
      });
    });

    Object.keys(settingsByCategory).forEach(category => {
      console.log(`\n📁 ${category.toUpperCase()}:`);
      settingsByCategory[category].forEach(setting => {
        console.log(`   ${setting.key}: ${setting.value}`);
      });
    });

    console.log('\n=== MIGRATION INSTRUCTIONS ===\n');

    console.log('🔄 AUTOMATIC MIGRATION:');
    console.log('   The application will automatically migrate localStorage settings');
    console.log('   to the database when you first load the Business Settings page.');
    console.log('   This happens only once per browser.');

    console.log('\n📱 MANUAL MIGRATION (if needed):');
    console.log('   1. Open your browser Developer Tools (F12)');
    console.log('   2. Go to Application → Local Storage');
    console.log('   3. Look for key: "business_settings"');
    console.log('   4. Copy the JSON value');
    console.log('   5. Use the Business Settings page to update values');

    console.log('\n🔧 API MIGRATION:');
    console.log('   You can also migrate programmatically using the API:');
    console.log('   POST /api/settings with your settings data');

    console.log('\n✅ BENEFITS OF DATABASE SETTINGS:');
    console.log('   ✅ Shared across all devices and browsers');
    console.log('   ✅ User-specific overrides supported');
    console.log('   ✅ Included in database backups');
    console.log('   ✅ Audit trail of changes');
    console.log('   ✅ Admin can manage system-wide defaults');
    console.log('   ✅ Settings persist even if browser cache is cleared');

    console.log('\n🎯 NEXT STEPS:');
    console.log('   1. Update your components to use the new hook:');
    console.log('      import { useBusinessSettings } from "@/hooks/use-database-settings"');
    console.log('   2. The old hook will automatically migrate and then use database');
    console.log('   3. Test the Business Settings page to ensure everything works');
    console.log('   4. Settings will be automatically migrated on first use');

    // Test API endpoint
    console.log('\n🧪 TESTING API ENDPOINTS:');
    console.log('   GET /api/settings?business=true - Get all business settings');
    console.log('   PUT /api/settings - Update settings');
    console.log('   GET /api/settings?category=wastage - Get wastage settings');

    console.log('\n=== MIGRATION READY ===');
    console.log('✅ Database settings system is ready to use');
    console.log('✅ Automatic migration will happen on first page load');
    console.log('✅ All existing functionality preserved');

  } catch (error) {
    console.error('❌ Error during migration check:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the migration guide
migrateSettingsToDatabase();
