interface DatabaseConfig {
  host: string
  port: number
  user: string
  password: string
  database: string
  ssl?: boolean
  sslCa?: string
}

interface AppConfig {
  name: string
  version: string
  companyName: string
  companyAddress: string
  companyPhone: string
  companyEmail: string
}

interface SecurityConfig {
  jwtSecret: string
  bcryptRounds: number
  sessionSecret: string
}

interface EmailConfig {
  host: string
  port: number
  user: string
  password: string
  from: string
}

interface BusinessConfig {
  currency: string
  goldUnit: string
  taxRate: number
  makingCharges: number
}

interface FileConfig {
  maxFileSize: number
  uploadDir: string
  backupDir: string
}

interface Config {
  database: DatabaseConfig
  app: AppConfig
  security: SecurityConfig
  email: EmailConfig
  business: BusinessConfig
  files: FileConfig
  isDevelopment: boolean
  isProduction: boolean
  debug: boolean
  logLevel: string
}

function validateEnvVar(name: string, value: string | undefined, required = true): string {
  if (!value && required) {
    throw new Error(`Environment variable ${name} is required but not set`)
  }
  return value || ""
}

function validateNumericEnvVar(name: string, value: string | undefined, defaultValue?: number): number {
  if (!value) {
    if (defaultValue !== undefined) return defaultValue
    throw new Error(`Environment variable ${name} is required but not set`)
  }
  const numValue = Number.parseInt(value, 10)
  if (isNaN(numValue)) {
    throw new Error(`Environment variable ${name} must be a valid number`)
  }
  return numValue
}

function validateFloatEnvVar(name: string, value: string | undefined, defaultValue?: number): number {
  if (!value) {
    if (defaultValue !== undefined) return defaultValue
    throw new Error(`Environment variable ${name} is required but not set`)
  }
  const numValue = Number.parseFloat(value)
  if (isNaN(numValue)) {
    throw new Error(`Environment variable ${name} must be a valid number`)
  }
  return numValue
}

export const config: Config = {
  database: {
    host: validateEnvVar("DB_HOST", process.env.DB_HOST),
    port: validateNumericEnvVar("DB_PORT", process.env.DB_PORT, 3306),
    user: validateEnvVar("DB_USER", process.env.DB_USER),
    password: validateEnvVar("DB_PASSWORD", process.env.DB_PASSWORD),
    database: validateEnvVar("DB_NAME", process.env.DB_NAME),
    ssl: process.env.DB_SSL === "true",
    sslCa: process.env.DB_SSL_CA,
  },
  app: {
    name: validateEnvVar("NEXT_PUBLIC_APP_NAME", process.env.NEXT_PUBLIC_APP_NAME),
    version: validateEnvVar("NEXT_PUBLIC_APP_VERSION", process.env.NEXT_PUBLIC_APP_VERSION),
    companyName: validateEnvVar("NEXT_PUBLIC_COMPANY_NAME", process.env.NEXT_PUBLIC_COMPANY_NAME),
    companyAddress: validateEnvVar("NEXT_PUBLIC_COMPANY_ADDRESS", process.env.NEXT_PUBLIC_COMPANY_ADDRESS),
    companyPhone: validateEnvVar("NEXT_PUBLIC_COMPANY_PHONE", process.env.NEXT_PUBLIC_COMPANY_PHONE),
    companyEmail: validateEnvVar("NEXT_PUBLIC_COMPANY_EMAIL", process.env.NEXT_PUBLIC_COMPANY_EMAIL),
  },
  security: {
    jwtSecret: validateEnvVar("JWT_SECRET", process.env.JWT_SECRET),
    bcryptRounds: validateNumericEnvVar("BCRYPT_ROUNDS", process.env.BCRYPT_ROUNDS, 12),
    sessionSecret: validateEnvVar("SESSION_SECRET", process.env.SESSION_SECRET),
  },
  email: {
    host: validateEnvVar("SMTP_HOST", process.env.SMTP_HOST, false),
    port: validateNumericEnvVar("SMTP_PORT", process.env.SMTP_PORT, 587),
    user: validateEnvVar("SMTP_USER", process.env.SMTP_USER, false),
    password: validateEnvVar("SMTP_PASSWORD", process.env.SMTP_PASSWORD, false),
    from: validateEnvVar("SMTP_FROM", process.env.SMTP_FROM, false),
  },
  business: {
    currency: validateEnvVar("DEFAULT_CURRENCY", process.env.DEFAULT_CURRENCY, false) || "INR",
    goldUnit: validateEnvVar("DEFAULT_GOLD_UNIT", process.env.DEFAULT_GOLD_UNIT, false) || "gram",
    taxRate: validateFloatEnvVar("DEFAULT_TAX_RATE", process.env.DEFAULT_TAX_RATE, 3.0),
    makingCharges: validateFloatEnvVar("DEFAULT_MAKING_CHARGES", process.env.DEFAULT_MAKING_CHARGES, 10.0),
  },
  files: {
    maxFileSize: validateNumericEnvVar("MAX_FILE_SIZE", process.env.MAX_FILE_SIZE, 10485760),
    uploadDir: validateEnvVar("UPLOAD_DIR", process.env.UPLOAD_DIR, false) || "./uploads",
    backupDir: validateEnvVar("BACKUP_DIR", process.env.BACKUP_DIR, false) || "./backups",
  },
  isDevelopment: process.env.NODE_ENV === "development",
  isProduction: process.env.NODE_ENV === "production",
  debug: process.env.DEBUG === "true",
  logLevel: process.env.LOG_LEVEL || "info",
}

// Validate production security
if (config.isProduction) {
  const productionWarnings: string[] = []

  if (config.security.jwtSecret.includes("sample") || config.security.jwtSecret.length < 32) {
    productionWarnings.push("JWT_SECRET appears to be a sample value or too short for production")
  }

  if (config.security.sessionSecret.includes("sample") || config.security.sessionSecret.length < 32) {
    productionWarnings.push("SESSION_SECRET appears to be a sample value or too short for production")
  }

  if (config.database.password === "password123" || config.database.password.includes("sample")) {
    productionWarnings.push("DB_PASSWORD appears to be a sample value")
  }

  if (productionWarnings.length > 0) {
    console.warn("🚨 PRODUCTION SECURITY WARNINGS:")
    productionWarnings.forEach((warning) => console.warn(`   - ${warning}`))
    console.warn("   Please update these values before deploying to production!")
  }
}

export default config
