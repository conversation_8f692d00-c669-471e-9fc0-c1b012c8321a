"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Loader2 } from "lucide-react"
import { useDatabaseMutation } from "@/lib/hooks/useDatabase"
import { useToast } from "@/hooks/use-toast"
import type { Supplier } from "@/lib/models/supplier"

interface EditSupplierDialogProps {
  supplier: Supplier | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export default function EditSupplierDialog({ supplier, open, onOpenChange, onSuccess }: EditSupplierDialogProps) {
  const [formData, setFormData] = useState<Partial<Supplier>>({})
  const { mutate, loading } = useDatabaseMutation<Supplier>()
  const { toast } = useToast()

  useEffect(() => {
    if (supplier) {
      setFormData(supplier)
    }
  }, [supplier])

  const handleSubmit = async () => {
    if (!supplier || !formData.name || !formData.contact_person) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields (Name and Contact Person).",
        variant: "destructive",
      })
      return
    }

    try {
      const result = await mutate(`suppliers/${supplier.id}`, "PUT", {
        name: formData.name,
        location: formData.location,
        contact_person: formData.contact_person,
        phone: formData.phone,
        email: formData.email,
        address: formData.address,
        speciality: formData.speciality,
      })

      if (result) {
        toast({
          title: "Supplier Updated Successfully",
          description: `"${formData.name}" has been updated.`,
          variant: "default",
        })
        onOpenChange(false)
        onSuccess()
      } else {
        toast({
          title: "Update Failed",
          description: "Failed to update the supplier. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Update Error",
        description: "An error occurred while updating the supplier.",
        variant: "destructive",
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Supplier</DialogTitle>
          <DialogDescription>Update supplier information</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto px-1">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Supplier Name</Label>
              <Input
                id="name"
                value={formData.name || ""}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter supplier name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                value={formData.location || ""}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                placeholder="Enter location"
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="contact_person">Contact Person</Label>
              <Input
                id="contact_person"
                value={formData.contact_person || ""}
                onChange={(e) => setFormData({ ...formData, contact_person: e.target.value })}
                placeholder="Enter contact person name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={formData.phone || ""}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                placeholder="Enter phone number"
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              value={formData.email || ""}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="Enter email address"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="address">Address</Label>
            <Input
              id="address"
              value={formData.address || ""}
              onChange={(e) => setFormData({ ...formData, address: e.target.value })}
              placeholder="Enter full address"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="speciality">Speciality</Label>
            <Input
              id="speciality"
              value={formData.speciality || ""}
              onChange={(e) => setFormData({ ...formData, speciality: e.target.value })}
              placeholder="Enter speciality"
            />
          </div>
        </div>
        <div className="flex justify-end gap-2 pt-4 border-t bg-white sticky bottom-0">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Update Supplier
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
