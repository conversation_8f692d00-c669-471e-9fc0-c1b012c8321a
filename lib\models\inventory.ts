import { executeQuery, executeSingle, executeUpdate } from "../database"

export interface InventoryItem {
  id: number
  supplier_id: number
  supplier_name?: string
  supplier_location?: string
  supplier_contact_person?: string
  product_name: string
  product_type: string
  metal_type: "Gold" | "Silver" | "Platinum"
  form_type: "Bar" | "Jewel" | "Old Jewel"
  jewel_type?: "With Stone" | "Without Stone"
  jewel_category?: string
  with_stone_weight: number
  without_stone_weight: number
  stone_weight?: number
  with_stone_cost: number
  without_stone_cost: number
  with_stone_tunch_percentage?: number
  without_stone_tunch_percentage?: number
  procured_in_24k: number
  sold_value_with_stone: number
  sold_value_without_stone: number
  sold_value_24k?: number
  sold_value_22k?: number
  sold_value_18k?: number
  balance_weight_24k: number
  balance_weight_22k: number
  sold_gold_weight_24k?: number
  sold_gold_weight_22k?: number
  balance_gold_weight_22k?: number
  stone_weight_22k?: number
  making_charges?: number
  status: "Available" | "Low Stock" | "Out of Stock"
  created_at: string
  updated_at: string
}

export interface CreateInventoryData {
  supplier_id: number
  product_name: string
  product_type: string
  metal_type: "Gold" | "Silver" | "Platinum"
  form_type: "Bar" | "Jewel" | "Old Jewel"
  jewel_type?: "With Stone" | "Without Stone"
  jewel_category?: string
  with_stone_weight?: number
  without_stone_weight?: number
  stone_weight?: number
  with_stone_cost?: number
  without_stone_cost?: number
  with_stone_tunch_percentage?: number
  without_stone_tunch_percentage?: number
  procured_in_24k?: number
  balance_weight_24k?: number
  balance_weight_22k?: number

  // Sold values
  stone_weight_22k?: number
  sold_gold_weight_22k?: number
  sold_gold_weight_24k?: number

  // Balance in stock
  balance_gold_weight_22k?: number

  // Business parameters
  wastage_percentage?: number
  expected_processing_loss?: number
  making_charges?: number
}

// Business Logic Constants - These are fallback values only
// The system should use dynamic conversion factors from current gold rates
export const FALLBACK_PURITY_CONVERSION_FACTORS = {
  "24K_TO_22K": 0.916,
  "24K_TO_18K": 0.750,
  "22K_TO_24K": 1.092,
  "18K_TO_24K": 1.333
} as const

export const DEFAULT_WASTAGE_RATES = {
  "Bar": 0.5,      // 0.5% wastage for bars
  "Jewel": 2.0,    // 2% wastage for jewelry
  "Old Jewel": 3.0 // 3% wastage for old jewelry refining
} as const

export class InventoryModel {
  // Business Logic Methods
  static calculateWastage(weight: number, formType: string, customWastageRate?: number): number {
    const wastageRate = customWastageRate || DEFAULT_WASTAGE_RATES[formType as keyof typeof DEFAULT_WASTAGE_RATES] || 1.0
    return (weight * wastageRate) / 100
  }

  static convertPurity(weight: number, fromPurity: string, toPurity: string): number {
    const conversionKey = `${fromPurity}_TO_${toPurity}` as keyof typeof FALLBACK_PURITY_CONVERSION_FACTORS
    const factor = FALLBACK_PURITY_CONVERSION_FACTORS[conversionKey]
    return factor ? weight * factor : weight
  }

  // Async version that uses current gold rates for conversion
  static async convertPurityDynamic(weight: number, fromPurity: string, toPurity: string): Promise<number> {
    if (weight <= 0) return 0

    try {
      // For 24K to 22K conversion, use current gold rates
      if (fromPurity === "24K" && toPurity === "22K") {
        const conversionFactor = await this.getCurrentConversionFactor()
        return weight * conversionFactor
      }

      // For other conversions, try to get current rates
      const goldRatesQuery = `SELECT rate_24k, rate_22k, rate_18k FROM gold_rates ORDER BY rate_date DESC LIMIT 1`
      const goldRates = await executeQuery<any>(goldRatesQuery, [])

      if (goldRates.length > 0) {
        const rates = goldRates[0]

        // Calculate dynamic conversion factors based on current rates
        if (fromPurity === "24K" && toPurity === "18K" && rates.rate_24k > 0 && rates.rate_18k > 0) {
          return weight * (rates.rate_18k / rates.rate_24k)
        }
        if (fromPurity === "22K" && toPurity === "24K" && rates.rate_22k > 0 && rates.rate_24k > 0) {
          return weight * (rates.rate_24k / rates.rate_22k)
        }
        if (fromPurity === "18K" && toPurity === "24K" && rates.rate_18k > 0 && rates.rate_24k > 0) {
          return weight * (rates.rate_24k / rates.rate_18k)
        }
      }
    } catch (error) {
      console.error('Error in dynamic purity conversion:', error)
    }

    // Fallback to static conversion
    return this.convertPurity(weight, fromPurity, toPurity)
  }

  static calculateNetWeight(grossWeight: number, stoneWeight: number = 0): number {
    return Math.max(0, grossWeight - stoneWeight)
  }

  static calculateBalance(procured: number, sold: number, wastage: number = 0): number {
    return Math.max(0, procured - sold - wastage)
  }

  // =====================================================
  // JEWELLERY WHOLESALE SPECIFIC METHODS
  // =====================================================

  /**
   * Calculate 24K equivalent from 22K weight using tunch percentage
   * Based on CSV sample calculations
   */
  static calculate24KFromTunch(weight22k: number, tunchPercentage: number): number {
    if (weight22k <= 0 || tunchPercentage <= 0) return 0
    return Number(((weight22k * tunchPercentage) / 100).toFixed(3))
  }

  /**
   * Calculate suggested tunch percentages based on product type and stone presence
   */
  static getSuggestedTunchPercentages(productType: string, hasStone: boolean): {
    withStone: number
    withoutStone: number
  } {
    const baseRates: { [key: string]: { withStone: number; withoutStone: number } } = {
      'Chain': { withStone: 93, withoutStone: 96 },
      'Bangles': { withStone: 94, withoutStone: 98 },
      'Studs': { withStone: 95, withoutStone: 99 },
      'Ring': { withStone: 94, withoutStone: 97 },
      'Necklace': { withStone: 93, withoutStone: 96 },
      'Earrings': { withStone: 95, withoutStone: 98 },
      'Pendant': { withStone: 94, withoutStone: 97 }
    }
    
    return baseRates[productType] || { withStone: 94, withoutStone: 97 }
  }

  /**
   * Calculate stock balance using CSV sample logic
   */
  static calculateStockBalance(procuredWeight: number, soldWeight: number): number {
    const balance = procuredWeight - soldWeight
    return Math.max(0, Number(balance.toFixed(3)))
  }

  static calculateExpectedYield(procuredWeight: number, formType: string, wastageRate?: number): {
    expectedWastage: number
    expectedYield: number
    yieldPercentage: number
  } {
    const wastage = this.calculateWastage(procuredWeight, formType, wastageRate)
    const expectedYield = procuredWeight - wastage
    const yieldPercentage = (expectedYield / procuredWeight) * 100

    return {
      expectedWastage: wastage,
      expectedYield: expectedYield,
      yieldPercentage: yieldPercentage
    }
  }

  // Get all inventory items with supplier details
  static async getAll(): Promise<InventoryItem[]> {
    const query = `
      SELECT i.*, s.name as supplier_name, s.location as supplier_location, 
             s.contact_person as supplier_contact_person
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      ORDER BY i.created_at DESC
    `
    return executeQuery<InventoryItem>(query)
  }

  // Get inventory item by ID
  static async getById(id: number): Promise<InventoryItem | null> {
    const query = `
      SELECT i.*, s.name as supplier_name, s.location as supplier_location,
             s.contact_person as supplier_contact_person
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      WHERE i.id = ?
    `
    const results = await executeQuery<InventoryItem>(query, [id])
    return results[0] || null
  }

  // Create new inventory item
  static async create(data: CreateInventoryData): Promise<number> {
    const procured24k = data.procured_in_24k || 0

    // Calculate expected wastage and yield
    const yieldData = this.calculateExpectedYield(
      procured24k,
      data.form_type,
      data.wastage_percentage
    )

    // Use provided balance weights or calculate them
    const processingLoss = data.expected_processing_loss || 0
    const balanceWeight24k = data.balance_weight_24k !== undefined
      ? data.balance_weight_24k
      : this.calculateBalance(procured24k, 0, processingLoss)
    const balanceWeight22k = data.balance_weight_22k !== undefined
      ? data.balance_weight_22k
      : this.convertPurity(balanceWeight24k, "24K", "22K")

    // Calculate suggested tunch percentages if not provided
    const suggestedTunch = this.getSuggestedTunchPercentages(data.product_type, !!data.with_stone_weight)
    const withStoneTunch = data.with_stone_tunch_percentage || suggestedTunch.withStone
    const withoutStoneTunch = data.without_stone_tunch_percentage || suggestedTunch.withoutStone

    const query = `
      INSERT INTO inventory (
        supplier_id, product_name, product_type, metal_type, form_type,
        jewel_type, jewel_category, with_stone_weight, without_stone_weight, stone_weight,
        with_stone_cost, without_stone_cost, with_stone_tunch_percentage,
        without_stone_tunch_percentage, procured_in_24k, balance_weight_24k, balance_weight_22k,
        stone_weight_22k, sold_gold_weight_22k, sold_gold_weight_24k, balance_gold_weight_22k,
        wastage_percentage, expected_processing_loss, making_charges
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    const params = [
      data.supplier_id,
      data.product_name,
      data.product_type,
      data.metal_type,
      data.form_type,
      data.jewel_type || null,
      data.jewel_category || null,
      data.with_stone_weight || 0,
      data.without_stone_weight || 0,
      data.stone_weight || 0,
      data.with_stone_cost || 0,
      data.without_stone_cost || 0,
      withStoneTunch,
      withoutStoneTunch,
      data.procured_in_24k || 0,
      balanceWeight24k,
      balanceWeight22k,
      0, // stone_weight_22k - starts at 0, updated on sales
      0, // sold_gold_weight_22k - starts at 0, updated on sales
      0, // sold_gold_weight_24k - starts at 0, updated on sales
      balanceWeight22k, // balance_gold_weight_22k - initially same as balance_weight_22k
      data.wastage_percentage || 0,
      data.expected_processing_loss || 0,
      data.making_charges || 0,
    ]
    const result = await executeUpdate(query, params)
    return result.insertId!
  }

  // Update inventory item
  static async update(id: number, data: Partial<CreateInventoryData>): Promise<boolean> {
    const fields = Object.keys(data)
      .map((key) => `${key} = ?`)
      .join(", ")
    const values = Object.values(data)

    const query = `UPDATE inventory SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`
    const result = await executeUpdate(query, [...values, id])
    return result.affectedRows > 0
  }

  // Delete inventory item
  static async delete(id: number): Promise<boolean> {
    const query = "DELETE FROM inventory WHERE id = ?"
    const result = await executeUpdate(query, [id])
    return result.affectedRows > 0
  }

  // Search inventory
  static async search(searchTerm: string): Promise<InventoryItem[]> {
    const query = `
      SELECT i.*, s.name as supplier_name, s.location as supplier_location,
             s.contact_person as supplier_contact_person
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      WHERE i.product_name LIKE ? OR i.product_type LIKE ? OR s.name LIKE ?
      ORDER BY i.product_name
    `
    const searchPattern = `%${searchTerm}%`
    return executeQuery<InventoryItem>(query, [searchPattern, searchPattern, searchPattern])
  }

  // Get current conversion factor from gold rates
  static async getCurrentConversionFactor(): Promise<number> {
    try {
      const goldRatesQuery = `SELECT rate_24k, rate_22k FROM gold_rates ORDER BY rate_date DESC LIMIT 1`
      const goldRates = await executeQuery<any>(goldRatesQuery, [])

      if (goldRates.length > 0 && goldRates[0].rate_24k > 0 && goldRates[0].rate_22k > 0) {
        return goldRates[0].rate_22k / goldRates[0].rate_24k
      }
    } catch (error) {
      console.error('Error fetching conversion factor:', error)
    }

    // Fallback to default conversion factor
    return FALLBACK_PURITY_CONVERSION_FACTORS["24K_TO_22K"]
  }

  // Update sold values and reduce available stock after a sale
  static async recordSale(id: number, soldWeight22k: number, soldWeight24k: number = 0, stoneWeight22k: number = 0): Promise<boolean> {
    const query = `
      UPDATE inventory
      SET sold_gold_weight_22k = sold_gold_weight_22k + ?,
          sold_gold_weight_24k = sold_gold_weight_24k + ?,
          stone_weight_22k = stone_weight_22k + ?,
          balance_gold_weight_22k = balance_gold_weight_22k - ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND balance_gold_weight_22k >= ?
    `
    const result = await executeUpdate(query, [
      soldWeight22k,
      soldWeight24k,
      stoneWeight22k,
      soldWeight22k, // Reduce available stock by sold amount
      id,
      soldWeight22k // Ensure we have enough stock
    ])
    return result.affectedRows > 0
  }

  // Get available stock for sale
  static async getAvailableStock(id: number): Promise<number> {
    const query = `SELECT balance_gold_weight_22k FROM inventory WHERE id = ?`
    const results = await executeQuery<{balance_gold_weight_22k: number}>(query, [id])
    return results[0]?.balance_gold_weight_22k || 0
  }

  // Legacy method for backward compatibility
  static async updateSoldValues(id: number, soldWithStone: number, soldWithoutStone: number): Promise<boolean> {
    // Convert to 22K equivalent and use new method
    const conversionFactor = await this.getCurrentConversionFactor()
    const totalSold22k = (soldWithStone + soldWithoutStone) * conversionFactor
    return this.recordSale(id, totalSold22k)
  }

  // Get low stock items
  static async getLowStockItems(): Promise<InventoryItem[]> {
    const query = `
      SELECT i.*, s.name as supplier_name, s.location as supplier_location,
             s.contact_person as supplier_contact_person
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      WHERE i.balance_weight_24k < 10 OR i.status = 'Low Stock'
      ORDER BY i.balance_weight_24k ASC
    `
    return executeQuery<InventoryItem>(query)
  }

  // Get available items for billing
  static async getAvailableItems(): Promise<InventoryItem[]> {
    const query = `
      SELECT i.*, s.name as supplier_name, s.location as supplier_location,
             s.contact_person as supplier_contact_person
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      WHERE i.balance_weight_24k > 0 AND i.status != 'Out of Stock'
      ORDER BY i.product_name
    `
    return executeQuery<InventoryItem>(query)
  }
}
