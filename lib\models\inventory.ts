import { executeQuery, executeSingle, executeUpdate } from "../database"

export interface InventoryItem {
  id: number
  supplier_id: number
  supplier_name?: string
  supplier_location?: string
  supplier_contact_person?: string
  product_name: string
  product_type: string
  metal_type: "Gold" | "Silver" | "Platinum"
  form_type: "Bar" | "Jewel" | "Old Jewel"
  jewel_type?: "With Stone" | "Without Stone"
  jewel_category?: string
  with_stone_weight: number
  without_stone_weight: number
  stone_weight?: number
  with_stone_cost: number
  without_stone_cost: number
  procured_in_24k: number
  sold_value_with_stone: number
  sold_value_without_stone: number
  sold_value_24k?: number
  sold_value_22k?: number
  sold_value_18k?: number
  balance_weight_24k: number
  balance_weight_22k: number
  status: "Available" | "Low Stock" | "Out of Stock"
  created_at: string
  updated_at: string
}

export interface CreateInventoryData {
  supplier_id: number
  product_name: string
  product_type: string
  metal_type: "Gold" | "Silver" | "Platinum"
  form_type: "Bar" | "Jewel" | "Old Jewel"
  jewel_type?: "With Stone" | "Without Stone"
  jewel_category?: string
  with_stone_weight?: number
  without_stone_weight?: number
  stone_weight?: number
  with_stone_cost?: number
  without_stone_cost?: number
  procured_in_24k?: number
  wastage_percentage?: number
  expected_processing_loss?: number
}

// Business Logic Constants
export const PURITY_CONVERSION_FACTORS = {
  "24K_TO_22K": 0.916,
  "24K_TO_18K": 0.750,
  "22K_TO_24K": 1.092,
  "18K_TO_24K": 1.333
} as const

export const DEFAULT_WASTAGE_RATES = {
  "Bar": 0.5,      // 0.5% wastage for bars
  "Jewel": 2.0,    // 2% wastage for jewelry
  "Old Jewel": 3.0 // 3% wastage for old jewelry refining
} as const

export class InventoryModel {
  // Business Logic Methods
  static calculateWastage(weight: number, formType: string, customWastageRate?: number): number {
    const wastageRate = customWastageRate || DEFAULT_WASTAGE_RATES[formType as keyof typeof DEFAULT_WASTAGE_RATES] || 1.0
    return (weight * wastageRate) / 100
  }

  static convertPurity(weight: number, fromPurity: string, toPurity: string): number {
    const conversionKey = `${fromPurity}_TO_${toPurity}` as keyof typeof PURITY_CONVERSION_FACTORS
    const factor = PURITY_CONVERSION_FACTORS[conversionKey]
    return factor ? weight * factor : weight
  }

  static calculateNetWeight(grossWeight: number, stoneWeight: number = 0): number {
    return Math.max(0, grossWeight - stoneWeight)
  }

  static calculateBalance(procured: number, sold: number, wastage: number = 0): number {
    return Math.max(0, procured - sold - wastage)
  }

  static calculateExpectedYield(procuredWeight: number, formType: string, wastageRate?: number): {
    expectedWastage: number
    expectedYield: number
    yieldPercentage: number
  } {
    const wastage = this.calculateWastage(procuredWeight, formType, wastageRate)
    const expectedYield = procuredWeight - wastage
    const yieldPercentage = (expectedYield / procuredWeight) * 100

    return {
      expectedWastage: wastage,
      expectedYield: expectedYield,
      yieldPercentage: yieldPercentage
    }
  }

  // Get all inventory items with supplier details
  static async getAll(): Promise<InventoryItem[]> {
    const query = `
      SELECT i.*, s.name as supplier_name, s.location as supplier_location, 
             s.contact_person as supplier_contact_person
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      ORDER BY i.created_at DESC
    `
    return executeQuery<InventoryItem>(query)
  }

  // Get inventory item by ID
  static async getById(id: number): Promise<InventoryItem | null> {
    const query = `
      SELECT i.*, s.name as supplier_name, s.location as supplier_location,
             s.contact_person as supplier_contact_person
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      WHERE i.id = ?
    `
    const results = await executeQuery<InventoryItem>(query, [id])
    return results[0] || null
  }

  // Create new inventory item
  static async create(data: CreateInventoryData): Promise<number> {
    const procured24k = data.procured_in_24k || 0

    // Calculate expected wastage and yield
    const yieldData = this.calculateExpectedYield(
      procured24k,
      data.form_type,
      data.wastage_percentage
    )

    // Calculate initial balances (no sales yet, but account for expected processing loss)
    const processingLoss = data.expected_processing_loss || 0
    const balanceWeight24k = this.calculateBalance(procured24k, 0, processingLoss)
    const balanceWeight22k = this.convertPurity(balanceWeight24k, "24K", "22K")

    const query = `
      INSERT INTO inventory (
        supplier_id, product_name, product_type, metal_type, form_type,
        jewel_type, jewel_category, with_stone_weight, without_stone_weight,
        with_stone_cost, without_stone_cost, procured_in_24k,
        balance_weight_24k, balance_weight_22k
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    const params = [
      data.supplier_id,
      data.product_name,
      data.product_type,
      data.metal_type,
      data.form_type,
      data.jewel_type || null,
      data.jewel_category || null,
      data.with_stone_weight || 0,
      data.without_stone_weight || 0,
      data.with_stone_cost || 0,
      data.without_stone_cost || 0,
      data.procured_in_24k || 0,
      balanceWeight24k,
      balanceWeight22k,
    ]
    const result = await executeUpdate(query, params)
    return result.insertId!
  }

  // Update inventory item
  static async update(id: number, data: Partial<CreateInventoryData>): Promise<boolean> {
    const fields = Object.keys(data)
      .map((key) => `${key} = ?`)
      .join(", ")
    const values = Object.values(data)

    const query = `UPDATE inventory SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`
    const result = await executeUpdate(query, [...values, id])
    return result.affectedRows > 0
  }

  // Delete inventory item
  static async delete(id: number): Promise<boolean> {
    const query = "DELETE FROM inventory WHERE id = ?"
    const result = await executeUpdate(query, [id])
    return result.affectedRows > 0
  }

  // Search inventory
  static async search(searchTerm: string): Promise<InventoryItem[]> {
    const query = `
      SELECT i.*, s.name as supplier_name, s.location as supplier_location,
             s.contact_person as supplier_contact_person
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      WHERE i.product_name LIKE ? OR i.product_type LIKE ? OR s.name LIKE ?
      ORDER BY i.product_name
    `
    const searchPattern = `%${searchTerm}%`
    return executeQuery<InventoryItem>(query, [searchPattern, searchPattern, searchPattern])
  }

  // Update sold values and balance
  static async updateSoldValues(id: number, soldWithStone: number, soldWithoutStone: number): Promise<boolean> {
    const query = `
      UPDATE inventory 
      SET sold_value_with_stone = sold_value_with_stone + ?,
          sold_value_without_stone = sold_value_without_stone + ?,
          balance_weight_24k = procured_in_24k - (sold_value_with_stone + ?) - (sold_value_without_stone + ?),
          balance_weight_22k = (procured_in_24k - (sold_value_with_stone + ?) - (sold_value_without_stone + ?)) * 0.916,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `
    const result = await executeUpdate(query, [
      soldWithStone,
      soldWithoutStone,
      soldWithStone,
      soldWithoutStone,
      soldWithStone,
      soldWithoutStone,
      id,
    ])
    return result.affectedRows > 0
  }

  // Get low stock items
  static async getLowStockItems(): Promise<InventoryItem[]> {
    const query = `
      SELECT i.*, s.name as supplier_name, s.location as supplier_location,
             s.contact_person as supplier_contact_person
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      WHERE i.balance_weight_24k < 10 OR i.status = 'Low Stock'
      ORDER BY i.balance_weight_24k ASC
    `
    return executeQuery<InventoryItem>(query)
  }

  // Get available items for billing
  static async getAvailableItems(): Promise<InventoryItem[]> {
    const query = `
      SELECT i.*, s.name as supplier_name, s.location as supplier_location,
             s.contact_person as supplier_contact_person
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      WHERE i.balance_weight_24k > 0 AND i.status != 'Out of Stock'
      ORDER BY i.product_name
    `
    return executeQuery<InventoryItem>(query)
  }
}
