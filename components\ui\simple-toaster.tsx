"use client"

import { useSimpleToast } from "@/hooks/use-simple-toast"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"

export function SimpleToaster() {
  const { toasts, dismiss } = useSimpleToast()

  if (toasts.length === 0) return null

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-sm">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={cn(
            "relative flex items-start gap-3 rounded-lg border p-4 shadow-lg transition-all duration-300 ease-in-out",
            "bg-white border-gray-200",
            toast.variant === "destructive" 
              ? "border-red-200 bg-red-50" 
              : "border-green-200 bg-green-50"
          )}
        >
          <div className="flex-1 min-w-0">
            <div className={cn(
              "font-medium text-sm",
              toast.variant === "destructive" ? "text-red-900" : "text-green-900"
            )}>
              {toast.title}
            </div>
            {toast.description && (
              <div className={cn(
                "text-sm mt-1",
                toast.variant === "destructive" ? "text-red-700" : "text-green-700"
              )}>
                {toast.description}
              </div>
            )}
          </div>
          <button
            onClick={() => dismiss(toast.id)}
            className={cn(
              "flex-shrink-0 rounded-md p-1 hover:bg-gray-100 transition-colors",
              toast.variant === "destructive" ? "hover:bg-red-100" : "hover:bg-green-100"
            )}
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      ))}
    </div>
  )
}
