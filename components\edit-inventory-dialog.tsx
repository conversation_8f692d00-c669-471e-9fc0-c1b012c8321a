"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Loader2 } from "lucide-react"
import { useDatabaseMutation } from "@/lib/hooks/useDatabase"
import type { InventoryItem } from "@/lib/models/inventory"

interface EditInventoryDialogProps {
  item: InventoryItem | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export default function EditInventoryDialog({ item, open, onOpenChange, onSuccess }: EditInventoryDialogProps) {
  const [formData, setFormData] = useState<Partial<InventoryItem>>({})
  const { mutate, loading } = useDatabaseMutation<InventoryItem>()

  useEffect(() => {
    if (item) {
      setFormData(item)
    }
  }, [item])

  const handleSubmit = async () => {
    if (!item || !formData.product_name) return

    const result = await mutate(`inventory/${item.id}`, "PUT", {
      product_name: formData.product_name,
      product_type: formData.product_type,
      with_stone_weight: formData.with_stone_weight,
      without_stone_weight: formData.without_stone_weight,
      with_stone_cost: formData.with_stone_cost,
      without_stone_cost: formData.without_stone_cost,
      procured_in_24k: formData.procured_in_24k,
    })

    if (result) {
      onOpenChange(false)
      onSuccess()
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Inventory Item</DialogTitle>
          <DialogDescription>Update inventory item details</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto px-1">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="product_name">Product Name</Label>
              <Input
                id="product_name"
                value={formData.product_name || ""}
                onChange={(e) => setFormData({ ...formData, product_name: e.target.value })}
                placeholder="Enter product name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="product_type">Product Type</Label>
              <Select
                value={formData.product_type || ""}
                onValueChange={(value) => setFormData({ ...formData, product_type: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select product type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Gold Chain">Gold Chain</SelectItem>
                  <SelectItem value="Gold Bangles">Gold Bangles</SelectItem>
                  <SelectItem value="Diamond Studs">Diamond Studs</SelectItem>
                  <SelectItem value="Gold Ring">Gold Ring</SelectItem>
                  <SelectItem value="Necklace">Necklace</SelectItem>
                  <SelectItem value="Earrings">Earrings</SelectItem>
                  <SelectItem value="Pendant">Pendant</SelectItem>
                  <SelectItem value="Temple Jewelry">Temple Jewelry</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="with_stone_weight">With Stone Weight (g)</Label>
              <Input
                id="with_stone_weight"
                type="number"
                step="0.001"
                value={formData.with_stone_weight || ""}
                onChange={(e) =>
                  setFormData({ ...formData, with_stone_weight: Number.parseFloat(e.target.value) || 0 })
                }
                placeholder="0.000"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="without_stone_weight">Without Stone Weight (g)</Label>
              <Input
                id="without_stone_weight"
                type="number"
                step="0.001"
                value={formData.without_stone_weight || ""}
                onChange={(e) =>
                  setFormData({ ...formData, without_stone_weight: Number.parseFloat(e.target.value) || 0 })
                }
                placeholder="0.000"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="procured_in_24k">Procured in 24K (g)</Label>
              <Input
                id="procured_in_24k"
                type="number"
                step="0.001"
                value={formData.procured_in_24k || ""}
                onChange={(e) => setFormData({ ...formData, procured_in_24k: Number.parseFloat(e.target.value) || 0 })}
                placeholder="0.000"
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="with_stone_cost">With Stone Cost (%)</Label>
              <Input
                id="with_stone_cost"
                type="number"
                value={formData.with_stone_cost || ""}
                onChange={(e) => setFormData({ ...formData, with_stone_cost: Number.parseFloat(e.target.value) || 0 })}
                placeholder="0"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="without_stone_cost">Without Stone Cost (%)</Label>
              <Input
                id="without_stone_cost"
                type="number"
                value={formData.without_stone_cost || ""}
                onChange={(e) =>
                  setFormData({ ...formData, without_stone_cost: Number.parseFloat(e.target.value) || 0 })
                }
                placeholder="0"
              />
            </div>
          </div>
        </div>
        <div className="flex justify-end gap-2 pt-4 border-t bg-white sticky bottom-0">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Update Item
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
