"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Loader2 } from "lucide-react"
import { useDatabaseMutation } from "@/lib/hooks/useDatabase"
import type { InventoryItem } from "@/lib/models/inventory"

// Metal Information Constants
const METAL_TYPES = ["Gold", "Silver", "Platinum"] as const
const FORM_TYPES = ["Bar", "Jewel", "Old Jewel"] as const
const JEWEL_TYPES = ["With Stone", "Without Stone"] as const
const JEWEL_CATEGORIES = [
  "Bangle", "Ring", "Chain", "<PERSON>lace", "<PERSON><PERSON>", "Pendant",
  "Brace<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ankle<PERSON>", "Others"
] as const

interface EditInventoryDialogProps {
  item: InventoryItem | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export default function EditInventoryDialog({ item, open, onOpenChange, onSuccess }: EditInventoryDialogProps) {
  const [formData, setFormData] = useState<Partial<InventoryItem>>({})
  const { mutate, loading } = useDatabaseMutation<InventoryItem>()

  useEffect(() => {
    if (item) {
      setFormData(item)
    }
  }, [item])

  const handleSubmit = async () => {
    if (!item || !formData.product_name) return

    const result = await mutate(`inventory/${item.id}`, "PUT", {
      product_name: formData.product_name,
      product_type: formData.product_type,
      metal_type: formData.metal_type,
      form_type: formData.form_type,
      jewel_type: formData.jewel_type,
      jewel_category: formData.jewel_category,
      with_stone_weight: formData.with_stone_weight,
      without_stone_weight: formData.without_stone_weight,
      with_stone_cost: formData.with_stone_cost,
      without_stone_cost: formData.without_stone_cost,
      procured_in_24k: formData.procured_in_24k,
    })

    if (result) {
      onOpenChange(false)
      onSuccess()
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Inventory Item</DialogTitle>
          <DialogDescription>Update inventory item details</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto px-1">
          <div className="space-y-2">
            <Label htmlFor="product_name">Product Name</Label>
            <Input
              id="product_name"
              value={formData.product_name || ""}
              onChange={(e) => setFormData({ ...formData, product_name: e.target.value })}
              placeholder="Enter product name (e.g., Gold Chain, Diamond Ring, etc.)"
            />
          </div>

          {/* Metal Information Section */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold mb-4 text-amber-700">Metal Information</h3>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="space-y-2">
                <Label htmlFor="metal_type">Metal Type</Label>
                <Select
                  value={formData.metal_type || "Gold"}
                  onValueChange={(value) => setFormData({ ...formData, metal_type: value as any })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select metal type" />
                  </SelectTrigger>
                  <SelectContent>
                    {METAL_TYPES.map((type) => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="form_type">Form Type</Label>
                <Select
                  value={formData.form_type || "Jewel"}
                  onValueChange={(value) => {
                    setFormData({
                      ...formData,
                      form_type: value as any,
                      jewel_type: value === "Jewel" ? (formData.jewel_type || "Without Stone") : undefined,
                      jewel_category: value === "Jewel" ? formData.jewel_category : undefined
                    })
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select form type" />
                  </SelectTrigger>
                  <SelectContent>
                    {FORM_TYPES.map((type) => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Jewel-specific fields */}
            {formData.form_type === "Jewel" && (
              <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                <h4 className="font-medium mb-3 text-amber-800">Jewel Type Selection</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="jewel_type">Jewel Type</Label>
                    <Select
                      value={formData.jewel_type || "Without Stone"}
                      onValueChange={(value) => setFormData({ ...formData, jewel_type: value as any })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select jewel type" />
                      </SelectTrigger>
                      <SelectContent>
                        {JEWEL_TYPES.map((type) => (
                          <SelectItem key={type} value={type}>{type}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="jewel_category">Jewel Category</Label>
                    <Select
                      value={formData.jewel_category || ""}
                      onValueChange={(value) => setFormData({
                        ...formData,
                        jewel_category: value,
                        product_type: value !== "Others" ? value : formData.product_type
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select jewel category" />
                      </SelectTrigger>
                      <SelectContent>
                        {JEWEL_CATEGORIES.map((category) => (
                          <SelectItem key={category} value={category}>{category}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {formData.jewel_category === "Others" && (
                  <div className="space-y-2">
                    <Label htmlFor="custom_category">Custom Category</Label>
                    <Input
                      id="custom_category"
                      value={formData.product_type || ""}
                      onChange={(e) => setFormData({ ...formData, product_type: e.target.value })}
                      placeholder="Enter custom jewel category"
                    />
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="with_stone_weight">With Stone Weight (g)</Label>
              <Input
                id="with_stone_weight"
                type="number"
                step="0.001"
                value={formData.with_stone_weight || ""}
                onChange={(e) =>
                  setFormData({ ...formData, with_stone_weight: Number.parseFloat(e.target.value) || 0 })
                }
                placeholder="0.000"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="without_stone_weight">Without Stone Weight (g)</Label>
              <Input
                id="without_stone_weight"
                type="number"
                step="0.001"
                value={formData.without_stone_weight || ""}
                onChange={(e) =>
                  setFormData({ ...formData, without_stone_weight: Number.parseFloat(e.target.value) || 0 })
                }
                placeholder="0.000"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="procured_in_24k">Procured in 24K (g)</Label>
              <Input
                id="procured_in_24k"
                type="number"
                step="0.001"
                value={formData.procured_in_24k || ""}
                onChange={(e) => setFormData({ ...formData, procured_in_24k: Number.parseFloat(e.target.value) || 0 })}
                placeholder="0.000"
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="with_stone_cost">With Stone Cost (%)</Label>
              <Input
                id="with_stone_cost"
                type="number"
                value={formData.with_stone_cost || ""}
                onChange={(e) => setFormData({ ...formData, with_stone_cost: Number.parseFloat(e.target.value) || 0 })}
                placeholder="0"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="without_stone_cost">Without Stone Cost (%)</Label>
              <Input
                id="without_stone_cost"
                type="number"
                value={formData.without_stone_cost || ""}
                onChange={(e) =>
                  setFormData({ ...formData, without_stone_cost: Number.parseFloat(e.target.value) || 0 })
                }
                placeholder="0"
              />
            </div>
          </div>
        </div>
        <div className="flex justify-end gap-2 pt-4 border-t bg-white sticky bottom-0">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Update Item
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
