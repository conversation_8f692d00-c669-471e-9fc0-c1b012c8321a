"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertTriangle, RefreshCw } from "lucide-react"

interface ErrorDisplayProps {
  error: string
  onRetry?: () => void
  title?: string
}

export default function ErrorDisplay({ error, onRetry, title = "Error" }: ErrorDisplayProps) {
  return (
    <div className="flex items-center justify-center p-8">
      <div className="max-w-md w-full">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-semibold">{title}</p>
              <p className="text-sm">{error}</p>
              {onRetry && (
                <Button variant="outline" size="sm" onClick={onRetry} className="mt-2 bg-transparent">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              )}
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
