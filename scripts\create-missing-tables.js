#!/usr/bin/env node

require("dotenv").config({ path: ".env.local" })

const mysql = require("mysql2/promise")

async function createMissingTables() {
  console.log("🔧 Creating missing database tables...\n")

  const config = {
    host: process.env.DB_HOST || "localhost",
    port: Number.parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME,
  }

  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(config)

    // Create suppliers table
    console.log("📊 Creating suppliers table...")
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS suppliers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHA<PERSON>(255) NOT NULL,
        location VARCHAR(255),
        contact_person VARCHAR(255),
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        speciality VARCHAR(255),
        total_purchases DECIMAL(15,2) DEFAULT 0,
        last_purchase_date DATE,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_suppliers_name (name),
        INDEX idx_suppliers_status (status)
      )
    `)

    // Create inventory table
    console.log("📦 Creating inventory table...")
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS inventory (
        id INT AUTO_INCREMENT PRIMARY KEY,
        supplier_id INT,
        product_name VARCHAR(255) NOT NULL,
        product_type VARCHAR(255),
        with_stone_weight DECIMAL(10,3) DEFAULT 0,
        without_stone_weight DECIMAL(10,3) DEFAULT 0,
        with_stone_cost DECIMAL(5,2) DEFAULT 0,
        without_stone_cost DECIMAL(5,2) DEFAULT 0,
        procured_in_24k DECIMAL(10,3) DEFAULT 0,
        sold_value_with_stone DECIMAL(10,3) DEFAULT 0,
        sold_value_without_stone DECIMAL(10,3) DEFAULT 0,
        balance_weight_24k DECIMAL(10,3) DEFAULT 0,
        balance_weight_22k DECIMAL(10,3) DEFAULT 0,
        status ENUM('Available', 'Low Stock', 'Out of Stock') DEFAULT 'Available',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
        INDEX idx_inventory_supplier_id (supplier_id),
        INDEX idx_inventory_product_name (product_name),
        INDEX idx_inventory_status (status)
      )
    `)

    // Create gold_rates table
    console.log("💰 Creating gold_rates table...")
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS gold_rates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        rate_24k DECIMAL(10,2) NOT NULL,
        rate_22k DECIMAL(10,2) NOT NULL,
        rate_18k DECIMAL(10,2) NOT NULL,
        rate_date DATE DEFAULT (CURRENT_DATE),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_gold_rates_date (rate_date)
      )
    `)

    // Create users table
    console.log("👤 Creating users table...")
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE,
        full_name VARCHAR(255),
        role ENUM('admin', 'manager', 'user') DEFAULT 'user',
        permissions JSON,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_users_username (username),
        INDEX idx_users_email (email),
        INDEX idx_users_status (status)
      )
    `)

    await connection.end()
    console.log("\n✅ All missing tables created successfully!")
    
  } catch (error) {
    console.log("\n❌ Table creation failed:")
    console.log(`   Error: ${error.message}`)
    process.exit(1)
  }
}

createMissingTables()
