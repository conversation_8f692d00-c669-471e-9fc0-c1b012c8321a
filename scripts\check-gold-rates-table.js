const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkGoldRatesTable() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jewellery_wholesale'
    });

    console.log('Connected to database:', process.env.DB_NAME || 'jewellery_wholesale');

    // Check if gold_rates table exists
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'gold_rates'
    `, [process.env.DB_NAME || 'jewellery_wholesale']);

    if (tables.length === 0) {
      console.log('❌ Gold rates table does not exist');
      
      // Create gold_rates table
      console.log('Creating gold_rates table...');
      await connection.execute(`
        CREATE TABLE gold_rates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            rate_24k DECIMAL(10,2) NOT NULL,
            rate_22k DECIMAL(10,2) NOT NULL,
            rate_18k DECIMAL(10,2) NOT NULL,
            rate_date DATE DEFAULT (CURRENT_DATE),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_gold_rates_date (rate_date)
        )
      `);
      console.log('✅ Gold rates table created successfully');
      
      // Add sample gold rates
      console.log('Adding sample gold rates...');
      const today = new Date().toISOString().split('T')[0];
      await connection.execute(`
        INSERT INTO gold_rates (rate_24k, rate_22k, rate_18k, rate_date)
        VALUES (?, ?, ?, ?)
      `, [7200.00, 6595.00, 5400.00, today]);
      console.log('✅ Sample gold rates added');
      
    } else {
      console.log('✅ Gold rates table exists');
      
      // Check if there are any rates
      const [rates] = await connection.execute('SELECT COUNT(*) as count FROM gold_rates');
      console.log(`Found ${rates[0].count} gold rate records`);
      
      if (rates[0].count === 0) {
        console.log('Adding sample gold rates...');
        const today = new Date().toISOString().split('T')[0];
        await connection.execute(`
          INSERT INTO gold_rates (rate_24k, rate_22k, rate_18k, rate_date)
          VALUES (?, ?, ?, ?)
        `, [7200.00, 6595.00, 5400.00, today]);
        console.log('✅ Sample gold rates added');
      }
    }

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
checkGoldRatesTable();
