#!/usr/bin/env node

require("dotenv").config({ path: ".env.local" })

const mysql = require("mysql2/promise")

async function clearData() {
  console.log("🧹 Clearing all data from database tables...\n")

  const config = {
    host: process.env.DB_HOST || "localhost",
    port: Number.parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME,
  }

  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(config)

    // Disable foreign key checks temporarily
    await connection.execute("SET FOREIGN_KEY_CHECKS = 0")

    // Clear all tables
    console.log("🗑️  Clearing bills...")
    await connection.execute("DELETE FROM bills")
    
    console.log("🗑️  Clearing inventory...")
    await connection.execute("DELETE FROM inventory")
    
    console.log("🗑️  Clearing suppliers...")
    await connection.execute("DELETE FROM suppliers")
    
    console.log("🗑️  Clearing customers...")
    await connection.execute("DELETE FROM customers")
    
    console.log("🗑️  Clearing gold_rates...")
    await connection.execute("DELETE FROM gold_rates")
    
    console.log("🗑️  Clearing users...")
    await connection.execute("DELETE FROM users")

    // Reset auto increment counters
    await connection.execute("ALTER TABLE bills AUTO_INCREMENT = 1")
    await connection.execute("ALTER TABLE inventory AUTO_INCREMENT = 1")
    await connection.execute("ALTER TABLE suppliers AUTO_INCREMENT = 1")
    await connection.execute("ALTER TABLE customers AUTO_INCREMENT = 1")
    await connection.execute("ALTER TABLE gold_rates AUTO_INCREMENT = 1")
    await connection.execute("ALTER TABLE users AUTO_INCREMENT = 1")

    // Re-enable foreign key checks
    await connection.execute("SET FOREIGN_KEY_CHECKS = 1")

    await connection.end()
    console.log("\n✅ All data cleared successfully!")
    
  } catch (error) {
    console.log("\n❌ Data clearing failed:")
    console.log(`   Error: ${error.message}`)
    process.exit(1)
  }
}

clearData()
