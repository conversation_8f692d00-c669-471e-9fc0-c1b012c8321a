import { executeQuery, executeSing<PERSON>, executeUpdate } from "../database"

export interface Setting {
  id: number
  category: string
  setting_key: string
  setting_value: any
  description: string
  data_type: "string" | "number" | "boolean" | "object" | "array"
  is_system: boolean
  is_user_configurable: boolean
  created_at: string
  updated_at: string
  created_by: string
  updated_by: string
}

export interface UserSetting {
  id: number
  user_id: number
  category: string
  setting_key: string
  setting_value: any
  created_at: string
  updated_at: string
}

export interface BusinessSettings {
  // Purity Conversion Factors
  conversion_24k_to_22k: number
  conversion_24k_to_18k: number
  conversion_22k_to_24k: number
  conversion_18k_to_24k: number
  
  // Default Wastage Rates (%)
  wastage_rate_bar: number
  wastage_rate_jewel: number
  wastage_rate_old_jewel: number
  
  // Business Rules
  auto_calculate_balances: boolean
  track_stone_separately: boolean
  enable_wastage_alerts: boolean
  low_stock_threshold: number
  
  // Pricing Rules
  default_making_charges: number
  stone_pricing_method: "weight" | "value" | "both"
  apply_gst: boolean
  gst_rate: number
  
  // Calculation Methods
  rounding_precision: number
  balance_calculation_method: "simple" | "weighted_average" | "fifo"
  
  // Alert Thresholds
  high_wastage_threshold: number
  price_variance_threshold: number
}

export class SettingsModel {
  // Helper method to safely parse setting values
  private static parseSettingValue(value: any): any {
    if (typeof value !== 'string') {
      return value
    }

    // Try to parse as JSON, but handle plain strings gracefully
    try {
      return JSON.parse(value)
    } catch (error) {
      // If it's not valid JSON, return the string as-is
      // This handles cases where values are stored as plain strings like "both"
      return value
    }
  }

  // Get all settings by category
  static async getByCategory(category: string): Promise<Setting[]> {
    const query = `
      SELECT * FROM settings 
      WHERE category = ? 
      ORDER BY setting_key
    `
    const results = await executeQuery<any>(query, [category])
    
    return results.map(row => ({
      ...row,
      setting_value: this.parseSettingValue(row.setting_value)
    }))
  }

  // Get single setting value
  static async getValue(category: string, key: string, userId?: number): Promise<any> {
    // First check for user-specific setting
    if (userId) {
      const userQuery = `
        SELECT setting_value FROM user_settings 
        WHERE user_id = ? AND category = ? AND setting_key = ?
      `
      const userResult = await executeSingle<any>(userQuery, [userId, category, key])
      if (userResult) {
        return this.parseSettingValue(userResult.setting_value)
      }
    }

    // Fall back to system setting
    const query = `
      SELECT setting_value FROM settings 
      WHERE category = ? AND setting_key = ?
    `
    const result = await executeSingle<any>(query, [category, key])
    if (result) {
      return this.parseSettingValue(result.setting_value)
    }
    
    return null
  }

  // Update setting value
  static async updateValue(
    category: string, 
    key: string, 
    value: any, 
    updatedBy: string = 'system'
  ): Promise<boolean> {
    const query = `
      UPDATE settings 
      SET setting_value = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP
      WHERE category = ? AND setting_key = ? AND is_user_configurable = TRUE
    `
    const result = await executeUpdate(query, [
      JSON.stringify(value), 
      updatedBy, 
      category, 
      key
    ])
    
    return result.affectedRows > 0
  }

  // Set user-specific setting
  static async setUserValue(
    userId: number,
    category: string,
    key: string,
    value: any
  ): Promise<boolean> {
    const query = `
      INSERT INTO user_settings (user_id, category, setting_key, setting_value)
      VALUES (?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
      setting_value = VALUES(setting_value),
      updated_at = CURRENT_TIMESTAMP
    `
    const result = await executeUpdate(query, [
      userId,
      category,
      key,
      JSON.stringify(value)
    ])
    
    return result.affectedRows > 0
  }

  // Get all business settings (with user overrides if provided)
  static async getBusinessSettings(userId?: number): Promise<BusinessSettings> {
    const categories = ['conversion', 'wastage', 'business', 'pricing', 'calculation', 'alerts']
    const settings: any = {}

    for (const category of categories) {
      const categorySettings = await this.getByCategory(category)
      for (const setting of categorySettings) {
        // Check for user override
        const value = await this.getValue(category, setting.setting_key, userId)
        settings[setting.setting_key] = value !== null ? value : setting.setting_value
      }
    }

    return settings as BusinessSettings
  }

  // Update multiple business settings
  static async updateBusinessSettings(
    newSettings: Partial<BusinessSettings>,
    updatedBy: string = 'system',
    userId?: number
  ): Promise<boolean> {
    try {
      for (const [key, value] of Object.entries(newSettings)) {
        // Determine category based on key
        let category = 'business'
        if (key.startsWith('conversion_')) category = 'conversion'
        else if (key.startsWith('wastage_rate_')) category = 'wastage'
        else if (key.includes('making_charges') || key.includes('gst') || key.includes('pricing')) category = 'pricing'
        else if (key.includes('rounding') || key.includes('calculation')) category = 'calculation'
        else if (key.includes('threshold')) category = 'alerts'

        if (userId) {
          // Set user-specific setting
          await this.setUserValue(userId, category, key, value)
        } else {
          // Update system setting
          await this.updateValue(category, key, value, updatedBy)
        }
      }
      return true
    } catch (error) {
      console.error('Error updating business settings:', error)
      return false
    }
  }

  // Get all settings (for admin interface)
  static async getAllSettings(): Promise<Setting[]> {
    const query = `
      SELECT * FROM settings 
      ORDER BY category, setting_key
    `
    const results = await executeQuery<any>(query, [])
    
    return results.map(row => ({
      ...row,
      setting_value: typeof row.setting_value === 'string' 
        ? JSON.parse(row.setting_value) 
        : row.setting_value
    }))
  }

  // Get user settings
  static async getUserSettings(userId: number): Promise<UserSetting[]> {
    const query = `
      SELECT * FROM user_settings 
      WHERE user_id = ?
      ORDER BY category, setting_key
    `
    const results = await executeQuery<any>(query, [userId])
    
    return results.map(row => ({
      ...row,
      setting_value: typeof row.setting_value === 'string' 
        ? JSON.parse(row.setting_value) 
        : row.setting_value
    }))
  }

  // Delete user setting (revert to system default)
  static async deleteUserSetting(userId: number, category: string, key: string): Promise<boolean> {
    const query = `
      DELETE FROM user_settings 
      WHERE user_id = ? AND category = ? AND setting_key = ?
    `
    const result = await executeUpdate(query, [userId, category, key])
    return result.affectedRows > 0
  }

  // Create new setting
  static async createSetting(
    category: string,
    key: string,
    value: any,
    description: string,
    dataType: "string" | "number" | "boolean" | "object" | "array",
    isSystem: boolean = false,
    isUserConfigurable: boolean = true,
    createdBy: string = 'system'
  ): Promise<number> {
    const query = `
      INSERT INTO settings (
        category, setting_key, setting_value, description, data_type, 
        is_system, is_user_configurable, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `
    const result = await executeUpdate(query, [
      category,
      key,
      JSON.stringify(value),
      description,
      dataType,
      isSystem,
      isUserConfigurable,
      createdBy
    ])
    
    return result.insertId!
  }

  // Helper method to get wastage rate
  static async getWastageRate(formType: string, userId?: number): Promise<number> {
    const key = `wastage_rate_${formType.toLowerCase().replace(' ', '_')}`
    const rate = await this.getValue('wastage', key, userId)
    
    // Default rates if not found
    const defaults: { [key: string]: number } = {
      'wastage_rate_bar': 0.5,
      'wastage_rate_jewel': 2.0,
      'wastage_rate_old_jewel': 3.0
    }
    
    return rate !== null ? rate : (defaults[key] || 2.0)
  }

  // Helper method to get conversion factor
  static async getConversionFactor(fromPurity: string, toPurity: string, userId?: number): Promise<number> {
    const key = `conversion_${fromPurity.toLowerCase()}_to_${toPurity.toLowerCase()}`
    const factor = await this.getValue('conversion', key, userId)
    
    // Default conversion factors
    const defaults: { [key: string]: number } = {
      'conversion_24k_to_22k': 0.916,
      'conversion_24k_to_18k': 0.750,
      'conversion_22k_to_24k': 1.092,
      'conversion_18k_to_24k': 1.333
    }
    
    return factor !== null ? factor : (defaults[key] || 1.0)
  }
}
