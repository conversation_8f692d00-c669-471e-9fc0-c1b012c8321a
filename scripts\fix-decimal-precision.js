const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function fixDecimalPrecision() {
  console.log('🔧 FIXING DECIMAL PRECISION');
  console.log('===========================\n');

  let connection;
  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jewellery_wholesale_software'
  };

  try {
    connection = await mysql.createConnection(config);
    console.log(`📊 Connected to database: ${config.database}`);

    // Check current decimal columns and their precision
    const [decimalColumns] = await connection.execute(`
      SELECT COLUMN_NAME, COLUMN_TYPE, NUMERIC_PRECISION, NUMERIC_SCALE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory' 
      AND DATA_TYPE = 'decimal'
    `, [config.database]);

    console.log('\n📋 CURRENT DECIMAL COLUMNS:');
    decimalColumns.forEach(col => {
      console.log(`   ${col.COLUMN_NAME}: ${col.COLUMN_TYPE} (precision: ${col.NUMERIC_PRECISION}, scale: ${col.NUMERIC_SCALE})`);
    });

    // Columns that need larger precision for cost values
    const columnsToFix = [
      { name: 'with_stone_cost', newType: 'DECIMAL(12,2)' },
      { name: 'without_stone_cost', newType: 'DECIMAL(12,2)' },
      { name: 'making_charges', newType: 'DECIMAL(12,2)' }
    ];

    console.log('\n🔧 UPDATING DECIMAL PRECISION:');
    
    for (const col of columnsToFix) {
      try {
        const alterQuery = `ALTER TABLE inventory MODIFY COLUMN ${col.name} ${col.newType} DEFAULT NULL`;
        console.log(`   Updating: ${col.name} to ${col.newType}`);
        
        await connection.execute(alterQuery);
        console.log(`   ✅ Successfully updated ${col.name}`);
      } catch (error) {
        console.log(`   ❌ Failed to update ${col.name}: ${error.message}`);
      }
    }

    // Test with realistic values
    console.log('\n🧪 TESTING WITH REALISTIC VALUES:');
    
    try {
      const testQuery = `
        INSERT INTO inventory (
          supplier_id, product_name, product_type, metal_type, form_type,
          jewel_type, with_stone_weight, without_stone_weight,
          with_stone_cost, without_stone_cost, with_stone_tunch_percentage,
          without_stone_tunch_percentage, procured_in_24k, balance_weight_24k, balance_weight_22k
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      // Use realistic values
      const testParams = [
        1, // supplier_id
        'Test Item Decimal Fix', // product_name
        'Ring', // product_type
        'Gold', // metal_type
        'Jewel', // form_type
        'With Stone', // jewel_type (valid enum value)
        10.5, // with_stone_weight
        9.8, // without_stone_weight
        75000.50, // with_stone_cost (realistic jewelry cost)
        65000.25, // without_stone_cost
        95.5, // with_stone_tunch_percentage
        96.2, // without_stone_tunch_percentage
        9.5, // procured_in_24k
        9.5, // balance_weight_24k
        8.7 // balance_weight_22k
      ];
      
      const [result] = await connection.execute(testQuery, testParams);
      console.log('✅ Test insert with fixed decimals successful!');
      console.log(`   Inserted ID: ${result.insertId}`);
      
      // Verify the inserted data
      const [inserted] = await connection.execute(
        'SELECT with_stone_cost, without_stone_cost FROM inventory WHERE id = ?', 
        [result.insertId]
      );
      console.log(`   Verified costs: with_stone=${inserted[0].with_stone_cost}, without_stone=${inserted[0].without_stone_cost}`);
      
      // Clean up test record
      await connection.execute('DELETE FROM inventory WHERE id = ?', [result.insertId]);
      console.log('✅ Test record cleaned up');
      
    } catch (error) {
      console.log(`❌ Test insert failed: ${error.message}`);
    }

    // Show updated schema
    console.log('\n📋 UPDATED DECIMAL COLUMNS:');
    const [updatedColumns] = await connection.execute(`
      SELECT COLUMN_NAME, COLUMN_TYPE, NUMERIC_PRECISION, NUMERIC_SCALE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory' 
      AND DATA_TYPE = 'decimal'
      ORDER BY COLUMN_NAME
    `, [config.database]);

    updatedColumns.forEach(col => {
      console.log(`   ${col.COLUMN_NAME}: ${col.COLUMN_TYPE} (precision: ${col.NUMERIC_PRECISION}, scale: ${col.NUMERIC_SCALE})`);
    });

    console.log('\n🎯 DECIMAL PRECISION FIX COMPLETE');
    console.log('=================================');
    console.log('✅ Cost columns can now handle larger values');
    console.log('✅ Inventory model should work correctly');
    console.log('✅ Try adding an inventory item again');

    console.log('\n📋 FINAL STATUS:');
    console.log('• Missing columns: ✅ Added');
    console.log('• Enum values: ✅ Verified');
    console.log('• Decimal precision: ✅ Fixed');
    console.log('• Database schema: ✅ Ready');

  } catch (error) {
    console.error('❌ Decimal precision fix failed:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the decimal precision fix
fixDecimalPrecision();
