-- Complete MySQL Database Schema for Jewellery Wholesale Management System
-- Updated schema based on current project requirements
-- Database name will be taken from .env.local (DB_NAME environment variable)

-- Note: Run this after setting up your .env.local file with the correct DB_NAME

-- Drop tables if they exist (for clean setup) - in correct order due to foreign keys
DROP TABLE IF EXISTS sales_transactions;
DROP TABLE IF EXISTS wastage_records;
DROP TABLE IF EXISTS bills;
DROP TABLE IF EXISTS inventory;
DROP TABLE IF EXISTS customers;
DROP TABLE IF EXISTS suppliers;
DROP TABLE IF EXISTS gold_rates;
DROP TABLE IF EXISTS users;

-- =====================================================
-- SUPPLIERS TABLE
-- =====================================================
CREATE TABLE suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHA<PERSON>(255) NOT NULL,
    location VARCHAR(255),
    contact_person VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    speciality VARCHAR(255),
    total_purchases DECIMAL(15,2) DEFAULT 0.00,
    last_purchase_date DATE,
    status ENUM('Active', 'Inactive') DEFAULT 'Active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_suppliers_name (name),
    INDEX idx_suppliers_status (status),
    INDEX idx_suppliers_location (location)
);

-- =====================================================
-- CUSTOMERS TABLE
-- =====================================================
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    contact_person VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    total_purchases DECIMAL(15,2) DEFAULT 0.00,
    last_purchase_date DATE,
    status ENUM('Active', 'Inactive') DEFAULT 'Active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_customers_name (name),
    INDEX idx_customers_status (status),
    INDEX idx_customers_location (location)
);

-- =====================================================
-- INVENTORY TABLE (Enhanced with all current fields)
-- =====================================================
CREATE TABLE inventory (
    id INT AUTO_INCREMENT PRIMARY KEY,
    supplier_id INT,
    product_name VARCHAR(255) NOT NULL,
    product_type VARCHAR(255),
    
    -- Metal and form information
    metal_type ENUM('Gold', 'Silver', 'Platinum') DEFAULT 'Gold',
    form_type ENUM('Bar', 'Jewel', 'Old Jewel') DEFAULT 'Jewel',
    jewel_type ENUM('With Stone', 'Without Stone') NULL,
    jewel_category VARCHAR(100) NULL,
    
    -- Weight information
    with_stone_weight DECIMAL(10,3) DEFAULT 0.000,
    without_stone_weight DECIMAL(10,3) DEFAULT 0.000,
    stone_weight DECIMAL(10,3) DEFAULT 0.000,
    
    -- Cost information
    with_stone_cost DECIMAL(10,2) DEFAULT 0.00,
    without_stone_cost DECIMAL(10,2) DEFAULT 0.00,
    
    -- Procurement and processing
    procured_in_24k DECIMAL(10,3) DEFAULT 0.000,
    wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
    expected_processing_loss DECIMAL(10,3) DEFAULT 0.000,
    
    -- Sales tracking
    sold_value_with_stone DECIMAL(10,3) DEFAULT 0.000,
    sold_value_without_stone DECIMAL(10,3) DEFAULT 0.000,
    sold_value_24k DECIMAL(10,3) DEFAULT 0.000,
    sold_value_22k DECIMAL(10,3) DEFAULT 0.000,
    sold_value_18k DECIMAL(10,3) DEFAULT 0.000,
    
    -- Balance tracking
    balance_weight_24k DECIMAL(10,3) DEFAULT 0.000,
    balance_weight_22k DECIMAL(10,3) DEFAULT 0.000,
    
    -- Making charges
    making_charges DECIMAL(10,2) DEFAULT 0.00,
    
    -- Status and timestamps
    status ENUM('Available', 'Low Stock', 'Out of Stock') DEFAULT 'Available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
    
    -- Indexes for performance
    INDEX idx_inventory_supplier_id (supplier_id),
    INDEX idx_inventory_product_name (product_name),
    INDEX idx_inventory_status (status),
    INDEX idx_inventory_metal_type (metal_type),
    INDEX idx_inventory_form_type (form_type),
    INDEX idx_inventory_jewel_type (jewel_type)
);

-- =====================================================
-- BILLS TABLE (Enhanced)
-- =====================================================
CREATE TABLE bills (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT,
    inventory_item_id INT,
    customer_name VARCHAR(255),
    customer_location VARCHAR(255),
    bill_number VARCHAR(50) UNIQUE NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_type VARCHAR(255),
    
    -- Weight details
    with_stone DECIMAL(10,3) DEFAULT 0.000,
    without_stone DECIMAL(10,3) DEFAULT 0.000,
    gross_weight DECIMAL(10,3) DEFAULT 0.000,
    stone_weight DECIMAL(10,3) DEFAULT 0.000,
    net_weight DECIMAL(10,3) DEFAULT 0.000,
    
    -- Purity details
    tunch_with_stone INT DEFAULT 0,
    tunch_without_stone INT DEFAULT 0,
    weight_in_24k DECIMAL(10,3) DEFAULT 0.000,
    
    -- Pricing details
    gold_24k_price DECIMAL(10,2) DEFAULT 0.00,
    stone_price DECIMAL(10,2) DEFAULT 0.00,
    making_charges DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(15,2) DEFAULT 0.00,
    
    -- Bill details
    status ENUM('Pending', 'Paid', 'Cancelled') DEFAULT 'Pending',
    bill_date DATE DEFAULT (CURRENT_DATE),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (inventory_item_id) REFERENCES inventory(id) ON DELETE SET NULL,
    
    -- Indexes for performance
    INDEX idx_bills_customer_id (customer_id),
    INDEX idx_bills_inventory_item_id (inventory_item_id),
    INDEX idx_bills_bill_number (bill_number),
    INDEX idx_bills_bill_date (bill_date),
    INDEX idx_bills_status (status)
);

-- =====================================================
-- GOLD RATES TABLE
-- =====================================================
CREATE TABLE gold_rates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rate_24k DECIMAL(10,2) NOT NULL,
    rate_22k DECIMAL(10,2) NOT NULL,
    rate_18k DECIMAL(10,2) NOT NULL,
    rate_date DATE DEFAULT (CURRENT_DATE),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_gold_rates_date (rate_date),
    UNIQUE KEY unique_rate_date (rate_date)
);

-- =====================================================
-- USERS TABLE (Enhanced with permissions)
-- =====================================================
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    role ENUM('Admin', 'Manager', 'Staff', 'Viewer') DEFAULT 'Staff',
    status ENUM('Active', 'Inactive') DEFAULT 'Active',
    last_login TIMESTAMP NULL,
    permissions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_users_email (email),
    INDEX idx_users_role (role),
    INDEX idx_users_status (status)
);

-- =====================================================
-- SALES TRANSACTIONS TABLE (For detailed sales tracking)
-- =====================================================
CREATE TABLE sales_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    inventory_id INT NOT NULL,
    customer_id INT,
    transaction_type ENUM('Sale', 'Return', 'Exchange', 'Wastage') DEFAULT 'Sale',
    
    -- Weight sold in different purities
    weight_24k DECIMAL(10,3) DEFAULT 0.000,
    weight_22k DECIMAL(10,3) DEFAULT 0.000,
    weight_18k DECIMAL(10,3) DEFAULT 0.000,
    
    -- Stone details if applicable
    stone_weight DECIMAL(10,3) DEFAULT 0.000,
    stone_value DECIMAL(10,2) DEFAULT 0.00,
    
    -- Pricing details
    rate_24k DECIMAL(10,2) DEFAULT 0.00,
    rate_22k DECIMAL(10,2) DEFAULT 0.00,
    making_charges DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(15,2) DEFAULT 0.00,
    
    -- Transaction details
    transaction_date DATE DEFAULT (CURRENT_DATE),
    bill_number VARCHAR(50),
    notes TEXT,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    
    -- Foreign keys
    FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    
    -- Indexes for performance
    INDEX idx_sales_inventory_id (inventory_id),
    INDEX idx_sales_customer_id (customer_id),
    INDEX idx_sales_transaction_date (transaction_date),
    INDEX idx_sales_transaction_type (transaction_type),
    INDEX idx_sales_bill_number (bill_number)
);

-- =====================================================
-- WASTAGE RECORDS TABLE (For wastage tracking)
-- =====================================================
CREATE TABLE wastage_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    inventory_id INT NOT NULL,
    wastage_type ENUM('Processing', 'Manufacturing', 'Refining', 'Loss', 'Other') DEFAULT 'Processing',
    
    -- Wastage amounts
    weight_24k DECIMAL(10,3) DEFAULT 0.000,
    weight_22k DECIMAL(10,3) DEFAULT 0.000,
    weight_18k DECIMAL(10,3) DEFAULT 0.000,
    
    -- Wastage details
    wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
    reason TEXT,
    process_stage VARCHAR(100),
    
    -- Recovery details (if any metal recovered)
    recovered_weight DECIMAL(10,3) DEFAULT 0.000,
    recovered_purity ENUM('24K', '22K', '18K', 'Mixed') DEFAULT '24K',
    
    -- Record details
    recorded_date DATE DEFAULT (CURRENT_DATE),
    recorded_by VARCHAR(100),
    notes TEXT,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE,
    
    -- Indexes for performance
    INDEX idx_wastage_inventory_id (inventory_id),
    INDEX idx_wastage_type (wastage_type),
    INDEX idx_wastage_date (recorded_date)
);

-- =====================================================
-- INSERT SAMPLE DATA
-- =====================================================

-- Sample gold rates
INSERT INTO gold_rates (rate_24k, rate_22k, rate_18k, rate_date) VALUES 
(7200.00, 6595.00, 5400.00, CURDATE());

-- Sample admin user (password should be hashed in real implementation)
INSERT INTO users (name, email, role, permissions) VALUES 
('System Admin', '<EMAIL>', 'Admin', 
'{"inventory": true, "billing": true, "reports": true, "suppliers": true, "goldRates": true, "backup": true}');

-- =====================================================
-- SCHEMA COMPLETE
-- =====================================================
-- This schema includes all tables and fields currently used in the application
-- Run this script after setting up your .env.local file with the correct database name
