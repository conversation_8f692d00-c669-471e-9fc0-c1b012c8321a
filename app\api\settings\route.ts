import { type NextRequest, NextResponse } from "next/server"
import { SettingsModel } from "@/lib/models/settings"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get("category")
    const key = searchParams.get("key")
    const userId = searchParams.get("user_id")
    const business = searchParams.get("business")

    // Get business settings (most common use case)
    if (business === "true") {
      const settings = await SettingsModel.getBusinessSettings(
        userId ? parseInt(userId) : undefined
      )
      return NextResponse.json({ success: true, data: settings })
    }

    // Get specific setting value
    if (category && key) {
      const value = await SettingsModel.getValue(
        category, 
        key, 
        userId ? parseInt(userId) : undefined
      )
      return NextResponse.json({ success: true, data: { value } })
    }

    // Get settings by category
    if (category) {
      const settings = await SettingsModel.getByCategory(category)
      return NextResponse.json({ success: true, data: settings })
    }

    // Get all settings (admin only)
    const settings = await SettingsModel.getAllSettings()
    return NextResponse.json({ success: true, data: settings })

  } catch (error) {
    console.error("Error fetching settings:", error)
    return NextResponse.json(
      { success: false, error: "Failed to fetch settings" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    const { category, key, value, description, dataType, isSystem, isUserConfigurable, createdBy } = data

    // Validate required fields
    if (!category || !key || value === undefined) {
      return NextResponse.json(
        { success: false, error: "Category, key, and value are required" },
        { status: 400 }
      )
    }

    const settingId = await SettingsModel.createSetting(
      category,
      key,
      value,
      description || "",
      dataType || "string",
      isSystem || false,
      isUserConfigurable !== false, // Default to true
      createdBy || "system"
    )

    return NextResponse.json(
      { success: true, data: { id: settingId } },
      { status: 201 }
    )

  } catch (error) {
    console.error("Error creating setting:", error)
    return NextResponse.json(
      { success: false, error: "Failed to create setting" },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const data = await request.json()
    const { category, key, value, updatedBy, userId, settings } = data

    // Bulk update business settings
    if (settings) {
      const success = await SettingsModel.updateBusinessSettings(
        settings,
        updatedBy || "system",
        userId
      )

      if (success) {
        return NextResponse.json({ success: true, message: "Settings updated successfully" })
      } else {
        return NextResponse.json(
          { success: false, error: "Failed to update some settings" },
          { status: 500 }
        )
      }
    }

    // Single setting update
    if (!category || !key || value === undefined) {
      return NextResponse.json(
        { success: false, error: "Category, key, and value are required" },
        { status: 400 }
      )
    }

    let success: boolean

    if (userId) {
      // Update user-specific setting
      success = await SettingsModel.setUserValue(userId, category, key, value)
    } else {
      // Update system setting
      success = await SettingsModel.updateValue(category, key, value, updatedBy || "system")
    }

    if (success) {
      return NextResponse.json({ success: true, message: "Setting updated successfully" })
    } else {
      return NextResponse.json(
        { success: false, error: "Setting not found or not configurable" },
        { status: 404 }
      )
    }

  } catch (error) {
    console.error("Error updating setting:", error)
    return NextResponse.json(
      { success: false, error: "Failed to update setting" },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get("category")
    const key = searchParams.get("key")
    const userId = searchParams.get("user_id")

    if (!category || !key) {
      return NextResponse.json(
        { success: false, error: "Category and key are required" },
        { status: 400 }
      )
    }

    if (!userId) {
      return NextResponse.json(
        { success: false, error: "User ID is required for deleting user settings" },
        { status: 400 }
      )
    }

    const success = await SettingsModel.deleteUserSetting(
      parseInt(userId),
      category,
      key
    )

    if (success) {
      return NextResponse.json({ 
        success: true, 
        message: "User setting deleted, reverted to system default" 
      })
    } else {
      return NextResponse.json(
        { success: false, error: "User setting not found" },
        { status: 404 }
      )
    }

  } catch (error) {
    console.error("Error deleting user setting:", error)
    return NextResponse.json(
      { success: false, error: "Failed to delete user setting" },
      { status: 500 }
    )
  }
}
