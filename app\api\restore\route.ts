import { type NextRequest, NextResponse } from "next/server"
import { executeSingle } from "@/lib/database"

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get("backup") as File

    if (!file) {
      return NextResponse.json({ success: false, error: "No backup file provided" }, { status: 400 })
    }

    const sqlContent = await file.text()

    // Split SQL content into individual statements
    const statements = sqlContent
      .split(";")
      .map((stmt) => stmt.trim())
      .filter((stmt) => stmt.length > 0 && !stmt.startsWith("--"))

    // Execute each statement
    for (const statement of statements) {
      if (statement.trim()) {
        await executeSingle(statement)
      }
    }

    return NextResponse.json({ success: true, message: "Database restored successfully" })
  } catch (error) {
    console.error("Restore error:", error)
    return NextResponse.json({ success: false, error: "<PERSON><PERSON> failed" }, { status: 500 })
  }
}
