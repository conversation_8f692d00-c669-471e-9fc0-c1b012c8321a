#!/usr/bin/env node

require("dotenv").config({ path: ".env.local" })
const mysql = require("mysql2/promise")
const fs = require("fs")
const path = require("path")

async function createDatabase() {
  console.log("🗄️  Creating database and tables...\n")

  const config = {
    host: process.env.DB_HOST || "localhost",
    port: Number.parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
  }

  const dbName = process.env.DB_NAME

  console.log("📋 Connection Details:")
  console.log(`   Host: ${config.host}`)
  console.log(`   Port: ${config.port}`)
  console.log(`   User: ${config.user}`)
  console.log(`   Database: ${dbName}`)
  console.log("")

  try {
    // Connect to MySQL server (without database)
    console.log("🔌 Connecting to MySQL server...")
    const connection = await mysql.createConnection(config)
    console.log("✅ Connected to MySQL server")

    // Create database if it doesn't exist
    console.log(`🏗️  Creating database '${dbName}'...`)
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbName}\``)
    console.log(`✅ Database '${dbName}' created/verified`)

    // Close connection and reconnect to the specific database
    await connection.end()

    // Connect to the specific database
    console.log("📊 Creating tables...")
    const dbConnection = await mysql.createConnection({
      ...config,
      database: dbName
    })

    // Read and execute the schema file
    const schemaPath = path.join(__dirname, "mysql-schema.sql")
    const schemaSQL = fs.readFileSync(schemaPath, "utf8")

    // Split SQL statements and execute them one by one
    const statements = schemaSQL
      .split(";")
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith("--") && !stmt.startsWith("CREATE DATABASE") && !stmt.startsWith("USE"))

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await dbConnection.execute(statement)
        } catch (error) {
          if (!error.message.includes("already exists")) {
            console.log(`⚠️  Warning executing: ${statement.substring(0, 50)}...`)
            console.log(`   Error: ${error.message}`)
          }
        }
      }
    }

    // Verify tables were created
    console.log("🔍 Verifying tables...")
    const [tables] = await dbConnection.execute("SHOW TABLES")

    if (tables.length > 0) {
      console.log(`✅ Created ${tables.length} tables:`)
      tables.forEach((table) => {
        const tableName = Object.values(table)[0]
        console.log(`   - ${tableName}`)
      })
    } else {
      console.log("⚠️  No tables found")
    }

    await dbConnection.end()
    console.log("\n🎉 Database setup completed successfully!")
    
  } catch (error) {
    console.log("\n❌ Database setup failed:")
    console.log(`   Error: ${error.message}`)
    
    if (error.code === "ER_ACCESS_DENIED_ERROR") {
      console.log("\n💡 Troubleshooting:")
      console.log("   - Check DB_USER and DB_PASSWORD in .env.local")
      console.log("   - Ensure MySQL user has proper privileges")
    } else if (error.code === "ECONNREFUSED") {
      console.log("\n💡 Troubleshooting:")
      console.log("   - Check if MySQL server is running")
      console.log("   - Verify DB_HOST and DB_PORT in .env.local")
    }
    
    process.exit(1)
  }
}

createDatabase()
