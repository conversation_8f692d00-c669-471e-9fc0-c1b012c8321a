"use client"

import { useState, useCallback } from "react"

export interface ToastMessage {
  id: string
  title: string
  description?: string
  variant?: "default" | "destructive"
  duration?: number
}

let toastCounter = 0
const generateId = () => `toast-${++toastCounter}`

// Global toast state
let globalToasts: ToastMessage[] = []
let listeners: Array<(toasts: ToastMessage[]) => void> = []

const notifyListeners = () => {
  listeners.forEach(listener => listener([...globalToasts]))
}

const addToast = (toast: Omit<ToastMessage, "id">) => {
  const newToast: ToastMessage = {
    ...toast,
    id: generateId(),
    duration: toast.duration || 5000
  }
  
  globalToasts = [newToast, ...globalToasts].slice(0, 3) // Keep only 3 toasts
  notifyListeners()
  
  // Auto remove after duration
  setTimeout(() => {
    removeToast(newToast.id)
  }, newToast.duration)
  
  return newToast.id
}

const removeToast = (id: string) => {
  globalToasts = globalToasts.filter(toast => toast.id !== id)
  notifyListeners()
}

export const useSimpleToast = () => {
  const [toasts, setToasts] = useState<ToastMessage[]>(globalToasts)
  
  // Subscribe to global toast changes
  useState(() => {
    listeners.push(setToasts)
    return () => {
      const index = listeners.indexOf(setToasts)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  })
  
  const toast = useCallback((message: Omit<ToastMessage, "id">) => {
    return addToast(message)
  }, [])
  
  const dismiss = useCallback((id: string) => {
    removeToast(id)
  }, [])
  
  return {
    toasts,
    toast,
    dismiss
  }
}

// Export a simple toast function for direct use
export const simpleToast = (message: Omit<ToastMessage, "id">) => {
  return addToast(message)
}
