"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Download, Upload, Database, Shield, Clock, CheckCircle, AlertTriangle, RefreshCw } from "lucide-react"

interface BackupFile {
  id: string
  name: string
  date: string
  size: string
  type: "Full" | "Partial"
  status: "Success" | "Failed" | "In Progress"
}

export default function BackupRestore() {
  const [isBackingUp, setIsBackingUp] = useState(false)
  const [isRestoring, setIsRestoring] = useState(false)
  const [backupProgress, setBackupProgress] = useState(0)
  const [restoreProgress, setRestoreProgress] = useState(0)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  const [backupHistory, setBackupHistory] = useState<BackupFile[]>([
    {
      id: "1",
      name: "jewellery_backup_2024_01_29.sql",
      date: "2024-01-29 10:30 AM",
      size: "2.5 MB",
      type: "Full",
      status: "Success",
    },
    {
      id: "2",
      name: "jewellery_backup_2024_01_28.sql",
      date: "2024-01-28 10:30 AM",
      size: "2.3 MB",
      type: "Full",
      status: "Success",
    },
    {
      id: "3",
      name: "jewellery_backup_2024_01_27.sql",
      date: "2024-01-27 10:30 AM",
      size: "2.1 MB",
      type: "Full",
      status: "Success",
    },
  ])

  const handleBackup = async (type: "full" | "partial") => {
    setIsBackingUp(true)
    setBackupProgress(0)

    try {
      // Simulate backup progress
      const interval = setInterval(() => {
        setBackupProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval)
            return 100
          }
          return prev + 10
        })
      }, 200)

      // In a real implementation, this would call a backup API
      const response = await fetch("/api/backup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ type }),
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement("a")
        a.href = url
        a.download = `jewellery_backup_${new Date().toISOString().split("T")[0]}.sql`
        a.click()
        window.URL.revokeObjectURL(url)
      }
    } catch (error) {
      console.error("Backup failed:", error)
    } finally {
      setIsBackingUp(false)
    }
  }

  const handleRestore = async () => {
    if (!selectedFile) return

    setIsRestoring(true)
    setRestoreProgress(0)

    try {
      const formData = new FormData()
      formData.append("backup", selectedFile)

      // Simulate progress
      const interval = setInterval(() => {
        setRestoreProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval)
            return 100
          }
          return prev + 8
        })
      }, 300)

      const response = await fetch("/api/restore", {
        method: "POST",
        body: formData,
      })

      if (response.ok) {
        alert("Database restored successfully!")
      } else {
        throw new Error("Restore failed")
      }
    } catch (error) {
      console.error("Restore failed:", error)
      alert("Restore failed. Please try again.")
    } finally {
      setIsRestoring(false)
      setSelectedFile(null)
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
    }
  }

  const downloadBackup = (backup: BackupFile) => {
    // In a real application, this would download the actual backup file
    const element = document.createElement("a")
    element.setAttribute("href", "data:text/plain;charset=utf-8," + encodeURIComponent("-- Sample backup file content"))
    element.setAttribute("download", backup.name)
    element.style.display = "none"
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Backup</CardTitle>
            <Database className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Today</div>
            <p className="text-xs text-muted-foreground">10:30 AM - Full backup</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Backup Size</CardTitle>
            <Shield className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.5 MB</div>
            <p className="text-xs text-muted-foreground">Compressed database</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Auto Backup</CardTitle>
            <Clock className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Daily</div>
            <p className="text-xs text-muted-foreground">10:30 AM scheduled</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Create Backup
            </CardTitle>
            <CardDescription>Create a backup of your jewellery database</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {isBackingUp && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Creating backup...</span>
                  <span>{backupProgress}%</span>
                </div>
                <Progress value={backupProgress} className="w-full" />
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <Button onClick={() => handleBackup("full")} disabled={isBackingUp} className="flex items-center gap-2">
                {isBackingUp ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Database className="h-4 w-4" />}
                Full Backup
              </Button>

              <Button
                variant="outline"
                onClick={() => handleBackup("partial")}
                disabled={isBackingUp}
                className="flex items-center gap-2"
              >
                {isBackingUp ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Database className="h-4 w-4" />}
                Partial Backup
              </Button>
            </div>

            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                Full backup includes all data (inventory, bills, suppliers). Partial backup includes only recent
                transactions.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Restore Database
            </CardTitle>
            <CardDescription>Restore your database from a backup file</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="backup-file">Select Backup File</Label>
              <Input
                id="backup-file"
                type="file"
                accept=".sql,.db,.backup"
                onChange={handleFileSelect}
                disabled={isRestoring}
              />
            </div>

            {selectedFile && (
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-sm font-medium">{selectedFile.name}</p>
                <p className="text-xs text-muted-foreground">Size: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
              </div>
            )}

            {isRestoring && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Restoring database...</span>
                  <span>{restoreProgress}%</span>
                </div>
                <Progress value={restoreProgress} className="w-full" />
              </div>
            )}

            <Dialog>
              <DialogTrigger asChild>
                <Button
                  disabled={!selectedFile || isRestoring}
                  className="w-full flex items-center gap-2"
                  variant={selectedFile ? "default" : "outline"}
                >
                  {isRestoring ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Upload className="h-4 w-4" />}
                  Restore Database
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-amber-500" />
                    Confirm Database Restore
                  </DialogTitle>
                  <DialogDescription>
                    This action will replace all current data with the backup data. This cannot be undone. Are you sure
                    you want to continue?
                  </DialogDescription>
                </DialogHeader>
                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline">Cancel</Button>
                  <Button onClick={handleRestore} className="bg-red-600 hover:bg-red-700">
                    Yes, Restore Database
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            <Alert className="border-amber-200 bg-amber-50">
              <AlertTriangle className="h-4 w-4 text-amber-600" />
              <AlertDescription className="text-amber-800">
                Warning: Restoring will overwrite all current data. Create a backup before restoring.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Backup History</CardTitle>
          <CardDescription>Previous backups and their status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 max-h-[500px] overflow-y-auto">
            {backupHistory.map((backup) => (
              <div key={backup.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Database className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium">{backup.name}</p>
                    <p className="text-sm text-muted-foreground">{backup.date}</p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <p className="text-sm font-medium">{backup.size}</p>
                    <div className="flex items-center gap-2">
                      <Badge variant={backup.type === "Full" ? "default" : "secondary"}>{backup.type}</Badge>
                      <Badge variant={backup.status === "Success" ? "default" : "destructive"}>
                        {backup.status === "Success" ? (
                          <CheckCircle className="h-3 w-3" />
                        ) : (
                          <AlertTriangle className="h-3 w-3" />
                        )}
                        {backup.status}
                      </Badge>
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadBackup(backup)}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Download
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
