const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkBillsTable() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME
    });

    console.log('Connected to database:', process.env.DB_NAME);

    // Check if bills table exists
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'bills'
    `, [process.env.DB_NAME || 'jewellery_wholesale']);

    if (tables.length === 0) {
      console.log('❌ Bills table does not exist');
      
      // Create bills table
      console.log('Creating bills table...');
      await connection.execute(`
        CREATE TABLE bills (
            id INT AUTO_INCREMENT PRIMARY KEY,
            customer_id INT,
            bill_number VARCHAR(50) UNIQUE,
            product_name VARCHAR(255),
            product_type VARCHAR(255),
            with_stone DECIMAL(10,3) DEFAULT 0,
            without_stone DECIMAL(10,3) DEFAULT 0,
            gross_weight DECIMAL(10,3) DEFAULT 0,
            stone_weight DECIMAL(10,3) DEFAULT 0,
            net_weight DECIMAL(10,3) DEFAULT 0,
            tunch_with_stone INT DEFAULT 0,
            tunch_without_stone INT DEFAULT 0,
            weight_in_24k DECIMAL(10,3) DEFAULT 0,
            gold_24k_price DECIMAL(10,2) DEFAULT 0,
            stone_price DECIMAL(10,2) DEFAULT 0,
            making_charges DECIMAL(10,2) DEFAULT 0,
            total_amount DECIMAL(15,2) DEFAULT 0,
            status ENUM('Pending', 'Completed', 'Cancelled') DEFAULT 'Pending',
            bill_date DATE DEFAULT (CURRENT_DATE),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_bills_bill_date (bill_date),
            INDEX idx_bills_status (status)
        )
      `);
      console.log('✅ Bills table created successfully');
    } else {
      console.log('✅ Bills table exists');
      
      // Get bills table schema
      const [columns] = await connection.execute(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'bills'
        ORDER BY ORDINAL_POSITION
      `, [process.env.DB_NAME || 'jewellery_wholesale']);

      console.log('\nBills table schema:');
      columns.forEach(col => {
        console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'} ${col.COLUMN_DEFAULT ? `DEFAULT ${col.COLUMN_DEFAULT}` : ''}`);
      });
    }

    // Check if customers table exists
    const [customerTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'customers'
    `, [process.env.DB_NAME || 'jewellery_wholesale']);

    if (customerTables.length === 0) {
      console.log('\n❌ Customers table does not exist');
      
      // Create customers table
      console.log('Creating customers table...');
      await connection.execute(`
        CREATE TABLE customers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            location VARCHAR(255),
            contact_person VARCHAR(255),
            phone VARCHAR(20),
            email VARCHAR(255),
            address TEXT,
            total_purchases DECIMAL(15,2) DEFAULT 0,
            last_purchase_date DATE,
            status ENUM('Active', 'Inactive') DEFAULT 'Active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_customers_name (name),
            INDEX idx_customers_status (status)
        )
      `);
      console.log('✅ Customers table created successfully');
    } else {
      console.log('\n✅ Customers table exists');
    }

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
checkBillsTable();
