import { executeQuery, executeSingle, executeUpdate } from "../database"

export interface Bill {
  id: number
  customer_id: number
  inventory_item_id?: number
  customer_name?: string
  customer_location?: string
  bill_number: string
  product_name: string
  product_type: string
  with_stone: number
  without_stone: number
  gross_weight: number
  stone_weight: number
  net_weight: number
  tunch_with_stone: number
  tunch_without_stone: number
  weight_in_24k: number
  gold_24k_price: number
  stone_price: number
  total_amount: number
  status: "Pending" | "Paid" | "Cancelled"
  bill_date: string
  created_at: string
  updated_at: string
}

export interface CreateBillData {
  customer_id: number
  inventory_item_id?: number
  bill_number: string
  product_name: string
  product_type: string
  with_stone?: number
  without_stone?: number
  gross_weight?: number
  stone_weight?: number
  net_weight?: number
  tunch_with_stone?: number
  tunch_without_stone?: number
  weight_in_24k?: number
  gold_24k_price?: number
  stone_price?: number
  total_amount?: number
}

export class BillModel {
  // =====================================================
  // JEWELLERY WHOLESALE SPECIFIC CALCULATIONS
  // =====================================================

  /**
   * Calculate bill total using CSV sample logic
   * Formula: (24K weight × 24K price per gram) + stone amount = total
   */
  static calculateBillTotal(
    weight24k: number, 
    goldRate24k: number, 
    stoneAmount: number = 0
  ): number {
    if (weight24k <= 0 || goldRate24k <= 0) return stoneAmount
    const goldValue = weight24k * goldRate24k
    return Number((goldValue + stoneAmount).toFixed(2))
  }

  /**
   * Calculate 24K weight from tunch percentage
   * Based on CSV sample: 10.160g (22k) with 96% tunch = 9.754g (24k)
   */
  static calculate24KFromTunch(weight: number, tunchPercentage: number): number {
    if (weight <= 0 || tunchPercentage <= 0) return 0
    return Number(((weight * tunchPercentage) / 100).toFixed(3))
  }

  // Get all bills with customer details
  static async getAll(): Promise<Bill[]> {
    const query = `
      SELECT b.*, c.name as customer_name, c.location as customer_location
      FROM bills b
      LEFT JOIN customers c ON b.customer_id = c.id
      ORDER BY b.created_at DESC
    `
    return executeQuery<Bill>(query)
  }

  // Get bill by ID
  static async getById(id: number): Promise<Bill | null> {
    const query = `
      SELECT b.*, c.name as customer_name, c.location as customer_location
      FROM bills b
      LEFT JOIN customers c ON b.customer_id = c.id
      WHERE b.id = ?
    `
    const results = await executeQuery<Bill>(query, [id])
    return results[0] || null
  }

  // Create new bill
  static async create(data: CreateBillData): Promise<number> {
    const query = `
      INSERT INTO bills (
        customer_id, inventory_item_id, bill_number, product_name, product_type, with_stone,
        without_stone, gross_weight, stone_weight, net_weight, tunch_with_stone,
        tunch_without_stone, weight_in_24k, gold_24k_price, stone_price, total_amount
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    const params = [
      data.customer_id,
      data.inventory_item_id || null,
      data.bill_number,
      data.product_name,
      data.product_type,
      data.with_stone || 0,
      data.without_stone || 0,
      data.gross_weight || 0,
      data.stone_weight || 0,
      data.net_weight || 0,
      data.tunch_with_stone || 0,
      data.tunch_without_stone || 0,
      data.weight_in_24k || 0,
      data.gold_24k_price || 0,
      data.stone_price || 0,
      data.total_amount || 0,
    ]
    const result = await executeUpdate(query, params)
    return result.insertId!
  }

  // Update bill status
  static async updateStatus(id: number, status: "Pending" | "Paid" | "Cancelled"): Promise<boolean> {
    const query = "UPDATE bills SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
    const result = await executeUpdate(query, [status, id])
    return result.affectedRows > 0
  }

  // Delete bill
  static async delete(id: number): Promise<boolean> {
    const query = "DELETE FROM bills WHERE id = ?"
    const result = await executeUpdate(query, [id])
    return result.affectedRows > 0
  }

  // Search bills
  static async search(searchTerm: string): Promise<Bill[]> {
    const query = `
      SELECT b.*, c.name as customer_name, c.location as customer_location
      FROM bills b
      LEFT JOIN customers c ON b.customer_id = c.id
      WHERE b.bill_number LIKE ? OR b.product_name LIKE ? OR c.name LIKE ?
      ORDER BY b.created_at DESC
    `
    const searchPattern = `%${searchTerm}%`
    return executeQuery<Bill>(query, [searchPattern, searchPattern, searchPattern])
  }

  // Get bills by date range
  static async getByDateRange(startDate: string, endDate: string): Promise<Bill[]> {
    const query = `
      SELECT b.*, c.name as customer_name, c.location as customer_location
      FROM bills b
      LEFT JOIN customers c ON b.customer_id = c.id
      WHERE b.bill_date BETWEEN ? AND ?
      ORDER BY b.bill_date DESC
    `
    return executeQuery<Bill>(query, [startDate, endDate])
  }

  // Get bills by customer
  static async getByCustomer(customerId: number): Promise<Bill[]> {
    const query = `
      SELECT b.*, c.name as customer_name, c.location as customer_location
      FROM bills b
      LEFT JOIN customers c ON b.customer_id = c.id
      WHERE b.customer_id = ?
      ORDER BY b.created_at DESC
    `
    return executeQuery<Bill>(query, [customerId])
  }

  // Generate next bill number
  static async generateBillNumber(): Promise<string> {
    const query = "SELECT COUNT(*) as count FROM bills WHERE bill_date = CURRENT_DATE"
    const results = await executeQuery<{ count: number }>(query)
    const count = results[0]?.count || 0
    const today = new Date().toISOString().slice(0, 10).replace(/-/g, "")
    return `INV${today}${String(count + 1).padStart(3, "0")}`
  }

  // Get sales summary
  static async getSalesSummary(
    startDate?: string,
    endDate?: string,
  ): Promise<{
    totalSales: number
    totalBills: number
    avgBillValue: number
  }> {
    let query = `
      SELECT 
        COUNT(*) as totalBills,
        COALESCE(SUM(total_amount), 0) as totalSales,
        COALESCE(AVG(total_amount), 0) as avgBillValue
      FROM bills 
      WHERE status != 'Cancelled'
    `
    const params: string[] = []

    if (startDate && endDate) {
      query += " AND bill_date BETWEEN ? AND ?"
      params.push(startDate, endDate)
    }

    const results = await executeQuery<{
      totalBills: number
      totalSales: number
      avgBillValue: number
    }>(query, params)

    return results[0] || { totalSales: 0, totalBills: 0, avgBillValue: 0 }
  }
}
