import { type NextRequest, NextResponse } from "next/server"
import { InventoryModel } from "@/lib/models/inventory"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const item = await InventoryModel.getById(id)

    if (!item) {
      return NextResponse.json({ success: false, error: "Inventory item not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: item })
  } catch (error) {
    console.error("Error fetching inventory item:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch inventory item" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const data = await request.json()

    console.log("PUT /api/inventory/[id] - Updating item:", id, "with data:", data)

    const success = await InventoryModel.update(id, data)

    console.log("Update result:", success)

    if (!success) {
      return NextResponse.json(
        { success: false, error: "Inventory item not found or no changes made" },
        { status: 404 },
      )
    }

    const item = await InventoryModel.getById(id)
    console.log("Updated item retrieved:", item)
    return NextResponse.json({ success: true, data: item })
  } catch (error) {
    console.error("Error updating inventory item:", error)
    return NextResponse.json({ success: false, error: "Failed to update inventory item" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const success = await InventoryModel.delete(id)

    if (!success) {
      return NextResponse.json({ success: false, error: "Inventory item not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "Inventory item deleted successfully" })
  } catch (error) {
    console.error("Error deleting inventory item:", error)
    return NextResponse.json({ success: false, error: "Failed to delete inventory item" }, { status: 500 })
  }
}
