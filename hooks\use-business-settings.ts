"use client"

import { useState, useEffect } from "react"

export interface BusinessSettings {
  // Purity Conversion Factors
  conversion_24k_to_22k: number
  conversion_24k_to_18k: number
  conversion_22k_to_24k: number
  conversion_18k_to_24k: number
  
  // Default Wastage Rates (%)
  wastage_rate_bar: number
  wastage_rate_jewel: number
  wastage_rate_old_jewel: number
  
  // Business Rules
  auto_calculate_balances: boolean
  track_stone_separately: boolean
  enable_wastage_alerts: boolean
  low_stock_threshold: number
  
  // Pricing Rules
  default_making_charges: number
  stone_pricing_method: "weight" | "value" | "both"
  apply_gst: boolean
  gst_rate: number
  
  // Calculation Methods
  rounding_precision: number
  balance_calculation_method: "simple" | "weighted_average" | "fifo"
  
  // Alert Thresholds
  high_wastage_threshold: number
  price_variance_threshold: number
}

export const DEFAULT_BUSINESS_SETTINGS: BusinessSettings = {
  conversion_24k_to_22k: 0.916,
  conversion_24k_to_18k: 0.750,
  conversion_22k_to_24k: 1.092,
  conversion_18k_to_24k: 1.333,
  
  wastage_rate_bar: 0.5,
  wastage_rate_jewel: 2.0,
  wastage_rate_old_jewel: 3.0,
  
  auto_calculate_balances: true,
  track_stone_separately: true,
  enable_wastage_alerts: true,
  low_stock_threshold: 10.0,
  
  default_making_charges: 500.0,
  stone_pricing_method: "both",
  apply_gst: true,
  gst_rate: 3.0,
  
  rounding_precision: 3,
  balance_calculation_method: "simple",
  
  high_wastage_threshold: 5.0,
  price_variance_threshold: 10.0
}

export function useBusinessSettings() {
  const [settings, setSettings] = useState<BusinessSettings>(DEFAULT_BUSINESS_SETTINGS)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = () => {
    try {
      const savedSettings = localStorage.getItem('business_settings')
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings)
        setSettings({ ...DEFAULT_BUSINESS_SETTINGS, ...parsed })
      }
    } catch (error) {
      console.error("Error loading business settings:", error)
    } finally {
      setLoading(false)
    }
  }

  const updateSettings = (newSettings: Partial<BusinessSettings>) => {
    const updatedSettings = { ...settings, ...newSettings }
    setSettings(updatedSettings)
    localStorage.setItem('business_settings', JSON.stringify(updatedSettings))
  }

  const getWastageRate = (formType: string): number => {
    switch (formType) {
      case "Bar":
        return settings.wastage_rate_bar
      case "Jewel":
        return settings.wastage_rate_jewel
      case "Old Jewel":
        return settings.wastage_rate_old_jewel
      default:
        return settings.wastage_rate_jewel
    }
  }

  const resetToDefaults = () => {
    setSettings(DEFAULT_BUSINESS_SETTINGS)
    localStorage.setItem('business_settings', JSON.stringify(DEFAULT_BUSINESS_SETTINGS))
  }

  return {
    settings,
    loading,
    updateSettings,
    getWastageRate,
    resetToDefaults,
    loadSettings
  }
}
