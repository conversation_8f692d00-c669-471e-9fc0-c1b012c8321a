"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Plus, Search, Edit, Trash2, Shield, Users, Key, UserCheck } from "lucide-react"
import { useDatabase, useDatabaseMutation } from "@/lib/hooks/useDatabase"
import { useToast } from "@/hooks/use-toast"
import EditUserDialog from "@/components/edit-user-dialog"
import type { User as UserType } from "@/lib/models/user"

export default function UserManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<UserType | null>(null)
  const [newUser, setNewUser] = useState<Partial<UserType>>({
    permissions: {
      inventory: false,
      billing: false,
      reports: false,
      suppliers: false,
      goldRates: false,
      backup: false,
    },
  })

  const { data: users, loading, error, refetch } = useDatabase<UserType>("users", searchTerm)
  const { mutate, loading: mutating } = useDatabaseMutation<UserType>()
  const { toast } = useToast()

  const filteredUsers =
    users?.filter(
      (user) =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.role.toLowerCase().includes(searchTerm.toLowerCase()),
    ) || []

  const handleAddUser = async () => {
    if (!newUser.name || !newUser.email || !newUser.role) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields (Name, Email, and Role).",
        variant: "destructive",
      })
      return
    }

    try {
      const userData = {
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
        permissions: newUser.permissions || {
          inventory: false,
          billing: false,
          reports: false,
          suppliers: false,
          goldRates: false,
          backup: false,
        },
      }

      const result = await mutate("users", "POST", userData)
      if (result) {
        toast({
          title: "User Added Successfully",
          description: `"${newUser.name}" has been added as ${newUser.role}.`,
          variant: "default",
        })

        setNewUser({
          permissions: {
            inventory: false,
            billing: false,
            reports: false,
            suppliers: false,
            goldRates: false,
            backup: false,
          },
        })
        setIsAddDialogOpen(false)
        await refetch()
      } else {
        toast({
          title: "Add Failed",
          description: "Failed to add the user. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Add Error",
        description: "An error occurred while adding the user.",
        variant: "destructive",
      })
    }
  }

  const toggleUserStatus = async (userId: number, currentStatus: string) => {
    const user = users?.find(u => u.id === userId)
    const userName = user?.name || `User #${userId}`
    const newStatus = currentStatus === "Active" ? "Inactive" : "Active"

    try {
      const result = await mutate(`users/${userId}`, "PUT", { status: newStatus })
      if (result) {
        toast({
          title: "Status Updated",
          description: `"${userName}" is now ${newStatus}.`,
          variant: "default",
        })
        await refetch()
      } else {
        toast({
          title: "Update Failed",
          description: "Failed to update user status. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Update Error",
        description: "An error occurred while updating user status.",
        variant: "destructive",
      })
    }
  }

  const handleEditUser = (user: UserType) => {
    setEditingUser(user)
    setIsEditDialogOpen(true)
  }

  const handleUpdateUser = async () => {
    if (!editingUser) return

    try {
      const result = await mutate(`users/${editingUser.id}`, "PUT", {
        name: editingUser.name,
        email: editingUser.email,
        role: editingUser.role,
        permissions: editingUser.permissions,
        status: editingUser.status,
      })

      if (result) {
        toast({
          title: "User Updated Successfully",
          description: `"${editingUser.name}" has been updated.`,
          variant: "default",
        })
        setEditingUser(null)
        setIsEditDialogOpen(false)
        await refetch()
      } else {
        toast({
          title: "Update Failed",
          description: "Failed to update the user. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Update Error",
        description: "An error occurred while updating the user.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteUser = async (id: number) => {
    const user = users?.find(u => u.id === id)
    const userName = user?.name || `User #${id}`

    if (confirm(`Are you sure you want to delete "${userName}"? This action cannot be undone.`)) {
      try {
        const result = await mutate(`users/${id}`, "DELETE")
        if (result && (result as any).deleted) {
          toast({
            title: "User Deleted",
            description: `"${userName}" has been successfully deleted.`,
            variant: "default",
          })
          await refetch()
        } else {
          toast({
            title: "Delete Failed",
            description: "Failed to delete the user. Please try again.",
            variant: "destructive",
          })
        }
      } catch (error) {
        toast({
          title: "Delete Error",
          description: "An error occurred while deleting the user.",
          variant: "destructive",
        })
      }
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case "Admin":
        return "bg-red-100 text-red-800"
      case "Manager":
        return "bg-blue-100 text-blue-800"
      case "Staff":
        return "bg-green-100 text-green-800"
      case "Viewer":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPermissionCount = (permissions: UserType["permissions"]) => {
    return Object.values(permissions).filter(Boolean).length
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users?.length}</div>
            <p className="text-xs text-muted-foreground">Registered users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users?.filter((u) => u.status === "Active").length}</div>
            <p className="text-xs text-muted-foreground">Currently active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Admins</CardTitle>
            <Shield className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users?.filter((u) => u.role === "Admin").length}</div>
            <p className="text-xs text-muted-foreground">Administrator accounts</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Logins</CardTitle>
            <Key className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users?.filter((u) => u.lastLogin !== "Never").length}</div>
            <p className="text-xs text-muted-foreground">Users logged in today</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>User Management</CardTitle>
              <CardDescription>Manage user accounts and permissions</CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add User
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Add New User</DialogTitle>
                  <DialogDescription>Create a new user account with specific permissions</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto px-1">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="userName">Full Name</Label>
                      <Input
                        id="userName"
                        value={newUser.name || ""}
                        onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                        placeholder="Enter full name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="userEmail">Email Address</Label>
                      <Input
                        id="userEmail"
                        type="email"
                        value={newUser.email || ""}
                        onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                        placeholder="Enter email address"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="userRole">Role</Label>
                    <Select onValueChange={(value) => setNewUser({ ...newUser, role: value as UserType["role"] })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select user role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Admin">Admin - Full access</SelectItem>
                        <SelectItem value="Manager">Manager - Limited admin access</SelectItem>
                        <SelectItem value="Staff">Staff - Basic operations</SelectItem>
                        <SelectItem value="Viewer">Viewer - Read-only access</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-4">
                    <Label>Permissions</Label>
                    <div className="grid grid-cols-2 gap-4">
                      {Object.entries(newUser.permissions || {}).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between">
                          <Label htmlFor={key} className="capitalize">
                            {key === "goldRates" ? "Gold Rates" : key}
                          </Label>
                          <Switch
                            id={key}
                            checked={value}
                            onCheckedChange={(checked) =>
                              setNewUser({
                                ...newUser,
                                permissions: {
                                  ...newUser.permissions,
                                  [key]: checked,
                                },
                              })
                            }
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex justify-end gap-2 pt-4 border-t bg-white sticky bottom-0">
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddUser} disabled={mutating}>
                    Add User
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Badge variant="secondary">{filteredUsers.length} users</Badge>
          </div>

          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User Details</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Permissions</TableHead>
                    <TableHead>Last Login</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{user.name}</p>
                          <p className="text-sm text-muted-foreground">{user.email}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getRoleColor(user.role)}>{user.role}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{getPermissionCount(user.permissions)}/6 modules</Badge>
                          <div className="flex gap-1">
                            {user.permissions.inventory && (
                              <div className="w-2 h-2 bg-green-500 rounded-full" title="Inventory" />
                            )}
                            {user.permissions.billing && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full" title="Billing" />
                            )}
                            {user.permissions.reports && (
                              <div className="w-2 h-2 bg-purple-500 rounded-full" title="Reports" />
                            )}
                            {user.permissions.suppliers && (
                              <div className="w-2 h-2 bg-orange-500 rounded-full" title="Suppliers" />
                            )}
                            {user.permissions.goldRates && (
                              <div className="w-2 h-2 bg-yellow-500 rounded-full" title="Gold Rates" />
                            )}
                            {user.permissions.backup && (
                              <div className="w-2 h-2 bg-red-500 rounded-full" title="Backup" />
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm">{user.lastLogin}</p>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge variant={user.status === "Active" ? "default" : "secondary"}>{user.status}</Badge>
                          <Switch
                            checked={user.status === "Active"}
                            onCheckedChange={() => toggleUserStatus(user.id, user.status)}
                            size="sm"
                            disabled={mutating}
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" onClick={() => handleEditUser(user)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700 bg-transparent"
                            onClick={() => handleDeleteUser(user.id)}
                            disabled={mutating}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
      <EditUserDialog
        user={editingUser}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSuccess={refetch}
      />
    </div>
  )
}
