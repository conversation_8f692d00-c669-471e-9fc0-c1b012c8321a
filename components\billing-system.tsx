"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Plus, Search } from "lucide-react"
import PrintInvoice from "@/components/print-invoice"
import { useDatabase } from "@/hooks/use-database"
import { useDatabaseMutation } from "@/hooks/use-database-mutation"

interface BillingItem {
  id: number
  customer_name: string
  customer_location: string
  product_name: string
  product_type: string
  with_stone: number
  without_stone: number
  gross_weight: number
  stone_weight: number
  net_weight: number
  tunch_with_stone: number
  tunch_without_stone: number
  weight_in_24k: number
  gold_24k_price: number
  stone_price: number
  total_amount: number
  bill_date: string
  status: "Pending" | "Paid" | "Cancelled"
}

interface Bill {
  id: number
  customer_id: number
  bill_number: string
  product_name: string
  product_type: string
  with_stone: number
  without_stone: number
  gross_weight: number
  stone_weight: number
  net_weight: number
  tunch_with_stone: number
  tunch_without_stone: number
  weight_in_24k: number
  gold_24k_price: number
  stone_price: number
  total_amount: number
  status: "Pending" | "Paid" | "Cancelled"
}

interface Customer {
  id: number
  name: string
  location: string
  phone: string
}

interface InventoryItem {
  id: number
  supplier_name: string
  product_name: string
  product_type: string
  with_stone_weight: number
  without_stone_weight: number
  balance_weight_24k: number
  balance_weight_22k: number
  status: "Available" | "Low Stock" | "Out of Stock"
}

export default function BillingSystem() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newBill, setNewBill] = useState<Partial<BillingItem>>({})
  const [goldRate, setGoldRate] = useState(10140)

  const { data: bills, loading, error, refetch } = useDatabase<Bill>("bills", searchTerm)
  const { data: customers } = useDatabase<Customer>("customers")
  const { data: allInventory } = useDatabase<InventoryItem>("inventory")
  const { mutate, loading: mutating } = useDatabaseMutation<Bill>()

  // Filter inventory items to show only available ones
  const inventoryItems = allInventory?.filter(item =>
    (Number(item.balance_weight_24k) || 0) > 0 && item.status !== 'Out of Stock'
  ) || []

  const filteredBills =
    bills?.filter((bill) => bill.product_name.toLowerCase().includes(searchTerm.toLowerCase())) || []

  const calculateTotal = (item: Partial<BillingItem>) => {
    const withoutStoneWeight = item.without_stone || 0
    const tunchWithoutStone = item.tunch_without_stone || 0
    const stonePrice = item.stone_price || 0

    // Calculate 24K equivalent weight using tunch percentage
    const weightIn24k = (withoutStoneWeight * tunchWithoutStone) / 100
    const goldValue = weightIn24k * goldRate

    return goldValue + stonePrice
  }

  const handleAddBill = async () => {
    if (newBill.customer_name && newBill.product_name) {
      const customer = customers?.find((c) => c.name === newBill.customer_name)
      if (!customer) return

      const billData = {
        customer_id: customer.id,
        bill_number: `INV${Date.now()}`, // Generate unique bill number
        product_name: newBill.product_name,
        product_type: newBill.product_type,
        with_stone: newBill.with_stone || 0,
        without_stone: newBill.without_stone || 0,
        gross_weight: (newBill.with_stone || 0) + (newBill.without_stone || 0),
        stone_weight: newBill.stone_weight || 0,
        net_weight: newBill.net_weight || newBill.without_stone || 0,
        tunch_with_stone: newBill.tunch_with_stone || 0,
        tunch_without_stone: newBill.tunch_without_stone || 0,
        weight_in_24k: newBill.weight_in_24k || 0,
        gold_24k_price: goldRate,
        stone_price: newBill.stone_price || 0,
        total_amount: calculateTotal(newBill),
        status: "Pending",
      }

      const result = await mutate("bills", "POST", billData)
      if (result) {
        setNewBill({})
        setIsAddDialogOpen(false)
        refetch()
      }
    }
  }

  const handleUpdateBillStatus = async (billId: number, newStatus: "Pending" | "Paid" | "Cancelled") => {
    const result = await mutate(`bills/${billId}`, "PUT", { status: newStatus })
    if (result) {
      refetch()
    }
  }

  const totalValue = filteredBills.reduce((sum, bill) => sum + (Number(bill.total_amount) || 0), 0)

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Billing System</CardTitle>
              <CardDescription>Manage customer bills and transactions</CardDescription>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-sm text-muted-foreground">Current Gold Rate (24K)</p>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    value={goldRate}
                    onChange={(e) => setGoldRate(Number.parseFloat(e.target.value) || 0)}
                    className="w-32 text-right font-medium"
                  />
                  <span className="text-sm text-muted-foreground">/10g</span>
                </div>
              </div>
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    New Bill
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Create New Bill</DialogTitle>
                    <DialogDescription>Select customer and inventory item for billing</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-6 py-4 max-h-[70vh] overflow-y-auto px-1">
                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label>Select Customer</Label>
                        <Select
                          onValueChange={(value) => {
                            const customer = customers?.find((c) => c.id === Number.parseInt(value))
                            if (customer) {
                              setNewBill({
                                ...newBill,
                                customer_name: customer.name,
                                customer_location: customer.location,
                              })
                            }
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Choose existing customer" />
                          </SelectTrigger>
                          <SelectContent>
                            {customers?.map((customer) => (
                              <SelectItem key={customer.id} value={customer.id.toString()}>
                                <div className="flex flex-col">
                                  <span className="font-medium">{customer.name}</span>
                                  <span className="text-sm text-muted-foreground">{customer.location}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Select Inventory Item</Label>
                        <Select
                          onValueChange={(value) => {
                            const item = inventoryItems?.find((i) => i.id === Number.parseInt(value))
                            if (item) {
                              const availableWeight = Number(item.balance_weight_24k) || 0
                              const weightToSell = Math.min(availableWeight, 10) // Default 10g or available stock
                              const defaultTunch = 95 // Default tunch percentage for gold
                              setNewBill({
                                ...newBill,
                                product_name: item.product_name,
                                product_type: item.product_type,
                                with_stone: Number(item.with_stone_weight) || 0,
                                without_stone: weightToSell,
                                gross_weight: (Number(item.with_stone_weight) || 0) + weightToSell,
                                stone_weight: 0,
                                net_weight: weightToSell,
                                tunch_with_stone: 0,
                                tunch_without_stone: defaultTunch,
                                weight_in_24k: (weightToSell * defaultTunch) / 100,
                                stone_price: 0,
                              })
                            }
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Choose inventory item" />
                          </SelectTrigger>
                          <SelectContent>
                            {inventoryItems?.map((item) => (
                              <SelectItem key={item.id} value={item.id.toString()}>
                                <div className="flex flex-col">
                                  <span className="font-medium">
                                    {item.product_name} - {item.product_type}
                                  </span>
                                  <span className="text-sm text-muted-foreground">
                                    Available: {(Number(item.balance_weight_24k) || 0).toFixed(3)}g | Status: {item.status}
                                  </span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {newBill.product_name && (
                      <Card className="border-blue-200 bg-blue-50">
                        <CardHeader>
                          <CardTitle className="text-lg">Selected Item Details</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium">Product: {newBill.product_name}</p>
                              <p className="text-sm text-muted-foreground">Type: {newBill.product_type}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">Tunch: {newBill.tunch_without_stone}%</p>
                              <p className="text-sm text-muted-foreground">
                                24K Weight: {(Number(newBill.weight_in_24k) || 0).toFixed(4)}g
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    <div className="space-y-4">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="sellWeight">Weight to Sell (g)</Label>
                          <Input
                            id="sellWeight"
                            type="number"
                            step="0.001"
                            value={newBill.without_stone || ""}
                            onChange={(e) => {
                              const weight = Number.parseFloat(e.target.value) || 0
                              setNewBill({
                                ...newBill,
                                without_stone: weight,
                                gross_weight: (newBill.with_stone || 0) + weight,
                                net_weight: weight,
                                weight_in_24k: (weight * (newBill.tunch_without_stone || 0)) / 100,
                              })
                            }}
                            placeholder="0.000"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="stonePrice">Stone Price (₹)</Label>
                          <Input
                            id="stonePrice"
                            type="number"
                            value={newBill.stone_price || ""}
                            onChange={(e) =>
                              setNewBill({ ...newBill, stone_price: Number.parseFloat(e.target.value) || 0 })
                            }
                            placeholder="0"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Gold Rate (₹/10g)</Label>
                          <Input
                            type="number"
                            value={goldRate}
                            onChange={(e) => setGoldRate(Number.parseFloat(e.target.value) || 0)}
                            className="font-medium"
                          />
                        </div>
                      </div>
                    </div>

                    {newBill.product_name && (
                      <div className="p-4 bg-amber-50 rounded-lg space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>Weight to Sell:</span>
                          <span>{(Number(newBill.without_stone) || 0).toFixed(3)}g</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span>Tunch %:</span>
                          <span>{newBill.tunch_without_stone || 0}%</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span>24K Equivalent:</span>
                          <span>{(Number(newBill.weight_in_24k) || 0).toFixed(4)}g</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="font-medium">Gold Rate:</span>
                          <span>₹{goldRate.toLocaleString("en-IN")}/10g</span>
                        </div>
                        <hr />
                        <div className="flex items-center justify-between">
                          <span className="font-medium">Calculated Total:</span>
                          <span className="text-2xl font-bold text-amber-600">
                            ₹{calculateTotal(newBill).toLocaleString("en-IN", { minimumFractionDigits: 2 })}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex justify-end gap-2 pt-4 border-t bg-white sticky bottom-0">
                    <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddBill} disabled={!newBill.customer_name || !newBill.product_name}>
                      Create Bill
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search bills..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Badge variant="secondary">{filteredBills.length} bills</Badge>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Total Value</p>
              <p className="text-2xl font-bold text-green-600">
                ₹{totalValue.toLocaleString("en-IN", { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>

          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Sl.No</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead>Weight Details</TableHead>
                    <TableHead>Tunch</TableHead>
                    <TableHead>24K Weight</TableHead>
                    <TableHead>Pricing</TableHead>
                    <TableHead>Total</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredBills.map((bill, index) => (
                    <TableRow key={bill.id}>
                      <TableCell>{index + 1}</TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{customers?.find((c) => c.id === bill.customer_id)?.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {customers?.find((c) => c.id === bill.customer_id)?.location}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{bill.product_name}</p>
                          <p className="text-sm text-muted-foreground">{bill.product_type}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <p>Gross: {(Number(bill.gross_weight) || 0).toFixed(3)}g</p>
                          <p>Stone: {(Number(bill.stone_weight) || 0).toFixed(3)}g</p>
                          <p>Net: {(Number(bill.net_weight) || 0).toFixed(3)}g</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <p>With Stone: {bill.tunch_with_stone}</p>
                          <p>Without: {bill.tunch_without_stone}</p>
                        </div>
                      </TableCell>
                      <TableCell>{(Number(bill.weight_in_24k) || 0).toFixed(3)}g</TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <p>Gold: ₹{(Number(bill.gold_24k_price) || 0).toLocaleString("en-IN")}</p>
                          <p>Stone: ₹{(Number(bill.stone_price) || 0).toLocaleString("en-IN")}</p>
                        </div>
                      </TableCell>
                      <TableCell className="font-bold text-green-600">
                        ₹{(Number(bill.total_amount) || 0).toLocaleString("en-IN", { minimumFractionDigits: 2 })}
                      </TableCell>
                      <TableCell>
                        <Select
                          value={bill.status}
                          onValueChange={(value) => handleUpdateBillStatus(bill.id, value as any)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Pending">Pending</SelectItem>
                            <SelectItem value="Paid">Paid</SelectItem>
                            <SelectItem value="Cancelled">Cancelled</SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <PrintInvoice billData={bill} />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleUpdateBillStatus(bill.id, bill.status === "Paid" ? "Pending" : "Paid")
                            }
                            disabled={mutating}
                          >
                            {bill.status === "Paid" ? "Mark Pending" : "Mark Paid"}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
