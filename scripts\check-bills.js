#!/usr/bin/env node

require("dotenv").config({ path: ".env.local" })

const mysql = require("mysql2/promise")

async function checkBills() {
  console.log("🧾 Checking current bills...\n")

  const config = {
    host: process.env.DB_HOST || "localhost",
    port: Number.parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "jewellery_wholesale_software",
  }

  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(config)

    // Get bills with customer info
    const [bills] = await connection.execute(`
      SELECT b.id, b.bill_number, b.product_name, b.weight_in_24k, 
             b.total_amount, b.status, b.bill_date, c.name as customer_name
      FROM bills b
      LEFT JOIN customers c ON b.customer_id = c.id
      ORDER BY b.created_at DESC
    `)

    console.log("📋 Current Bills:")
    console.log("=" .repeat(100))
    console.log("ID | Bill Number | Product Name      | Weight 24K | Total Amount | Status    | Customer")
    console.log("-" .repeat(100))
    
    bills.forEach(bill => {
      const id = String(bill.id).padEnd(2)
      const billNum = String(bill.bill_number || '').padEnd(11)
      const product = String(bill.product_name || '').padEnd(17)
      const weight = String((Number(bill.weight_in_24k) || 0).toFixed(3) + 'g').padEnd(10)
      const amount = String('₹' + (Number(bill.total_amount) || 0).toLocaleString('en-IN')).padEnd(12)
      const status = String(bill.status || '').padEnd(9)
      const customer = String(bill.customer_name || '').padEnd(15)
      
      console.log(`${id} | ${billNum} | ${product} | ${weight} | ${amount} | ${status} | ${customer}`)
    })

    console.log("-" .repeat(100))
    console.log(`Total Bills: ${bills.length}`)
    
    // Calculate totals
    const totalSales = bills.reduce((sum, bill) => sum + (Number(bill.total_amount) || 0), 0)
    const totalWeight = bills.reduce((sum, bill) => sum + (Number(bill.weight_in_24k) || 0), 0)
    const completedBills = bills.filter(bill => bill.status === 'Completed').length
    const pendingBills = bills.filter(bill => bill.status === 'Pending').length
    
    console.log(`\n📊 Summary:`)
    console.log(`   Total Sales: ₹${totalSales.toLocaleString('en-IN')}`)
    console.log(`   Total Weight Sold: ${totalWeight.toFixed(3)}g`)
    console.log(`   Completed Bills: ${completedBills}`)
    console.log(`   Pending Bills: ${pendingBills}`)

    await connection.end()
    
  } catch (error) {
    console.log("\n❌ Bills check failed:")
    console.log(`   Error: ${error.message}`)
    process.exit(1)
  }
}

checkBills()
