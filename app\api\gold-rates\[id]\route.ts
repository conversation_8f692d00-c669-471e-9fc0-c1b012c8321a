import { type NextRequest, NextResponse } from "next/server"
import { GoldRateModel } from "@/lib/models/gold-rate"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const goldRate = await GoldRateModel.getById(id)

    if (!goldRate) {
      return NextResponse.json({ success: false, error: "Gold rate not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: goldRate })
  } catch (error) {
    console.error("Error fetching gold rate:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch gold rate" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const data = await request.json()

    // Validate required fields
    if (!data.rate_24k) {
      return NextResponse.json({ success: false, error: "24K rate is required" }, { status: 400 })
    }

    const success = await GoldRateModel.update(id, data)

    if (!success) {
      return NextResponse.json({ success: false, error: "Gold rate not found" }, { status: 404 })
    }

    const goldRate = await GoldRateModel.getById(id)
    return NextResponse.json({ success: true, data: goldRate })
  } catch (error) {
    console.error("Error updating gold rate:", error)
    return NextResponse.json({ success: false, error: "Failed to update gold rate" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const success = await GoldRateModel.delete(id)

    if (!success) {
      return NextResponse.json({ success: false, error: "Gold rate not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "Gold rate deleted successfully" })
  } catch (error) {
    console.error("Error deleting gold rate:", error)
    return NextResponse.json({ success: false, error: "Failed to delete gold rate" }, { status: 500 })
  }
}
