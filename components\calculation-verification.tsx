"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Calculator, CheckCircle, AlertCircle } from "lucide-react"
import { useBusinessSettings } from "@/hooks/use-database-settings"
import { ClientBusinessLogic } from "@/lib/utils/client-business-logic"
import { useDatabase } from "@/hooks/use-database"

export default function CalculationVerification() {
  const { settings: businessSettings, loading: settingsLoading } = useBusinessSettings()
  const { data: goldRates } = useDatabase<any>("gold-rates")

  // Get current gold rate from database
  const currentGoldRate = goldRates && goldRates.length > 0 ? goldRates[0].rate_24k : 0

  const [calculation, setCalculation] = useState({
    chainWeight: 10.12,
    tunchPercent: 96,
    goldRate: 0,
    basePrice: 0,
    cgst: 0,
    sgst: 1430.68,
  })

  // Update calculation gold rate when database rates change
  useEffect(() => {
    if (currentGoldRate > 0) {
      setCalculation(prev => ({ ...prev, goldRate: currentGoldRate }))
    }
  }, [currentGoldRate])

  // Manual calculation using current database gold rate
  const manualCalculation = {
    chainWeight: 10.12,
    tunchPercent: 96,
    goldRate: currentGoldRate || 10112, // Use current rate or fallback
    weightIn24k: 10.12 * 0.96, // 9.7152
    goldValue: 9.7152 * (currentGoldRate || 10112), // Use current rate
    basePrice: 95348.79,
    cgst: 1430.68,
    sgst: 1430.68,
    totalWithTax: 95348.79 + 1430.68 + 1430.68, // 98,210.15
  }

  // System calculation
  const systemCalculation = {
    chainWeight: calculation.chainWeight,
    tunchPercent: calculation.tunchPercent,
    goldRate: calculation.goldRate,
    weightIn24k: (calculation.chainWeight * calculation.tunchPercent) / 100,
    goldValue: ((calculation.chainWeight * calculation.tunchPercent) / 100) * calculation.goldRate,
    basePrice: calculation.basePrice,
    cgst: calculation.cgst,
    sgst: calculation.sgst,
    totalWithTax: calculation.basePrice + calculation.cgst + calculation.sgst,
  }

  const isCalculationCorrect = Math.abs(systemCalculation.goldValue - manualCalculation.goldValue) < 0.01

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Calculation Verification
          </CardTitle>
          <CardDescription>Verify billing calculations against manual calculations</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Input Parameters</h3>
              <div className="space-y-3">
                <div className="space-y-2">
                  <Label htmlFor="chainWeight">Chain Weight (g)</Label>
                  <Input
                    id="chainWeight"
                    type="number"
                    step="0.001"
                    value={calculation.chainWeight}
                    onChange={(e) =>
                      setCalculation({ ...calculation, chainWeight: Number.parseFloat(e.target.value) || 0 })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tunchPercent">Tunch Percentage (%)</Label>
                  <Input
                    id="tunchPercent"
                    type="number"
                    value={calculation.tunchPercent}
                    onChange={(e) =>
                      setCalculation({ ...calculation, tunchPercent: Number.parseFloat(e.target.value) || 0 })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="goldRate">Gold Rate (₹/10g)</Label>
                  <Input
                    id="goldRate"
                    type="number"
                    value={calculation.goldRate}
                    onChange={(e) =>
                      setCalculation({ ...calculation, goldRate: Number.parseFloat(e.target.value) || 0 })
                    }
                  />
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Tax Calculation</h3>
              <div className="space-y-3">
                <div className="space-y-2">
                  <Label htmlFor="basePrice">Base Price (₹)</Label>
                  <Input
                    id="basePrice"
                    type="number"
                    step="0.01"
                    value={calculation.basePrice}
                    onChange={(e) =>
                      setCalculation({ ...calculation, basePrice: Number.parseFloat(e.target.value) || 0 })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cgst">CGST (₹)</Label>
                  <Input
                    id="cgst"
                    type="number"
                    step="0.01"
                    value={calculation.cgst}
                    onChange={(e) => setCalculation({ ...calculation, cgst: Number.parseFloat(e.target.value) || 0 })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sgst">SGST (₹)</Label>
                  <Input
                    id="sgst"
                    type="number"
                    step="0.01"
                    value={calculation.sgst}
                    onChange={(e) => setCalculation({ ...calculation, sgst: Number.parseFloat(e.target.value) || 0 })}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-lg text-blue-800">Manual Calculation (Your Note)</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Chain Weight:</span>
                  <span className="font-mono">{manualCalculation.chainWeight.toFixed(3)}g</span>
                </div>
                <div className="flex justify-between">
                  <span>Tunch %:</span>
                  <span className="font-mono">{manualCalculation.tunchPercent}%</span>
                </div>
                <div className="flex justify-between">
                  <span>24K Weight:</span>
                  <span className="font-mono">{manualCalculation.weightIn24k.toFixed(4)}g</span>
                </div>
                <div className="flex justify-between">
                  <span>Gold Rate:</span>
                  <span className="font-mono">₹{manualCalculation.goldRate.toLocaleString("en-IN")}</span>
                </div>
                <hr className="border-blue-300" />
                <div className="flex justify-between font-bold text-lg">
                  <span>Gold Value:</span>
                  <span className="font-mono text-blue-800">
                    ₹{manualCalculation.goldValue.toLocaleString("en-IN", { minimumFractionDigits: 2 })}
                  </span>
                </div>
                <div className="space-y-2 pt-2 border-t border-blue-300">
                  <div className="flex justify-between">
                    <span>Base Price:</span>
                    <span className="font-mono">₹{manualCalculation.basePrice.toLocaleString("en-IN")}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>CGST:</span>
                    <span className="font-mono">₹{manualCalculation.cgst.toLocaleString("en-IN")}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>SGST:</span>
                    <span className="font-mono">₹{manualCalculation.sgst.toLocaleString("en-IN")}</span>
                  </div>
                  <div className="flex justify-between font-bold">
                    <span>Total with Tax:</span>
                    <span className="font-mono">₹{manualCalculation.totalWithTax.toLocaleString("en-IN")}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="text-lg text-green-800">System Calculation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Chain Weight:</span>
                  <span className="font-mono">{systemCalculation.chainWeight.toFixed(3)}g</span>
                </div>
                <div className="flex justify-between">
                  <span>Tunch %:</span>
                  <span className="font-mono">{systemCalculation.tunchPercent}%</span>
                </div>
                <div className="flex justify-between">
                  <span>24K Weight:</span>
                  <span className="font-mono">{systemCalculation.weightIn24k.toFixed(4)}g</span>
                </div>
                <div className="flex justify-between">
                  <span>Gold Rate:</span>
                  <span className="font-mono">₹{systemCalculation.goldRate.toLocaleString("en-IN")}</span>
                </div>
                <hr className="border-green-300" />
                <div className="flex justify-between font-bold text-lg">
                  <span>Gold Value:</span>
                  <span className="font-mono text-green-800">
                    ₹{systemCalculation.goldValue.toLocaleString("en-IN", { minimumFractionDigits: 2 })}
                  </span>
                </div>
                <div className="space-y-2 pt-2 border-t border-green-300">
                  <div className="flex justify-between">
                    <span>Base Price:</span>
                    <span className="font-mono">₹{systemCalculation.basePrice.toLocaleString("en-IN")}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>CGST:</span>
                    <span className="font-mono">₹{systemCalculation.cgst.toLocaleString("en-IN")}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>SGST:</span>
                    <span className="font-mono">₹{systemCalculation.sgst.toLocaleString("en-IN")}</span>
                  </div>
                  <div className="flex justify-between font-bold">
                    <span>Total with Tax:</span>
                    <span className="font-mono">₹{systemCalculation.totalWithTax.toLocaleString("en-IN")}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card
            className={`border-2 ${isCalculationCorrect ? "border-green-500 bg-green-50" : "border-red-500 bg-red-50"}`}
          >
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                {isCalculationCorrect ? (
                  <CheckCircle className="h-6 w-6 text-green-600" />
                ) : (
                  <AlertCircle className="h-6 w-6 text-red-600" />
                )}
                <div>
                  <h3 className={`font-semibold ${isCalculationCorrect ? "text-green-800" : "text-red-800"}`}>
                    {isCalculationCorrect ? "✅ Calculations Match!" : "❌ Calculation Mismatch"}
                  </h3>
                  <p className={`text-sm ${isCalculationCorrect ? "text-green-700" : "text-red-700"}`}>
                    {isCalculationCorrect
                      ? "The system calculation matches your manual calculation perfectly."
                      : `Difference: ₹${Math.abs(systemCalculation.goldValue - manualCalculation.goldValue).toFixed(2)}`}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">Calculation Formula:</h4>
            <div className="font-mono text-sm space-y-1">
              <p>24K Weight = Chain Weight × (Tunch % ÷ 100)</p>
              <p>Gold Value = 24K Weight × Gold Rate</p>
              <p>Total = Gold Value + Stone Price</p>
              <p className="text-blue-600">
                Example: {calculation.chainWeight}g × {calculation.tunchPercent}% ={" "}
                {systemCalculation.weightIn24k.toFixed(4)}g × ₹{calculation.goldRate} = ₹
                {systemCalculation.goldValue.toLocaleString("en-IN", { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
