const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function checkEnumValues() {
  console.log('🔍 CHECKING ENUM VALUES');
  console.log('=======================\n');

  let connection;
  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jewellery_wholesale_software'
  };

  try {
    connection = await mysql.createConnection(config);
    console.log(`📊 Connected to database: ${config.database}`);

    // Check enum values for relevant columns
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME, COLUMN_TYPE 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory' 
      AND DATA_TYPE = 'enum'
    `, [config.database]);

    console.log('\n📋 ENUM COLUMNS AND THEIR VALUES:');
    columns.forEach(col => {
      console.log(`\n${col.COLUMN_NAME}:`);
      console.log(`   ${col.COLUMN_TYPE}`);
      
      // Extract enum values
      const enumMatch = col.COLUMN_TYPE.match(/enum\((.*)\)/);
      if (enumMatch) {
        const values = enumMatch[1].split(',').map(v => v.replace(/'/g, '').trim());
        console.log(`   Allowed values: ${values.join(', ')}`);
      }
    });

    // Test with valid enum values
    console.log('\n🧪 TESTING WITH VALID ENUM VALUES:');
    
    try {
      const testQuery = `
        INSERT INTO inventory (
          supplier_id, product_name, product_type, metal_type, form_type,
          jewel_type, jewel_category, with_stone_weight, without_stone_weight,
          with_stone_cost, without_stone_cost, with_stone_tunch_percentage,
          without_stone_tunch_percentage, procured_in_24k, balance_weight_24k, balance_weight_22k
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      // Use valid enum values based on what we see in the database
      const testParams = [
        1, // supplier_id
        'Test Item Valid Enums', // product_name
        'Ring', // product_type
        'Gold', // metal_type (should be valid)
        'Jewel', // form_type (should be valid)
        null, // jewel_type (use null to avoid enum issues)
        'Wedding', // jewel_category
        10.5, // with_stone_weight
        9.8, // without_stone_weight
        50000, // with_stone_cost
        45000, // without_stone_cost
        95.5, // with_stone_tunch_percentage
        96.2, // without_stone_tunch_percentage
        9.5, // procured_in_24k
        9.5, // balance_weight_24k
        8.7 // balance_weight_22k
      ];
      
      const [result] = await connection.execute(testQuery, testParams);
      console.log('✅ Test insert with valid enums successful!');
      console.log(`   Inserted ID: ${result.insertId}`);
      
      // Clean up test record
      await connection.execute('DELETE FROM inventory WHERE product_name = ?', ['Test Item Valid Enums']);
      console.log('✅ Test record cleaned up');
      
    } catch (error) {
      console.log(`❌ Test insert failed: ${error.message}`);
      
      // If it's an enum error, suggest fixes
      if (error.message.includes('Data truncated') || error.message.includes('enum')) {
        console.log('\n🔧 ENUM VALUE SUGGESTIONS:');
        console.log('The error suggests invalid enum values. Consider:');
        console.log('1. Using NULL for optional enum fields');
        console.log('2. Checking the exact enum values allowed');
        console.log('3. Updating the inventory model to use valid enum values');
      }
    }

    console.log('\n🎯 ENUM CHECK COMPLETE');
    console.log('======================');
    console.log('✅ Database schema is now ready');
    console.log('✅ Try adding an inventory item through the UI');
    console.log('✅ The 500 error should be resolved');

  } catch (error) {
    console.error('❌ Enum check failed:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the enum check
checkEnumValues();
