import { type NextRequest, NextResponse } from "next/server"
import { InventoryModel } from "@/lib/models/inventory"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search")
    const available = searchParams.get("available")

    let inventory
    if (search) {
      inventory = await InventoryModel.search(search)
    } else if (available === "true") {
      inventory = await InventoryModel.getAvailableItems()
    } else {
      inventory = await InventoryModel.getAll()
    }

    return NextResponse.json({ success: true, data: inventory })
  } catch (error) {
    console.error("Error fetching inventory:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch inventory" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.supplier_id || !data.product_name || !data.product_type) {
      return NextResponse.json(
        { success: false, error: "Supplier ID, product name, and product type are required" },
        { status: 400 },
      )
    }

    // Validate numeric fields
    if (data.with_stone_weight && (isNaN(data.with_stone_weight) || data.with_stone_weight < 0)) {
      return NextResponse.json(
        { success: false, error: "With stone weight must be a valid positive number" },
        { status: 400 },
      )
    }

    if (data.without_stone_weight && (isNaN(data.without_stone_weight) || data.without_stone_weight < 0)) {
      return NextResponse.json(
        { success: false, error: "Without stone weight must be a valid positive number" },
        { status: 400 },
      )
    }

    if (data.balance_weight_24k && (isNaN(data.balance_weight_24k) || data.balance_weight_24k < 0)) {
      return NextResponse.json(
        { success: false, error: "Balance weight 24K must be a valid positive number" },
        { status: 400 },
      )
    }

    const inventoryId = await InventoryModel.create(data)
    const inventory = await InventoryModel.getById(inventoryId)

    return NextResponse.json({ success: true, data: inventory }, { status: 201 })
  } catch (error) {
    console.error("Error creating inventory item:", error)
    return NextResponse.json({ success: false, error: "Failed to create inventory item" }, { status: 500 })
  }
}
