/**
 * Comprehensive Error Handling Utilities
 * Centralized error handling, logging, and user feedback
 */

export interface ErrorInfo {
  code: string
  message: string
  details?: any
  timestamp: Date
  component?: string
  action?: string
}

export interface LoadingState {
  isLoading: boolean
  loadingMessage?: string
  progress?: number
}

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export class ErrorHandler {
  
  // =====================================================
  // ERROR CLASSIFICATION
  // =====================================================
  
  static classifyError(error: any): ErrorInfo {
    const timestamp = new Date()
    
    // Network errors
    if (error.name === 'NetworkError' || error.message?.includes('fetch')) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network connection failed. Please check your internet connection.',
        details: error,
        timestamp
      }
    }
    
    // Database errors
    if (error.message?.includes('database') || error.code?.startsWith('ER_')) {
      return {
        code: 'DATABASE_ERROR',
        message: 'Database operation failed. Please try again.',
        details: error,
        timestamp
      }
    }
    
    // Validation errors
    if (error.name === 'ValidationError' || error.message?.includes('validation')) {
      return {
        code: 'VALIDATION_ERROR',
        message: error.message || 'Invalid data provided.',
        details: error,
        timestamp
      }
    }
    
    // Settings errors
    if (error.message?.includes('settings') || error.message?.includes('configuration')) {
      return {
        code: 'SETTINGS_ERROR',
        message: 'Settings configuration error. Using default values.',
        details: error,
        timestamp
      }
    }
    
    // Authentication errors
    if (error.status === 401 || error.message?.includes('unauthorized')) {
      return {
        code: 'AUTH_ERROR',
        message: 'Authentication required. Please log in.',
        details: error,
        timestamp
      }
    }
    
    // Permission errors
    if (error.status === 403 || error.message?.includes('forbidden')) {
      return {
        code: 'PERMISSION_ERROR',
        message: 'You do not have permission to perform this action.',
        details: error,
        timestamp
      }
    }
    
    // Generic error
    return {
      code: 'UNKNOWN_ERROR',
      message: error.message || 'An unexpected error occurred.',
      details: error,
      timestamp
    }
  }
  
  // =====================================================
  // ERROR LOGGING
  // =====================================================
  
  static logError(errorInfo: ErrorInfo, component?: string, action?: string): void {
    const logEntry = {
      ...errorInfo,
      component,
      action,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
      url: typeof window !== 'undefined' ? window.location.href : 'server'
    }
    
    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 Error in ${component || 'Unknown Component'}`)
      console.error('Code:', errorInfo.code)
      console.error('Message:', errorInfo.message)
      console.error('Action:', action || 'Unknown Action')
      console.error('Details:', errorInfo.details)
      console.error('Timestamp:', errorInfo.timestamp)
      console.groupEnd()
    }
    
    // In production, you would send this to a logging service
    // Example: sendToLoggingService(logEntry)
  }
  
  // =====================================================
  // USER-FRIENDLY ERROR MESSAGES
  // =====================================================
  
  static getUserMessage(errorInfo: ErrorInfo): string {
    const userMessages: { [key: string]: string } = {
      'NETWORK_ERROR': 'Connection problem. Please check your internet and try again.',
      'DATABASE_ERROR': 'Data operation failed. Please try again in a moment.',
      'VALIDATION_ERROR': errorInfo.message,
      'SETTINGS_ERROR': 'Settings issue detected. Using default values for now.',
      'AUTH_ERROR': 'Please log in to continue.',
      'PERMISSION_ERROR': 'You need permission to do this action.',
      'UNKNOWN_ERROR': 'Something went wrong. Please try again.'
    }
    
    return userMessages[errorInfo.code] || errorInfo.message
  }
  
  // =====================================================
  // RETRY LOGIC
  // =====================================================
  
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000,
    component?: string,
    action?: string
  ): Promise<T> {
    let lastError: any
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        const errorInfo = this.classifyError(error)
        
        // Don't retry validation or permission errors
        if (['VALIDATION_ERROR', 'AUTH_ERROR', 'PERMISSION_ERROR'].includes(errorInfo.code)) {
          throw error
        }
        
        if (attempt === maxRetries) {
          this.logError(errorInfo, component, action)
          throw error
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay * attempt))
      }
    }
    
    throw lastError
  }
  
  // =====================================================
  // LOADING STATE MANAGEMENT
  // =====================================================
  
  static createLoadingState(message?: string, progress?: number): LoadingState {
    return {
      isLoading: true,
      loadingMessage: message,
      progress
    }
  }
  
  static completeLoadingState(): LoadingState {
    return {
      isLoading: false
    }
  }
  
  // =====================================================
  // DATA VALIDATION
  // =====================================================
  
  static validateWeight(weight: any): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    
    if (typeof weight !== 'number') {
      errors.push('Weight must be a number')
    } else {
      if (weight <= 0) {
        errors.push('Weight must be greater than 0')
      }
      if (weight > 100000) {
        errors.push('Weight seems too large (max 100kg)')
      }
      if (weight > 10000) {
        warnings.push('Weight is very large, please verify')
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
  
  static validateTunch(tunch: any): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    
    if (typeof tunch !== 'number') {
      errors.push('Tunch must be a number')
    } else {
      if (tunch <= 0) {
        errors.push('Tunch must be greater than 0')
      }
      if (tunch > 100) {
        errors.push('Tunch cannot be greater than 100%')
      }
      if (tunch < 70) {
        warnings.push('Tunch is very low, please verify')
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
  
  static validateGoldRate(rate: any): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    
    if (typeof rate !== 'number') {
      errors.push('Gold rate must be a number')
    } else {
      if (rate <= 0) {
        errors.push('Gold rate must be greater than 0')
      }
      if (rate > 1000000) {
        errors.push('Gold rate seems too high')
      }
      if (rate < 1000) {
        warnings.push('Gold rate seems very low')
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
  
  // =====================================================
  // FORM VALIDATION
  // =====================================================
  
  static validateInventoryForm(data: any): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    
    // Required fields
    if (!data.product_name?.trim()) {
      errors.push('Product name is required')
    }
    
    if (!data.supplier_id) {
      errors.push('Supplier is required')
    }
    
    // Weight validations
    const weightValidation = this.validateWeight(data.with_stone_weight || data.without_stone_weight)
    errors.push(...weightValidation.errors)
    warnings.push(...weightValidation.warnings)
    
    // Wastage validation
    if (data.wastage_percentage && (data.wastage_percentage < 0 || data.wastage_percentage > 50)) {
      errors.push('Wastage percentage must be between 0% and 50%')
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
  
  static validateBillForm(data: any): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    
    // Required fields
    if (!data.customer_name?.trim()) {
      errors.push('Customer name is required')
    }
    
    if (!data.product_name?.trim()) {
      errors.push('Product name is required')
    }
    
    // Weight and tunch validations
    const weightValidation = this.validateWeight(data.without_stone)
    const tunchValidation = this.validateTunch(data.tunch_without_stone)
    
    errors.push(...weightValidation.errors, ...tunchValidation.errors)
    warnings.push(...weightValidation.warnings, ...tunchValidation.warnings)
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}

// Export utility functions for direct use
export const {
  classifyError,
  logError,
  getUserMessage,
  withRetry,
  createLoadingState,
  completeLoadingState,
  validateWeight,
  validateTunch,
  validateGoldRate,
  validateInventoryForm,
  validateBillForm
} = ErrorHandler
