#!/usr/bin/env node

require("dotenv").config({ path: ".env.local" })

const mysql = require("mysql2/promise")

async function testDatabase() {
  console.log("🗄️  Testing database connection...\n")

  const config = {
    host: process.env.DB_HOST || "localhost",
    port: Number.parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME,
  }

  console.log("📋 Connection Details:")
  console.log(`   Host: ${config.host}`)
  console.log(`   Port: ${config.port}`)
  console.log(`   User: ${config.user}`)
  console.log(`   Database: ${config.database}`)
  console.log("")

  try {
    // Test connection without database first
    console.log("🔌 Testing MySQL server connection...")
    const serverConnection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      user: config.user,
      password: config.password,
    })

    console.log("✅ MySQL server connection successful")

    // Check if database exists
    console.log("🔍 Checking if database exists...")
    const [databases] = await serverConnection.execute(`SHOW DATABASES LIKE '${config.database}'`)

    if (databases.length === 0) {
      console.log(`⚠️  Database '${config.database}' does not exist`)
      console.log("   Run: npm run db:create to create the database")
    } else {
      console.log(`✅ Database '${config.database}' exists`)

      // Test database connection
      console.log("🔌 Testing database connection...")
      const dbConnection = await mysql.createConnection(config)

      // Check tables
      console.log("📊 Checking database tables...")
      const [tables] = await dbConnection.execute("SHOW TABLES")

      if (tables.length === 0) {
        console.log("⚠️  No tables found in database")
        console.log("   Run: npm run db:create to create tables")
        console.log("   Run: npm run db:seed to add sample data")
      } else {
        console.log(`✅ Found ${tables.length} tables:`)
        tables.forEach((table) => {
          const tableName = Object.values(table)[0]
          console.log(`   - ${tableName}`)
        })

        // Test a simple query
        console.log("🧪 Testing sample query...")
        const [rows] = await dbConnection.execute("SELECT COUNT(*) as count FROM suppliers")
        console.log(`✅ Query successful - Found ${rows[0].count} suppliers`)
      }

      await dbConnection.end()
    }

    await serverConnection.end()

    console.log("\n🎉 Database test completed successfully!")
  } catch (error) {
    console.log("\n❌ Database test failed:")
    console.log(`   Error: ${error.message}`)

    if (error.code === "ER_ACCESS_DENIED_ERROR") {
      console.log("\n💡 Troubleshooting:")
      console.log("   - Check DB_USER and DB_PASSWORD in .env.local")
      console.log("   - Ensure MySQL user has proper privileges")
      console.log("   - Try: mysql -u root -p")
    } else if (error.code === "ECONNREFUSED") {
      console.log("\n💡 Troubleshooting:")
      console.log("   - Check if MySQL server is running")
      console.log("   - Verify DB_HOST and DB_PORT in .env.local")
      console.log("   - Try: sudo systemctl start mysql")
    } else if (error.code === "ER_BAD_DB_ERROR") {
      console.log("\n💡 Troubleshooting:")
      console.log("   - Database does not exist")
      console.log("   - Run: npm run db:create")
    }

    process.exit(1)
  }
}

testDatabase()
