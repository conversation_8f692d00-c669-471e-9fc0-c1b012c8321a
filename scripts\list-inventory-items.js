const mysql = require('mysql2/promise');

async function listInventoryItems() {
  console.log('📋 LISTING ALL INVENTORY ITEMS');
  console.log('==============================\n');

  let connection;
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'jewellery_wholesale'
    });

    console.log('✅ Connected to database');

    // Get all inventory items
    const [items] = await connection.execute(`
      SELECT id, product_name, product_type, procured_in_24k, 
             balance_weight_24k, balance_weight_22k, without_stone_weight,
             without_stone_cost, created_at
      FROM inventory 
      ORDER BY id DESC
    `);

    console.log(`\n📊 FOUND ${items.length} INVENTORY ITEMS:`);
    console.log('=====================================');

    if (items.length === 0) {
      console.log('❌ No inventory items found in database');
      console.log('This means the item was not saved properly.');
      return;
    }

    items.forEach((item, index) => {
      console.log(`\n${index + 1}. ITEM ID: ${item.id}`);
      console.log(`   Product Name: "${item.product_name}"`);
      console.log(`   Product Type: "${item.product_type}"`);
      console.log(`   Procured 24K: ${item.procured_in_24k}`);
      console.log(`   Balance 24K: ${item.balance_weight_24k}`);
      console.log(`   Balance 22K: ${item.balance_weight_22k}`);
      console.log(`   Without Stone Weight: ${item.without_stone_weight}`);
      console.log(`   Without Stone Cost: ${item.without_stone_cost}`);
      console.log(`   Created: ${item.created_at}`);
    });

    // Find the most recent item that matches your data
    const matchingItem = items.find(item => 
      item.without_stone_weight == 120.420 && 
      item.procured_in_24k == 113.195 &&
      item.without_stone_cost == 94
    );

    if (matchingItem) {
      console.log('\n🎯 FOUND MATCHING ITEM:');
      console.log('=======================');
      console.log(`ID: ${matchingItem.id}`);
      console.log(`Product: "${matchingItem.product_name}"`);
      console.log('This appears to be your Chain item.');
      
      console.log('\n🔧 FIXING THIS ITEM:');
      console.log('====================');
      
      await connection.execute(`
        UPDATE inventory 
        SET 
          balance_weight_24k = 9.754,
          balance_weight_22k = 110.260,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [matchingItem.id]);

      console.log('✅ Updated balance weights successfully!');
      
      // Verify the update
      const [updatedData] = await connection.execute(`
        SELECT balance_weight_24k, balance_weight_22k FROM inventory WHERE id = ?
      `, [matchingItem.id]);

      const updated = updatedData[0];
      console.log('\n📊 VERIFIED UPDATE:');
      console.log('===================');
      console.log(`Balance 24K: ${updated.balance_weight_24k} ✅`);
      console.log(`Balance 22K: ${updated.balance_weight_22k} ✅`);
      
    } else {
      console.log('\n❌ NO MATCHING ITEM FOUND');
      console.log('Looking for item with:');
      console.log('- without_stone_weight: 120.420');
      console.log('- procured_in_24k: 113.195');
      console.log('- without_stone_cost: 94');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the script
listInventoryItems();
