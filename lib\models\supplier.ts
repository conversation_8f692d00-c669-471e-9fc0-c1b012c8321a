import { executeQuery, executeSingle } from "../database"

export interface Supplier {
  id: number
  name: string
  location: string
  contact_person: string
  phone: string
  email: string
  address: string
  speciality: string
  total_purchases: number
  last_purchase_date: string
  status: "Active" | "Inactive"
  created_at: string
  updated_at: string
}

export interface CreateSupplierData {
  name: string
  location?: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  speciality?: string
}

export class SupplierModel {
  // Get all suppliers
  static async getAll(): Promise<Supplier[]> {
    const query = `
      SELECT * FROM suppliers 
      ORDER BY created_at DESC
    `
    return executeQuery<Supplier>(query)
  }

  // Get supplier by ID
  static async getById(id: number): Promise<Supplier | null> {
    const query = "SELECT * FROM suppliers WHERE id = ?"
    const results = await executeQuery<Supplier>(query, [id])
    return results[0] || null
  }

  // Create new supplier
  static async create(data: CreateSupplierData): Promise<number> {
    const query = `
      INSERT INTO suppliers (name, location, contact_person, phone, email, address, speciality)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `
    const params = [
      data.name,
      data.location || null,
      data.contact_person || null,
      data.phone || null,
      data.email || null,
      data.address || null,
      data.speciality || null,
    ]
    const result = await executeSingle(query, params)
    return result.insertId!
  }

  // Update supplier
  static async update(id: number, data: Partial<CreateSupplierData>): Promise<boolean> {
    const fields = Object.keys(data)
      .map((key) => `${key} = ?`)
      .join(", ")
    const values = Object.values(data)

    const query = `UPDATE suppliers SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`
    const result = await executeSingle(query, [...values, id])
    return result.affectedRows > 0
  }

  // Delete supplier
  static async delete(id: number): Promise<boolean> {
    const query = "DELETE FROM suppliers WHERE id = ?"
    const result = await executeSingle(query, [id])
    return result.affectedRows > 0
  }

  // Search suppliers
  static async search(searchTerm: string): Promise<Supplier[]> {
    const query = `
      SELECT * FROM suppliers 
      WHERE name LIKE ? OR location LIKE ? OR speciality LIKE ?
      ORDER BY name
    `
    const searchPattern = `%${searchTerm}%`
    return executeQuery<Supplier>(query, [searchPattern, searchPattern, searchPattern])
  }

  // Update total purchases
  static async updateTotalPurchases(id: number, amount: number): Promise<boolean> {
    const query = `
      UPDATE suppliers 
      SET total_purchases = total_purchases + ?, 
          last_purchase_date = CURRENT_DATE,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `
    const result = await executeSingle(query, [amount, id])
    return result.affectedRows > 0
  }
}
