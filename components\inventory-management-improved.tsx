"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Plus, Search, Edit, Trash2, Package, TrendingUp, IndianRupee, AlertTriangle } from "lucide-react"
import { useDatabase } from "@/hooks/use-database"
import { useDatabaseMutation } from "@/hooks/use-database-mutation"
import { useBusinessSettings } from "@/hooks/use-database-settings"
import { useSimpleToast } from "@/hooks/use-simple-toast"
import EditInventoryDialog from "@/components/edit-inventory-dialog"

interface InventoryItem {
  id: number
  supplier_id: number
  supplier_name?: string
  supplier_location?: string
  supplier_contact_person?: string
  product_name: string
  product_type: string
  metal_type: "Gold" | "Silver" | "Platinum"
  form_type: "Bar" | "Jewel" | "Old Jewel"
  jewel_type?: "With Stone" | "Without Stone"
  jewel_category?: string
  with_stone_weight: number
  without_stone_weight: number
  stone_weight?: number
  with_stone_cost: number
  without_stone_cost: number
  with_stone_tunch_percentage?: number
  without_stone_tunch_percentage?: number
  procured_in_24k: number
  wastage_percentage?: number
  expected_processing_loss?: number
  sold_value_with_stone: number
  sold_value_without_stone: number
  balance_weight_24k: number
  balance_weight_22k: number
  making_charges?: number
  created_at: string
  updated_at: string
  status: "Available" | "Low Stock" | "Out of Stock"
}

interface Supplier {
  id: number
  name: string
  location: string
  contact_person: string
  phone: string
  speciality: string
}

// Metal Information Constants
const METAL_TYPES = ["Gold", "Silver", "Platinum"] as const
const FORM_TYPES = ["Bar", "Jewel", "Old Jewel"] as const
const JEWEL_TYPES = ["With Stone", "Without Stone"] as const
const JEWEL_CATEGORIES = [
  "Bangle", "Ring", "Chain", "Necklace", "Studs", "Pendant",
  "Bracelet", "Mangalsutra", "Nosepin", "Vaddanam", "Choker",
  "Earrings", "Haram", "Anklet", "Others"
] as const

export default function InventoryManagementImproved() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newItem, setNewItem] = useState<Partial<InventoryItem>>({
    metal_type: "Gold",
    form_type: "Jewel",
    jewel_type: "Without Stone",
    wastage_percentage: 0 // Will be set by useEffect
  })
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null)
  const [editingItem, setEditingItem] = useState<InventoryItem | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  const { data: inventory, refetch } = useDatabase<InventoryItem>("inventory", searchTerm)
  const { data: suppliers } = useDatabase<Supplier>("suppliers")
  const { mutate, loading: mutating } = useDatabaseMutation<InventoryItem>()
  const { settings: businessSettings, getWastageRate } = useBusinessSettings()
  const { toast } = useSimpleToast()



  const filteredInventory = (inventory || []).filter(
    (item) =>
      (item.supplier_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.product_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.supplier_location || '').toLowerCase().includes(searchTerm.toLowerCase()),
  )



  // Initialize wastage percentage when settings are loaded
  useEffect(() => {
    if (newItem.form_type) {
      const defaultWastage = getWastageRate(newItem.form_type)
      // Update wastage percentage if it's 0 or if settings have changed
      if (newItem.wastage_percentage === 0 || newItem.wastage_percentage === undefined) {
        setNewItem(prev => ({
          ...prev,
          wastage_percentage: defaultWastage
        }))
      }
    }
  }, [businessSettings.wastage_rate_jewel, businessSettings.wastage_rate_bar, businessSettings.wastage_rate_old_jewel]) // React to settings changes

  const handleSupplierSelect = (supplierId: string) => {
    const supplier = (suppliers || []).find((s) => s.id === Number.parseInt(supplierId))
    if (supplier) {
      setSelectedSupplier(supplier)
      setNewItem({
        ...newItem,
        supplier_id: supplier.id,
        supplier_name: supplier.name,
        supplier_location: supplier.location,
        supplier_contact_person: supplier.contact_person,
      })
    }
  }

  const handleAddItem = async () => {
    if (!selectedSupplier || !newItem.product_name || !newItem.metal_type || !newItem.form_type) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields (Supplier, Product Name, Metal Type, and Form Type).",
        variant: "destructive",
      })
      return
    }

    try {
      const inventoryData = {
        supplier_id: selectedSupplier.id,
        product_name: newItem.product_name,
        product_type: newItem.product_type,
        metal_type: newItem.metal_type,
        form_type: newItem.form_type,
        jewel_type: newItem.form_type === "Jewel" ? newItem.jewel_type : undefined,
        jewel_category: newItem.form_type === "Jewel" && newItem.jewel_category ? newItem.jewel_category : undefined,
        with_stone_weight: newItem.with_stone_weight || 0,
        without_stone_weight: newItem.without_stone_weight || 0,
        with_stone_cost: newItem.with_stone_cost || 0,
        without_stone_cost: newItem.without_stone_cost || 0,
        with_stone_tunch_percentage: newItem.with_stone_tunch_percentage,
        without_stone_tunch_percentage: newItem.without_stone_tunch_percentage,
        procured_in_24k: newItem.procured_in_24k || 0,
        balance_weight_24k: newItem.balance_weight_24k,
        balance_weight_22k: newItem.balance_weight_22k,
        wastage_percentage: newItem.wastage_percentage,
        expected_processing_loss: newItem.expected_processing_loss,
      }

      const result = await mutate("inventory", "POST", inventoryData)
      if (result) {
        toast({
          title: "Item Added Successfully",
          description: `"${newItem.product_name}" has been added to inventory.`,
          variant: "default",
        })

        const defaultWastage = getWastageRate("Jewel")
        setNewItem({
          metal_type: "Gold",
          form_type: "Jewel",
          jewel_type: "Without Stone",
          wastage_percentage: defaultWastage
        })
        setSelectedSupplier(null)
        setIsAddDialogOpen(false)

        // Force refresh the inventory list
        await refetch()
      } else {
        toast({
          title: "Add Failed",
          description: "Failed to add the inventory item. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Add Error",
        description: "An error occurred while adding the item.",
        variant: "destructive",
      })
    }
  }

  const handleEditItem = (item: InventoryItem) => {
    setEditingItem(item)
    setIsEditDialogOpen(true)
  }

  const handleDeleteItem = async (id: number) => {
    // Find the item name for the success message
    const item = inventory?.find(i => i.id === id)
    const itemName = item?.product_name || `Item #${id}`

    if (confirm(`Are you sure you want to delete "${itemName}"?`)) {
      try {
        const result = await mutate(`inventory/${id}`, "DELETE")

        if (result) {
          toast({
            title: "Item Deleted",
            description: `"${itemName}" has been successfully deleted.`,
            variant: "default",
          })
          // Force refresh the inventory list
          await refetch()
        } else {
          toast({
            title: "Delete Failed",
            description: "Failed to delete the inventory item. Please try again.",
            variant: "destructive",
          })
        }
      } catch (error) {
        toast({
          title: "Delete Error",
          description: "An error occurred while deleting the item.",
          variant: "destructive",
        })
      }
    }
  }

  const totalInventoryValue = (inventory || []).reduce((sum, item) => sum + (Number(item.balance_weight_24k) || 0) * 6890, 0)
  const lowStockItems = (inventory || []).filter((item) => item.status === "Low Stock").length
  const availableItems = (inventory || []).filter((item) => item.status === "Available").length

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(inventory || []).length}</div>
            <p className="text-xs text-muted-foreground">Inventory items</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Stock</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{availableItems}</div>
            <p className="text-xs text-muted-foreground">Items in stock</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inventory Value</CardTitle>
            <IndianRupee className="h-4 w-4 text-amber-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{(totalInventoryValue / 100000).toFixed(1)}L</div>
            <p className="text-xs text-muted-foreground">Total stock value</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock Alert</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{lowStockItems}</div>
            <p className="text-xs text-muted-foreground">Items need reorder</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Inventory Management</CardTitle>
              <CardDescription>Manage your jewellery inventory with supplier selection - Updated</CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Item
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Add New Inventory Item</DialogTitle>
                  <DialogDescription>Select supplier and enter product details</DialogDescription>
                </DialogHeader>
                <div className="grid gap-6 py-4 max-h-[70vh] overflow-y-auto px-1">
                  <div className="space-y-2">
                    <Label>Select Supplier</Label>
                    <Select onValueChange={handleSupplierSelect}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose existing supplier" />
                      </SelectTrigger>
                      <SelectContent>
                        {(suppliers || []).map((supplier) => (
                          <SelectItem key={supplier.id} value={supplier.id.toString()}>
                            <div className="flex flex-col">
                              <span className="font-medium">{supplier.name}</span>
                              <span className="text-sm text-muted-foreground">
                                {supplier.location} • {supplier.speciality}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedSupplier && (
                    <Card className="border-blue-200 bg-blue-50">
                      <CardHeader>
                        <CardTitle className="text-lg">Selected Supplier</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm font-medium">Supplier: {selectedSupplier.name}</p>
                            <p className="text-sm text-muted-foreground">Location: {selectedSupplier.location}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Speciality: {selectedSupplier.speciality}</p>
                            <p className="text-sm text-muted-foreground">Contact: {selectedSupplier.contact_person}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="productName">Product Name</Label>
                    <Input
                      id="productName"
                      value={newItem.product_name || ""}
                      onChange={(e) => setNewItem({ ...newItem, product_name: e.target.value })}
                      placeholder="Enter product name (e.g., Gold Chain, Diamond Ring, etc.)"
                    />
                  </div>

                  {/* Metal Information Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-4 text-amber-700">Metal Information</h3>

                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="space-y-2">
                        <Label htmlFor="metalType">Metal Type</Label>
                        <Select
                          value={newItem.metal_type || "Gold"}
                          onValueChange={(value) => setNewItem({ ...newItem, metal_type: value as any })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select metal type" />
                          </SelectTrigger>
                          <SelectContent>
                            {METAL_TYPES.map((type) => (
                              <SelectItem key={type} value={type}>{type}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="formType">Form Type</Label>
                        <Select
                          value={newItem.form_type || "Jewel"}
                          onValueChange={(value) => {
                            const defaultWastage = getWastageRate(value)
                            setNewItem({
                              ...newItem,
                              form_type: value as any,
                              jewel_type: value === "Jewel" ? "Without Stone" : undefined,
                              jewel_category: value === "Jewel" ? undefined : undefined,
                              wastage_percentage: defaultWastage
                            })
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select form type" />
                          </SelectTrigger>
                          <SelectContent>
                            {FORM_TYPES.map((type) => (
                              <SelectItem key={type} value={type}>{type}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Jewel-specific fields */}
                    {newItem.form_type === "Jewel" && (
                      <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                        <h4 className="font-medium mb-3 text-amber-800">Jewel Type Selection</h4>
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div className="space-y-2">
                            <Label htmlFor="jewelType">Jewel Type</Label>
                            <Select
                              value={newItem.jewel_type || "Without Stone"}
                              onValueChange={(value) => setNewItem({ ...newItem, jewel_type: value as any })}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select jewel type" />
                              </SelectTrigger>
                              <SelectContent>
                                {JEWEL_TYPES.map((type) => (
                                  <SelectItem key={type} value={type}>{type}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="jewelCategory">Jewel Category</Label>
                            <Select
                              value={newItem.jewel_category || ""}
                              onValueChange={(value) => setNewItem({
                                ...newItem,
                                jewel_category: value,
                                product_type: value !== "Others" ? value : newItem.product_type
                              })}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select jewel category" />
                              </SelectTrigger>
                              <SelectContent>
                                {JEWEL_CATEGORIES.map((category) => (
                                  <SelectItem key={category} value={category}>{category}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        {newItem.jewel_category === "Others" && (
                          <div className="space-y-2">
                            <Label htmlFor="customCategory">Custom Category</Label>
                            <Input
                              id="customCategory"
                              value={newItem.product_type || ""}
                              onChange={(e) => setNewItem({ ...newItem, product_type: e.target.value })}
                              placeholder="Enter custom jewel category"
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Physical Weights Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-4 text-blue-700">Physical Weights (g)</h3>

                    <div className="grid grid-cols-1 gap-4 mb-4">
                      <div className="space-y-2">
                        <Label htmlFor="grossWeight">
                          Weight (g) <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="grossWeight"
                          type="number"
                          step="0.001"
                          value={newItem.form_type === "Bar" || newItem.jewel_type === "Without Stone"
                            ? (newItem.without_stone_weight || "")
                            : (newItem.with_stone_weight || "")
                          }
                          onChange={(e) => {
                            const value = Number.parseFloat(e.target.value) || 0
                            if (newItem.form_type === "Bar" || newItem.jewel_type === "Without Stone") {
                              setNewItem({ ...newItem, without_stone_weight: value, with_stone_weight: 0 })
                            } else {
                              setNewItem({ ...newItem, with_stone_weight: value })
                            }
                          }}
                          placeholder="120.420"
                          className="text-center"
                        />
                        <p className="text-xs text-muted-foreground text-center">
                          Total weight of the item
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Cost Information Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-4 text-green-700">Cost Information</h3>

                    <div className="grid grid-cols-1 gap-4 mb-4">
                      <div className="space-y-2">
                        <Label htmlFor="costPercentage">
                          {newItem.form_type === "Bar"
                            ? "Without Stone Cost (%)"
                            : newItem.jewel_type === "With Stone"
                              ? "With Stone Cost (%)"
                              : "Without Stone Cost (%)"}
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="costPercentage"
                          type="number"
                          step="0.01"
                          value={newItem.form_type === "Bar" || newItem.jewel_type === "Without Stone"
                            ? (newItem.without_stone_cost || "")
                            : (newItem.with_stone_cost || "")
                          }
                          onChange={(e) => {
                            const value = Number.parseFloat(e.target.value) || 0
                            if (newItem.form_type === "Bar" || newItem.jewel_type === "Without Stone") {
                              setNewItem({ ...newItem, without_stone_cost: value, with_stone_cost: 0 })
                            } else {
                              setNewItem({ ...newItem, with_stone_cost: value })
                            }
                          }}
                          placeholder="94.00"
                          className="text-center"
                        />
                        <p className="text-xs text-muted-foreground text-center">
                          Cost percentage for procurement pricing
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Gold Weights Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-4 text-amber-700">Gold Weights (g)</h3>

                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="space-y-2">
                        <Label htmlFor="procuredIn24k">
                          Procured Weight 24K <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="procuredIn24k"
                          type="number"
                          step="0.001"
                          value={newItem.procured_in_24k || ""}
                          onChange={(e) => {
                            const procured24k = Number.parseFloat(e.target.value) || 0
                            setNewItem({
                              ...newItem,
                              procured_in_24k: procured24k,
                              balance_weight_24k: procured24k, // Auto-set balance weight
                              balance_weight_22k: Number((procured24k * 0.916).toFixed(3)) // Auto-calculate 22K
                            })
                          }}
                          placeholder="113.195"
                          className="text-center"
                        />
                        <p className="text-xs text-muted-foreground text-center">
                          Total procured weight
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="balanceWeight24k">Balance Weight 24K</Label>
                        <Input
                          id="balanceWeight24k"
                          type="number"
                          step="0.001"
                          value={newItem.balance_weight_24k || ""}
                          onChange={(e) => {
                            const balance24k = Number.parseFloat(e.target.value) || 0
                            setNewItem({
                              ...newItem,
                              balance_weight_24k: balance24k,
                              balance_weight_22k: Number((balance24k * 0.916).toFixed(3)) // Auto-calculate 22K
                            })
                          }}
                          placeholder="9.754"
                          className="text-center"
                        />
                        <p className="text-xs text-muted-foreground text-center">
                          Actual balance in 24K
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="balanceWeight22k">Balance Weight 22K</Label>
                        <Input
                          id="balanceWeight22k"
                          type="number"
                          step="0.001"
                          value={newItem.balance_weight_22k || ""}
                          onChange={(e) => setNewItem({ ...newItem, balance_weight_22k: Number.parseFloat(e.target.value) || 0 })}
                          placeholder="110.260"
                          className="text-center"
                        />
                        <p className="text-xs text-muted-foreground text-center">
                          Actual balance in 22K
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Business Parameters Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-4 text-purple-700">Business Parameters</h3>

                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="space-y-2">
                        <Label htmlFor="withStoneTunch">With Stone Tunch (%)</Label>
                        <Input
                          id="withStoneTunch"
                          type="number"
                          step="0.01"
                          value={newItem.with_stone_tunch_percentage || ""}
                          onChange={(e) => setNewItem({ ...newItem, with_stone_tunch_percentage: Number.parseFloat(e.target.value) || 0 })}
                          placeholder="93.00"
                          className="text-center"
                        />
                        <p className="text-xs text-muted-foreground text-center">
                          Tunch percentage for with stone
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="withoutStoneTunch">Without Stone Tunch (%)</Label>
                        <Input
                          id="withoutStoneTunch"
                          type="number"
                          step="0.01"
                          value={newItem.without_stone_tunch_percentage || ""}
                          onChange={(e) => setNewItem({ ...newItem, without_stone_tunch_percentage: Number.parseFloat(e.target.value) || 0 })}
                          placeholder="94.00"
                          className="text-center"
                        />
                        <p className="text-xs text-muted-foreground text-center">
                          Tunch percentage for without stone
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="space-y-2">
                        <Label htmlFor="wastagePercentage">Wastage Percentage (%)</Label>
                        <Input
                          id="wastagePercentage"
                          type="number"
                          step="0.01"
                          value={newItem.wastage_percentage || ""}
                          onChange={(e) => setNewItem({ ...newItem, wastage_percentage: Number.parseFloat(e.target.value) || 0 })}
                          placeholder="2.00"
                          className="text-center"
                        />
                        <p className="text-xs text-muted-foreground text-center">
                          Expected wastage percentage
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="processingLoss">Processing Loss (g)</Label>
                        <Input
                          id="processingLoss"
                          type="number"
                          step="0.001"
                          value={newItem.expected_processing_loss || ""}
                          onChange={(e) => setNewItem({ ...newItem, expected_processing_loss: Number.parseFloat(e.target.value) || 0 })}
                          placeholder="0.000"
                          className="text-center"
                        />
                        <p className="text-xs text-muted-foreground text-center">
                          Expected processing loss in grams
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-4 mb-4">
                      <div className="space-y-2">
                        <Label htmlFor="makingCharges">Making Charges (₹)</Label>
                        <Input
                          id="makingCharges"
                          type="number"
                          step="0.01"
                          value={newItem.making_charges || ""}
                          onChange={(e) => setNewItem({ ...newItem, making_charges: Number.parseFloat(e.target.value) || 0 })}
                          placeholder="5000.00"
                          className="text-center"
                        />
                        <p className="text-xs text-muted-foreground text-center">
                          Making charges in rupees
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Smart Calculations Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-4 text-indigo-700">Smart Calculations</h3>

                    <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-200 mb-4">
                      <h4 className="font-medium mb-3 text-indigo-800">Auto-calculated Values:</h4>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-indigo-600">Stone Weight:</span>
                          <div className="font-medium">0.000g</div>
                        </div>
                        <div>
                          <span className="text-indigo-600">Processing Loss:</span>
                          <div className="font-medium">0.000g</div>
                        </div>
                        <div>
                          <span className="text-indigo-600">Available Stock 24K:</span>
                          <div className="font-medium">0.000g</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-200 mb-4">
                      <h4 className="font-medium mb-3 text-green-800">Expected Values:</h4>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-green-600">Processing Loss:</span>
                          <div className="font-medium">0.000g</div>
                        </div>
                        <div>
                          <span className="text-green-600">Expected Yield:</span>
                          <div className="font-medium">0.000g</div>
                        </div>
                        <div>
                          <span className="text-green-600">Available Stock 22K:</span>
                          <div className="font-medium">0.000g</div>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Calculate 24K from Tunch
                          const weight = newItem.without_stone_weight || 0
                          const tunch = newItem.without_stone_tunch_percentage || 94
                          const calculated24k = (weight * tunch) / 100
                          setNewItem({
                            ...newItem,
                            procured_in_24k: Number(calculated24k.toFixed(3)),
                            balance_weight_24k: Number(calculated24k.toFixed(3))
                          })
                        }}
                      >
                        Calculate 24K from Tunch
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Calculate Stone Weight
                          const grossWeight = newItem.with_stone_weight || 0
                          const netWeight = newItem.without_stone_weight || 0
                          const stoneWeight = grossWeight - netWeight
                          setNewItem({ ...newItem, stone_weight: Math.max(0, stoneWeight) })
                        }}
                      >
                        Calculate Stone Weight
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Calculate 22K Balance
                          const balance24k = newItem.balance_weight_24k || 0
                          const balance22k = balance24k * 0.916
                          setNewItem({ ...newItem, balance_weight_22k: Number(balance22k.toFixed(3)) })
                        }}
                      >
                        Calculate 22K Balance
                      </Button>
                    </div>
                  </div>

                  {/* Summary Section */}
                  {selectedSupplier && newItem.product_name && (
                    <div className="border-t pt-4">
                      <div className="p-4 bg-green-50 rounded-lg space-y-2">
                        <h4 className="font-semibold text-green-800">Inventory Summary</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p>
                              <span className="font-medium">Supplier:</span> {selectedSupplier.name}
                            </p>
                            <p>
                              <span className="font-medium">Product:</span> {newItem.product_name}
                            </p>
                            <p>
                              <span className="font-medium">Type:</span> {newItem.product_type || newItem.jewel_category}
                            </p>
                            <p>
                              <span className="font-medium">Metal:</span> {newItem.metal_type} ({newItem.form_type})
                            </p>
                          </div>
                          <div>
                            <p>
                              <span className="font-medium">Gross Weight:</span>{" "}
                              {(newItem.form_type === "Bar" || newItem.jewel_type === "Without Stone"
                                ? (newItem.without_stone_weight || 0)
                                : (newItem.with_stone_weight || 0)
                              ).toFixed(3)}g
                            </p>
                            <p>
                              <span className="font-medium">24K Procured:</span>{" "}
                              {(newItem.procured_in_24k || 0).toFixed(3)}g
                            </p>
                            <p>
                              <span className="font-medium">24K Balance:</span>{" "}
                              {(newItem.balance_weight_24k || 0).toFixed(3)}g
                            </p>
                            <p>
                              <span className="font-medium">22K Balance:</span>{" "}
                              {(newItem.balance_weight_22k || 0).toFixed(3)}g
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="flex justify-end gap-2 pt-4 border-t bg-white sticky bottom-0">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsAddDialogOpen(false)
                      setSelectedSupplier(null)
                      const defaultWastage = getWastageRate("Jewel")
                      setNewItem({
                        metal_type: "Gold",
                        form_type: "Jewel",
                        jewel_type: "Without Stone",
                        wastage_percentage: defaultWastage
                      })
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleAddItem}
                    disabled={
                      !selectedSupplier ||
                      !newItem.product_name ||
                      !newItem.metal_type ||
                      !newItem.form_type ||
                      (newItem.form_type === "Jewel" && !newItem.jewel_type) ||
                      (newItem.form_type === "Jewel" && !newItem.jewel_category) ||
                      !newItem.procured_in_24k ||
                      !newItem.balance_weight_24k ||
                      (newItem.form_type === "Bar" || newItem.jewel_type === "Without Stone"
                        ? (!newItem.without_stone_weight || !newItem.without_stone_cost)
                        : (!newItem.with_stone_weight || !newItem.with_stone_cost))
                    }
                    className="bg-green-600 hover:bg-green-700"
                  >
                    Add Item
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search inventory..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Badge variant="secondary">{(filteredInventory || []).length} items</Badge>
          </div>

          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Sl.No</TableHead>
                    <TableHead>Supplier Details</TableHead>
                    <TableHead>Product Details</TableHead>
                    <TableHead>Metal Info</TableHead>
                    <TableHead>Weight (g)</TableHead>
                    <TableHead>Cost Price (%)</TableHead>
                    <TableHead>Stock Status</TableHead>
                    <TableHead className="w-32">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {(filteredInventory || []).map((item, index) => (
                    <TableRow key={item.id}>
                      <TableCell>{index + 1}</TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{item.supplier_name}</p>
                          <p className="text-sm text-muted-foreground">{item.supplier_location}</p>
                          <p className="text-sm text-blue-600">{item.supplier_contact_person}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium text-amber-600">{item.product_name}</p>
                          {item.product_type && (
                            <p className="text-sm text-muted-foreground">{item.product_type}</p>
                          )}
                          <p className="text-xs text-gray-500">Added: {new Date(item.created_at).toLocaleDateString()}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <div className="flex items-center gap-1">
                            <Badge variant="outline" className="text-xs">
                              {item.metal_type || "Gold"}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {item.form_type || "Jewel"}
                            </Badge>
                          </div>
                          {item.form_type === "Jewel" && (
                            <div className="space-y-1">
                              <p className="text-xs text-muted-foreground">
                                {item.jewel_type || "Without Stone"}
                              </p>
                              {item.jewel_category && (
                                <p className="text-xs font-medium text-amber-600">
                                  {item.jewel_category}
                                </p>
                              )}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <p>24K: {(Number(item.balance_weight_24k) || 0).toFixed(3)}g</p>
                          <p>22K: {(Number(item.balance_weight_22k) || 0).toFixed(3)}g</p>
                          <p>Procured: {(Number(item.procured_in_24k) || 0).toFixed(3)}g</p>
                          {item.jewel_type === "With Stone" && (
                            <p className="text-xs text-muted-foreground">
                              Gross: {(Number(item.with_stone_weight) || 0).toFixed(3)}g |
                              Net: {(Number(item.without_stone_weight) || 0).toFixed(3)}g
                            </p>
                          )}
                          {item.jewel_type === "Without Stone" && (
                            <p className="text-xs text-muted-foreground">
                              Weight: {(Number(item.without_stone_weight) || 0).toFixed(3)}g
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <p>With Stone: {Number(item.with_stone_cost || 0).toFixed(2)}%</p>
                          <p>Without: {Number(item.without_stone_cost || 0).toFixed(2)}%</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            item.status === "Available"
                              ? "default"
                              : item.status === "Low Stock"
                                ? "secondary"
                                : "destructive"
                          }
                        >
                          {item.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="w-32">
                        <div className="flex items-center gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditItem(item)}
                            className="h-8 w-8 p-0 bg-blue-50 hover:bg-blue-100 border-blue-200"
                            title="Edit Item"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleDeleteItem(item.id)}
                            disabled={mutating}
                            title="Delete Item"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
      <EditInventoryDialog
        item={editingItem}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSuccess={refetch}
      />
    </div>
  )
}
