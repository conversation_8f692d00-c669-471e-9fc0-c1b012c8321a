"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Plus, Search, Edit, Trash2, Package, TrendingUp, IndianRupee, AlertTriangle } from "lucide-react"
import { useDatabase } from "@/hooks/use-database"
import { useDatabaseMutation } from "@/hooks/use-database-mutation"
import { useBusinessSettings } from "@/hooks/use-database-settings"
import { useSimpleToast } from "@/hooks/use-simple-toast"
import EditInventoryDialog from "@/components/edit-inventory-dialog"

interface InventoryItem {
  id: number
  supplier_id: number
  supplier_name?: string
  supplier_location?: string
  supplier_contact_person?: string
  product_name: string
  product_type: string
  metal_type: "Gold" | "Silver" | "Platinum"
  form_type: "Bar" | "Jewel" | "Old Jewel"
  jewel_type?: "With Stone" | "Without Stone"
  jewel_category?: string
  with_stone_weight: number
  without_stone_weight: number
  stone_weight?: number
  with_stone_cost: number
  without_stone_cost: number
  procured_in_24k: number
  wastage_percentage?: number
  expected_processing_loss?: number
  sold_value_with_stone: number
  sold_value_without_stone: number
  balance_weight_24k: number
  balance_weight_22k: number
  created_at: string
  status: "Available" | "Sold" | "Reserved"
}

interface Supplier {
  id: number
  name: string
  location: string
  contact_person: string
  phone: string
  speciality: string
}

// Metal Information Constants
const METAL_TYPES = ["Gold", "Silver", "Platinum"] as const
const FORM_TYPES = ["Bar", "Jewel", "Old Jewel"] as const
const JEWEL_TYPES = ["With Stone", "Without Stone"] as const
const JEWEL_CATEGORIES = [
  "Bangle", "Ring", "Chain", "Necklace", "Studs", "Pendant",
  "Bracelet", "Mangalsutra", "Nosepin", "Vaddanam", "Choker",
  "Earrings", "Haram", "Anklet", "Others"
] as const

export default function InventoryManagementImproved() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newItem, setNewItem] = useState<Partial<InventoryItem>>({
    metal_type: "Gold",
    form_type: "Jewel",
    jewel_type: "Without Stone",
    wastage_percentage: 0 // Will be set by useEffect
  })
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null)
  const [editingItem, setEditingItem] = useState<InventoryItem | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  const { data: inventory, loading, error, refetch } = useDatabase<InventoryItem>("inventory", searchTerm)
  const { data: suppliers } = useDatabase<Supplier>("suppliers")
  const { mutate, loading: mutating } = useDatabaseMutation<InventoryItem>()
  const { settings: businessSettings, getWastageRate, loading: settingsLoading } = useBusinessSettings()
  const { toast } = useSimpleToast()



  const filteredInventory = (inventory || []).filter(
    (item) =>
      (item.supplier_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.product_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.supplier_location || '').toLowerCase().includes(searchTerm.toLowerCase()),
  )



  // Initialize wastage percentage when settings are loaded
  useEffect(() => {
    if (!settingsLoading && newItem.form_type) {
      const defaultWastage = getWastageRate(newItem.form_type)
      // Update wastage percentage if it's 0 or if settings have changed
      if (newItem.wastage_percentage === 0 || newItem.wastage_percentage === undefined) {
        setNewItem(prev => ({
          ...prev,
          wastage_percentage: defaultWastage
        }))
      }
    }
  }, [settingsLoading, businessSettings.wastage_rate_jewel, businessSettings.wastage_rate_bar, businessSettings.wastage_rate_old_jewel]) // React to settings changes

  const handleSupplierSelect = (supplierId: string) => {
    const supplier = (suppliers || []).find((s) => s.id === Number.parseInt(supplierId))
    if (supplier) {
      setSelectedSupplier(supplier)
      setNewItem({
        ...newItem,
        supplier_id: supplier.id,
        supplier_name: supplier.name,
        supplier_location: supplier.location,
        supplier_contact_person: supplier.contact_person,
      })
    }
  }

  const handleAddItem = async () => {
    if (!selectedSupplier || !newItem.product_name || !newItem.metal_type || !newItem.form_type) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields (Supplier, Product Name, Metal Type, and Form Type).",
        variant: "destructive",
      })
      return
    }

    try {
      const inventoryData = {
        supplier_id: selectedSupplier.id,
        product_name: newItem.product_name,
        product_type: newItem.product_type,
        metal_type: newItem.metal_type,
        form_type: newItem.form_type,
        jewel_type: newItem.form_type === "Jewel" ? newItem.jewel_type : undefined,
        jewel_category: newItem.form_type === "Jewel" && newItem.jewel_category ? newItem.jewel_category : undefined,
        with_stone_weight: newItem.with_stone_weight || 0,
        without_stone_weight: newItem.without_stone_weight || 0,
        with_stone_cost: newItem.with_stone_cost || 0,
        without_stone_cost: newItem.without_stone_cost || 0,
        procured_in_24k: newItem.procured_in_24k || 0,
      }

      const result = await mutate("inventory", "POST", inventoryData)
      if (result) {
        toast({
          title: "Item Added Successfully",
          description: `"${newItem.product_name}" has been added to inventory.`,
          variant: "default",
        })

        const defaultWastage = getWastageRate("Jewel")
        setNewItem({
          metal_type: "Gold",
          form_type: "Jewel",
          jewel_type: "Without Stone",
          wastage_percentage: defaultWastage
        })
        setSelectedSupplier(null)
        setIsAddDialogOpen(false)

        // Force refresh the inventory list
        await refetch()
      } else {
        toast({
          title: "Add Failed",
          description: "Failed to add the inventory item. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Add Error",
        description: "An error occurred while adding the item.",
        variant: "destructive",
      })
    }
  }

  const handleEditItem = (item: InventoryItem) => {
    setEditingItem(item)
    setIsEditDialogOpen(true)
  }

  const handleDeleteItem = async (id: number) => {
    // Find the item name for the success message
    const item = inventory?.find(i => i.id === id)
    const itemName = item?.product_name || `Item #${id}`

    if (confirm(`Are you sure you want to delete "${itemName}"?`)) {
      try {
        const result = await mutate(`inventory/${id}`, "DELETE")

        if (result && (result as any).deleted) {
          toast({
            title: "Item Deleted",
            description: `"${itemName}" has been successfully deleted.`,
            variant: "default",
          })
          // Force refresh the inventory list
          await refetch()
        } else {
          toast({
            title: "Delete Failed",
            description: "Failed to delete the inventory item. Please try again.",
            variant: "destructive",
          })
        }
      } catch (error) {
        toast({
          title: "Delete Error",
          description: "An error occurred while deleting the item.",
          variant: "destructive",
        })
      }
    }
  }

  const totalInventoryValue = (inventory || []).reduce((sum, item) => sum + (Number(item.balance_weight_24k) || 0) * 6890, 0)
  const lowStockItems = (inventory || []).filter((item) => item.status === "Low Stock").length
  const availableItems = (inventory || []).filter((item) => item.status === "Available").length

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(inventory || []).length}</div>
            <p className="text-xs text-muted-foreground">Inventory items</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Stock</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{availableItems}</div>
            <p className="text-xs text-muted-foreground">Items in stock</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inventory Value</CardTitle>
            <IndianRupee className="h-4 w-4 text-amber-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{(totalInventoryValue / 100000).toFixed(1)}L</div>
            <p className="text-xs text-muted-foreground">Total stock value</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock Alert</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{lowStockItems}</div>
            <p className="text-xs text-muted-foreground">Items need reorder</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Inventory Management</CardTitle>
              <CardDescription>Manage your jewellery inventory with supplier selection</CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Item
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Add New Inventory Item</DialogTitle>
                  <DialogDescription>Select supplier and enter product details</DialogDescription>
                </DialogHeader>
                <div className="grid gap-6 py-4 max-h-[70vh] overflow-y-auto px-1">
                  <div className="space-y-2">
                    <Label>Select Supplier</Label>
                    <Select onValueChange={handleSupplierSelect}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose existing supplier" />
                      </SelectTrigger>
                      <SelectContent>
                        {(suppliers || []).map((supplier) => (
                          <SelectItem key={supplier.id} value={supplier.id.toString()}>
                            <div className="flex flex-col">
                              <span className="font-medium">{supplier.name}</span>
                              <span className="text-sm text-muted-foreground">
                                {supplier.location} • {supplier.speciality}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedSupplier && (
                    <Card className="border-blue-200 bg-blue-50">
                      <CardHeader>
                        <CardTitle className="text-lg">Selected Supplier</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm font-medium">Supplier: {selectedSupplier.name}</p>
                            <p className="text-sm text-muted-foreground">Location: {selectedSupplier.location}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Speciality: {selectedSupplier.speciality}</p>
                            <p className="text-sm text-muted-foreground">Contact: {selectedSupplier.contact_person}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="productName">Product Name</Label>
                    <Input
                      id="productName"
                      value={newItem.product_name || ""}
                      onChange={(e) => setNewItem({ ...newItem, product_name: e.target.value })}
                      placeholder="Enter product name (e.g., Gold Chain, Diamond Ring, etc.)"
                    />
                  </div>

                  {/* Metal Information Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-4 text-amber-700">Metal Information</h3>

                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="space-y-2">
                        <Label htmlFor="metalType">Metal Type</Label>
                        <Select
                          value={newItem.metal_type || "Gold"}
                          onValueChange={(value) => setNewItem({ ...newItem, metal_type: value as any })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select metal type" />
                          </SelectTrigger>
                          <SelectContent>
                            {METAL_TYPES.map((type) => (
                              <SelectItem key={type} value={type}>{type}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="formType">Form Type</Label>
                        <Select
                          value={newItem.form_type || "Jewel"}
                          onValueChange={(value) => {
                            const defaultWastage = getWastageRate(value)
                            setNewItem({
                              ...newItem,
                              form_type: value as any,
                              jewel_type: value === "Jewel" ? "Without Stone" : undefined,
                              jewel_category: value === "Jewel" ? undefined : undefined,
                              wastage_percentage: defaultWastage
                            })
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select form type" />
                          </SelectTrigger>
                          <SelectContent>
                            {FORM_TYPES.map((type) => (
                              <SelectItem key={type} value={type}>{type}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Jewel-specific fields */}
                    {newItem.form_type === "Jewel" && (
                      <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                        <h4 className="font-medium mb-3 text-amber-800">Jewel Type Selection</h4>
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div className="space-y-2">
                            <Label htmlFor="jewelType">Jewel Type</Label>
                            <Select
                              value={newItem.jewel_type || "Without Stone"}
                              onValueChange={(value) => setNewItem({ ...newItem, jewel_type: value as any })}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select jewel type" />
                              </SelectTrigger>
                              <SelectContent>
                                {JEWEL_TYPES.map((type) => (
                                  <SelectItem key={type} value={type}>{type}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="jewelCategory">Jewel Category</Label>
                            <Select
                              value={newItem.jewel_category || ""}
                              onValueChange={(value) => setNewItem({
                                ...newItem,
                                jewel_category: value,
                                product_type: value !== "Others" ? value : newItem.product_type
                              })}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select jewel category" />
                              </SelectTrigger>
                              <SelectContent>
                                {JEWEL_CATEGORIES.map((category) => (
                                  <SelectItem key={category} value={category}>{category}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        {newItem.jewel_category === "Others" && (
                          <div className="space-y-2">
                            <Label htmlFor="customCategory">Custom Category</Label>
                            <Input
                              id="customCategory"
                              value={newItem.product_type || ""}
                              onChange={(e) => setNewItem({ ...newItem, product_type: e.target.value })}
                              placeholder="Enter custom jewel category"
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Weight and Cost Information - Adaptive based on jewel type */}
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-4 text-blue-700">Weight & Cost Information</h3>

                    <div className="grid grid-cols-3 gap-4 mb-4">
                      {/* Gross Weight - Always shown */}
                      <div className="space-y-2">
                        <Label htmlFor="grossWeight">
                          Gross Weight (g)
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="grossWeight"
                          type="number"
                          step="0.001"
                          value={newItem.form_type === "Bar" || newItem.jewel_type === "Without Stone"
                            ? (newItem.without_stone_weight || "")
                            : (newItem.with_stone_weight || "")
                          }
                          onChange={(e) => {
                            const value = Number.parseFloat(e.target.value) || 0
                            if (newItem.form_type === "Bar" || newItem.jewel_type === "Without Stone") {
                              setNewItem({ ...newItem, without_stone_weight: value, with_stone_weight: 0 })
                            } else {
                              setNewItem({ ...newItem, with_stone_weight: value })
                            }
                          }}
                          placeholder="0.000"
                        />
                      </div>

                      {/* Stone Weight - Only for "With Stone" jewels */}
                      {newItem.form_type === "Jewel" && newItem.jewel_type === "With Stone" && (
                        <div className="space-y-2">
                          <Label htmlFor="stoneWeight">
                            Stone Weight (g)
                            <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="stoneWeight"
                            type="number"
                            step="0.001"
                            value={newItem.stone_weight || ""}
                            onChange={(e) => {
                              const stoneWeight = Number.parseFloat(e.target.value) || 0
                              const grossWeight = newItem.with_stone_weight || 0
                              setNewItem({
                                ...newItem,
                                stone_weight: stoneWeight,
                                without_stone_weight: Math.max(0, grossWeight - stoneWeight)
                              })
                            }}
                            placeholder="0.000"
                          />
                          <p className="text-xs text-muted-foreground">
                            Net Weight: {((newItem.with_stone_weight || 0) - (newItem.stone_weight || 0)).toFixed(3)}g
                          </p>
                        </div>
                      )}

                      {/* Procured in 24K */}
                      <div className="space-y-2">
                        <Label htmlFor="procuredIn24k">
                          Procured in 24K (g)
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="procuredIn24k"
                          type="number"
                          step="0.001"
                          value={newItem.procured_in_24k || ""}
                          onChange={(e) =>
                            setNewItem({ ...newItem, procured_in_24k: Number.parseFloat(e.target.value) || 0 })
                          }
                          placeholder="0.000"
                        />
                      </div>
                    </div>

                    {/* Cost Information - Adaptive */}
                    <div className="space-y-2">
                      <Label htmlFor="costPercentage">
                        {newItem.form_type === "Bar"
                          ? "Cost Percentage (%)"
                          : newItem.jewel_type === "With Stone"
                            ? "With Stone Cost (%)"
                            : "Without Stone Cost (%)"}
                        <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="costPercentage"
                        type="number"
                        step="0.01"
                        value={newItem.form_type === "Bar" || newItem.jewel_type === "Without Stone"
                          ? (newItem.without_stone_cost || "")
                          : (newItem.with_stone_cost || "")
                        }
                        onChange={(e) => {
                          const value = Number.parseFloat(e.target.value) || 0
                          if (newItem.form_type === "Bar" || newItem.jewel_type === "Without Stone") {
                            setNewItem({ ...newItem, without_stone_cost: value, with_stone_cost: 0 })
                          } else {
                            setNewItem({ ...newItem, with_stone_cost: value })
                          }
                        }}
                        placeholder="0.00"
                      />
                      <p className="text-xs text-muted-foreground">
                        Cost percentage for procurement pricing
                      </p>
                    </div>

                    {/* Business Parameters Section */}
                    <div className="border-t pt-4">
                      <h3 className="text-lg font-semibold mb-4 text-green-700">Business Parameters</h3>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="wastagePercentage">Expected Wastage (%)</Label>
                          <Input
                            id="wastagePercentage"
                            type="number"
                            step="0.01"
                            value={newItem.wastage_percentage || ""}
                            onChange={(e) => setNewItem({ ...newItem, wastage_percentage: Number.parseFloat(e.target.value) || 0 })}
                            placeholder="2.00"
                          />
                          <p className="text-xs text-muted-foreground">
                            Expected wastage during processing (auto-set from settings: {getWastageRate(newItem.form_type)}%)
                          </p>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="processingLoss">Processing Loss (g)</Label>
                          <Input
                            id="processingLoss"
                            type="number"
                            step="0.001"
                            value={newItem.expected_processing_loss || ""}
                            onChange={(e) => setNewItem({ ...newItem, expected_processing_loss: Number.parseFloat(e.target.value) || 0 })}
                            placeholder="0.000"
                          />
                          <p className="text-xs text-muted-foreground">
                            Expected processing loss in grams
                          </p>
                        </div>
                      </div>

                      {/* Yield Calculation Display */}
                      {newItem.procured_in_24k && (
                        <div className="mt-4 p-3 bg-green-50 rounded-lg">
                          <h4 className="font-medium text-green-800 mb-2">Expected Yield Calculation</h4>
                          <div className="grid grid-cols-3 gap-4 text-sm">
                            <div>
                              <span className="text-green-600">Procured:</span>
                              <div className="font-medium">{newItem.procured_in_24k}g (24K)</div>
                            </div>
                            <div>
                              <span className="text-orange-600">Expected Wastage:</span>
                              <div className="font-medium">
                                {((newItem.procured_in_24k * (newItem.wastage_percentage || getWastageRate(newItem.form_type))) / 100).toFixed(3)}g
                              </div>
                            </div>
                            <div>
                              <span className="text-green-600">Expected Yield:</span>
                              <div className="font-medium">
                                {(newItem.procured_in_24k -
                                  (newItem.procured_in_24k * (newItem.wastage_percentage || getWastageRate(newItem.form_type))) / 100 -
                                  (newItem.expected_processing_loss || 0)).toFixed(3)}g
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {selectedSupplier && newItem.product_name && (
                    <div className="p-4 bg-green-50 rounded-lg space-y-2">
                      <h4 className="font-semibold text-green-800">Inventory Summary</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p>
                            <span className="font-medium">Supplier:</span> {selectedSupplier.name}
                          </p>
                          <p>
                            <span className="font-medium">Product:</span> {newItem.product_name}
                          </p>
                          <p>
                            <span className="font-medium">Type:</span> {newItem.product_type}
                          </p>
                        </div>
                        <div>
                          <p>
                            <span className="font-medium">Total Weight:</span>{" "}
                            {((newItem.with_stone_weight || 0) + (newItem.without_stone_weight || 0)).toFixed(3)}g
                          </p>
                          <p>
                            <span className="font-medium">24K Equivalent:</span>{" "}
                            {(newItem.procured_in_24k || 0).toFixed(3)}g
                          </p>
                          <p>
                            <span className="font-medium">22K Equivalent:</span>{" "}
                            {((newItem.procured_in_24k || 0) * 0.916).toFixed(3)}g
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="flex justify-end gap-2 pt-4 border-t bg-white sticky bottom-0">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsAddDialogOpen(false)
                      setSelectedSupplier(null)
                      const defaultWastage = getWastageRate("Jewel")
                      setNewItem({
                        metal_type: "Gold",
                        form_type: "Jewel",
                        jewel_type: "Without Stone",
                        wastage_percentage: defaultWastage
                      })
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleAddItem}
                    disabled={
                      !selectedSupplier ||
                      !newItem.product_name ||
                      !newItem.metal_type ||
                      !newItem.form_type ||
                      (newItem.form_type === "Jewel" && !newItem.jewel_type) ||
                      (newItem.form_type === "Jewel" && !newItem.jewel_category) ||
                      !newItem.procured_in_24k ||
                      (newItem.form_type === "Bar" || newItem.jewel_type === "Without Stone"
                        ? (!newItem.without_stone_weight || !newItem.without_stone_cost)
                        : (!newItem.with_stone_weight || !newItem.with_stone_cost)) ||
                      (newItem.form_type === "Jewel" && newItem.jewel_type === "With Stone" && !newItem.stone_weight)
                    }
                  >
                    Add Item
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search inventory..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Badge variant="secondary">{(filteredInventory || []).length} items</Badge>
          </div>

          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Sl.No</TableHead>
                    <TableHead>Supplier Details</TableHead>
                    <TableHead>Product Details</TableHead>
                    <TableHead>Metal Info</TableHead>
                    <TableHead>Weight (g)</TableHead>
                    <TableHead>Cost Price (%)</TableHead>
                    <TableHead>Stock Status</TableHead>
                    <TableHead className="w-32">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {(filteredInventory || []).map((item, index) => (
                    <TableRow key={item.id}>
                      <TableCell>{index + 1}</TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{item.supplier_name}</p>
                          <p className="text-sm text-muted-foreground">{item.supplier_location}</p>
                          <p className="text-sm text-blue-600">{item.supplier_contact_person}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium text-amber-600">{item.product_name}</p>
                          {item.product_type && (
                            <p className="text-sm text-muted-foreground">{item.product_type}</p>
                          )}
                          <p className="text-xs text-gray-500">Added: {new Date(item.created_at).toLocaleDateString()}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <div className="flex items-center gap-1">
                            <Badge variant="outline" className="text-xs">
                              {item.metal_type || "Gold"}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {item.form_type || "Jewel"}
                            </Badge>
                          </div>
                          {item.form_type === "Jewel" && (
                            <div className="space-y-1">
                              <p className="text-xs text-muted-foreground">
                                {item.jewel_type || "Without Stone"}
                              </p>
                              {item.jewel_category && (
                                <p className="text-xs font-medium text-amber-600">
                                  {item.jewel_category}
                                </p>
                              )}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <p>24K: {(Number(item.balance_weight_24k) || 0).toFixed(3)}g</p>
                          <p>22K: {(Number(item.balance_weight_22k) || 0).toFixed(3)}g</p>
                          <p>Procured: {(Number(item.procured_in_24k) || 0).toFixed(3)}g</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <p>With Stone: {item.with_stone_cost}%</p>
                          <p>Without: {item.without_stone_cost}%</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            item.status === "Available"
                              ? "default"
                              : item.status === "Low Stock"
                                ? "secondary"
                                : "destructive"
                          }
                        >
                          {item.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="w-32">
                        <div className="flex items-center gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditItem(item)}
                            className="h-8 w-8 p-0 bg-blue-50 hover:bg-blue-100 border-blue-200"
                            title="Edit Item"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleDeleteItem(item.id)}
                            disabled={mutating}
                            title="Delete Item"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
      <EditInventoryDialog
        item={editingItem}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSuccess={refetch}
      />
    </div>
  )
}
