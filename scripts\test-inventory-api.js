const fetch = require('node-fetch');

async function testInventoryAPI() {
  try {
    console.log('Testing inventory API...');
    
    const response = await fetch('http://localhost:3001/api/inventory');
    const data = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response data type:', typeof data);
    console.log('Is array:', Array.isArray(data));
    console.log('Data length:', data?.length);
    
    if (Array.isArray(data) && data.length > 0) {
      console.log('\nFirst item structure:');
      console.log(JSON.stringify(data[0], null, 2));
    } else {
      console.log('No data or not an array:', data);
    }
    
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testInventoryAPI();
