-- Sample data for Jewellery Wholesale Management System
USE jewellery_wholesale;

-- Insert suppliers
INSERT INTO suppliers (name, location, contact_person, phone, email, address, speciality, total_purchases, last_purchase_date, status) VALUES
('Emerald Jewel Industry', 'Coimbatore', '<PERSON><PERSON>', '+91 98765 43210', 'r<PERSON><PERSON>@emeraldjewel.com', '123, Jewelry Street, Coimbatore, Tamil Nadu', 'Gold Chains, Necklaces', 2500000.00, '2024-01-15', 'Active'),
('Nala Gold', 'Mumbai', '<PERSON><PERSON> Sharma', '+91 87654 32109', '<EMAIL>', '456, Gold Market, Mumbai, Maharashtra', 'Gold Bangles, Rings', 3200000.00, '2024-01-16', 'Active'),
('SSJ', 'Surat', '<PERSON><PERSON>', '+91 76543 21098', '<EMAIL>', '789, Diamond Plaza, Surat, Gujarat', 'Diamond Jewelry, Studs', 1800000.00, '2024-01-17', 'Active'),
('Chennai Gold House', 'Chennai', '<PERSON><PERSON> Kumar', '+91 98765 11111', '<EMAIL>', '101, Gold Street, Chennai, Tamil Nadu', 'Temple Jewelry, Antique', 1200000.00, '2024-01-18', 'Active'),
('Mysore Jewels', 'Mysore', 'Ravi Shankar', '+91 87654 22222', '<EMAIL>', '202, Palace Road, Mysore, Karnataka', 'Traditional Jewelry', 950000.00, '2024-01-19', 'Active'),
('Bangalore Gems', 'Bangalore', 'Kiran Kumar', '+91 99887 33333', '<EMAIL>', '303, MG Road, Bangalore, Karnataka', 'Precious Stones, Gems', 750000.00, '2024-01-20', 'Active');

-- Insert customers
INSERT INTO customers (name, location, contact_person, phone, email, address, total_purchases, last_purchase_date, status) VALUES
('VS Jewellery', 'Nammakal', 'Venkatesh', '+91 99887 76655', '<EMAIL>', '12, Main Street, Nammakal, Tamil Nadu', 450000.00, '2024-01-29', 'Active'),
('Krishna Jewels', 'Valapadi', 'Krishna Murthy', '+91 88776 65544', '<EMAIL>', '34, Temple Road, Valapadi, Tamil Nadu', 680000.00, '2024-01-28', 'Active'),
('Dhanapal Jewels', 'Erode', 'Dhanapal', '+91 77665 54433', '<EMAIL>', '56, Market Street, Erode, Tamil Nadu', 320000.00, '2024-01-27', 'Active'),
('Ravi Gold', 'Salem', 'Ravi Kumar', '+91 99887 12345', '<EMAIL>', '78, Gold Street, Salem, Tamil Nadu', 280000.00, '2024-01-26', 'Active'),
('Sree Jewellers', 'Tiruchengode', 'Sreekanth', '+91 88776 98765', '<EMAIL>', '90, Temple Street, Tiruchengode, Tamil Nadu', 195000.00, '2024-01-25', 'Active');

-- Insert inventory items
INSERT INTO inventory (supplier_id, product_name, product_type, with_stone_weight, without_stone_weight, with_stone_cost, without_stone_cost, procured_in_24k, sold_value_with_stone, sold_value_without_stone, balance_weight_24k, balance_weight_22k, status) VALUES
(1, 'Chain', 'Gold Chain', 0.000, 120.420, 93.00, 94.00, 113.195, 0.000, 10.160, 103.035, 110.260, 'Available'),
(2, 'Bangles', 'Gold Bangles', 0.000, 246.340, 94.00, 96.00, 236.486, 0.000, 32.120, 204.366, 214.220, 'Available'),
(3, 'Studs', 'Diamond Studs', 110.325, 89.120, 95.00, 99.00, 193.038, 1.560, 2.010, 189.468, 195.875, 'Low Stock'),
(1, 'Necklace', 'Gold Necklace', 0.000, 85.500, 95.00, 96.00, 82.080, 0.000, 0.000, 82.080, 89.560, 'Available'),
(2, 'Earrings', 'Gold Earrings', 15.200, 30.800, 96.00, 97.00, 44.176, 0.000, 0.000, 44.176, 48.220, 'Available'),
(4, 'Temple Set', 'Temple Jewelry', 25.500, 45.300, 94.00, 95.00, 66.535, 0.000, 0.000, 66.535, 72.580, 'Available');

-- Insert bills
INSERT INTO bills (customer_id, bill_number, product_name, product_type, with_stone, without_stone, gross_weight, stone_weight, net_weight, tunch_with_stone, tunch_without_stone, weight_in_24k, gold_24k_price, stone_price, total_amount, status, bill_date) VALUES
(1, 'INV001', 'Chain', 'Gold Chain', 0.000, 10.160, 10.160, 0.000, 10.160, 0, 96, 9.754, 10140.00, 0.00, 98901.50, 'Completed', '2024-01-15'),
(2, 'INV002', 'Bangles', 'Gold Bangles', 0.000, 32.120, 32.120, 0.000, 32.120, 0, 98, 31.478, 10140.00, 0.00, 319182.86, 'Pending', '2024-01-16'),
(3, 'INV003', 'Studs', 'Diamond Studs', 1.560, 2.010, 3.570, 0.160, 3.410, 97, 101, 3.388, 10140.00, 1600.00, 35955.33, 'Completed', '2024-01-17'),
(4, 'INV004', 'Necklace', 'Gold Necklace', 0.000, 15.250, 15.250, 0.000, 15.250, 0, 95, 14.488, 10140.00, 0.00, 146908.32, 'Pending', '2024-01-18'),
(5, 'INV005', 'Earrings', 'Gold Earrings', 2.100, 5.800, 7.900, 0.100, 7.800, 96, 97, 7.726, 10140.00, 800.00, 79141.64, 'Completed', '2024-01-19');

-- Insert current gold rates
INSERT INTO gold_rates (rate_24k, rate_22k, rate_18k, rate_date) VALUES
(10140.00, 9295.00, 7605.00, '2024-01-30'),
(10120.00, 9276.00, 7590.00, '2024-01-29'),
(10100.00, 9258.00, 7575.00, '2024-01-28'),
(10080.00, 9239.00, 7560.00, '2024-01-27'),
(10060.00, 9221.00, 7545.00, '2024-01-26');

-- Insert users
INSERT INTO users (name, email, password_hash, role, status, last_login, permissions) VALUES
('Admin User', '<EMAIL>', '$2b$10$example_hash', 'Admin', 'Active', '2024-01-29 09:30:00', '{"inventory": true, "billing": true, "reports": true, "suppliers": true, "goldRates": true, "backup": true}'),
('Rajesh Kumar', '<EMAIL>', '$2b$10$example_hash', 'Manager', 'Active', '2024-01-29 08:45:00', '{"inventory": true, "billing": true, "reports": true, "suppliers": true, "goldRates": false, "backup": false}'),
('Priya Sharma', '<EMAIL>', '$2b$10$example_hash', 'Staff', 'Active', '2024-01-28 17:20:00', '{"inventory": true, "billing": true, "reports": false, "suppliers": false, "goldRates": false, "backup": false}'),
('Viewer User', '<EMAIL>', '$2b$10$example_hash', 'Viewer', 'Active', NULL, '{"inventory": false, "billing": false, "reports": true, "suppliers": false, "goldRates": false, "backup": false}');
