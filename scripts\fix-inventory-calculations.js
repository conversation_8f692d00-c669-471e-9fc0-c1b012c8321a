const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixInventoryCalculations() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jewellery_wholesale_software'
    });

    console.log('Connected to database');

    // Find the Chain record from Emerald Jewel Industry
    const [records] = await connection.execute(`
      SELECT i.*, s.name as supplier_name
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      WHERE i.product_name LIKE '%chain%' 
      AND s.name LIKE '%emerald%'
    `);

    if (records.length === 0) {
      console.log('❌ Chain record from Emerald Jewel Industry not found');
      return;
    }

    const record = records[0];
    console.log('✅ Found record:', record.product_name, 'from', record.supplier_name);

    // Physical record values
    const physicalData = {
      procured_in_24k: 113.195,
      sold_value_with_stone: 0, // No stone sales
      sold_value_without_stone_24k: 9.754, // Gold Weight in 24K (sold)
      sold_value_without_stone_22k: 10.160, // Gold Weight in 22K (sold)
      balance_weight_24k: 9.754, // This should be remaining 24K gold
      balance_weight_22k: 110.260 // Balance in Stock (22K)
    };

    console.log('\n=== UPDATING WITH PHYSICAL RECORD VALUES ===');
    console.log('Physical Record Data:');
    console.log(`- Procured in 24K: ${physicalData.procured_in_24k}g`);
    console.log(`- Sold 24K Gold: ${physicalData.sold_value_without_stone_24k}g`);
    console.log(`- Sold 22K Gold: ${physicalData.sold_value_without_stone_22k}g`);
    console.log(`- Balance 24K: ${physicalData.balance_weight_24k}g`);
    console.log(`- Balance 22K: ${physicalData.balance_weight_22k}g`);

    // Update the record with correct values
    await connection.execute(`
      UPDATE inventory 
      SET 
        sold_value_with_stone = ?,
        sold_value_without_stone = ?,
        balance_weight_24k = ?,
        balance_weight_22k = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      physicalData.sold_value_with_stone,
      physicalData.sold_value_without_stone_24k, // Using 24K sold value
      physicalData.balance_weight_24k,
      physicalData.balance_weight_22k,
      record.id
    ]);

    console.log('✅ Record updated successfully');

    // Verify the update
    const [updatedRecord] = await connection.execute(`
      SELECT * FROM inventory WHERE id = ?
    `, [record.id]);

    console.log('\n=== VERIFICATION ===');
    const updated = updatedRecord[0];
    console.log('Updated Database Values:');
    console.log(`- Procured in 24K: ${updated.procured_in_24k}g`);
    console.log(`- Sold With Stone: ${updated.sold_value_with_stone}g`);
    console.log(`- Sold Without Stone: ${updated.sold_value_without_stone}g`);
    console.log(`- Balance 24K: ${updated.balance_weight_24k}g`);
    console.log(`- Balance 22K: ${updated.balance_weight_22k}g`);

    console.log('\n=== BUSINESS LOGIC ANALYSIS ===');
    console.log('From your physical record, I can see:');
    console.log('1. Total Procured: 113.195g (24K)');
    console.log('2. Sold Gold in 24K: 9.754g');
    console.log('3. Sold Gold in 22K: 10.160g');
    console.log('4. Remaining Balance: 110.260g (22K)');
    console.log('');
    console.log('This suggests the business logic should be:');
    console.log('- Track sold values separately for 24K and 22K');
    console.log('- Balance = Procured - Sold (in respective purities)');
    console.log('- The current app needs to be updated to handle this correctly');

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
fixInventoryCalculations();
