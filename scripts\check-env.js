#!/usr/bin/env node

const fs = require("fs")
const path = require("path")

console.log("🔍 Checking environment configuration...\n")

const envPath = path.join(process.cwd(), ".env.local")

if (!fs.existsSync(envPath)) {
  console.log("❌ .env.local file not found")
  console.log("Please run: npm run setup")
  process.exit(1)
}

// Read environment file
const envContent = fs.readFileSync(envPath, "utf8")
const envVars = {}

envContent.split("\n").forEach((line) => {
  const trimmed = line.trim()
  if (trimmed && !trimmed.startsWith("#")) {
    const [key, ...valueParts] = trimmed.split("=")
    if (key && valueParts.length > 0) {
      envVars[key] = valueParts.join("=")
    }
  }
})

// Required variables
const required = [
  "DB_HOST",
  "DB_PORT",
  "DB_USER",
  "DB_PASSWORD",
  "DB_NAME",
  "JWT_SECRET",
  "SESSION_SECRET",
  "NEXT_PUBLIC_APP_NAME",
  "NEXT_PUBLIC_COMPANY_NAME",
]

// Optional but recommended
const recommended = [
  "SMTP_HOST",
  "SMTP_USER",
  "SMTP_PASSWORD",
  "NEXT_PUBLIC_COMPANY_ADDRESS",
  "NEXT_PUBLIC_COMPANY_PHONE",
  "NEXT_PUBLIC_COMPANY_EMAIL",
]

let hasErrors = false
let hasWarnings = false

console.log("📋 Required Variables:")
required.forEach((key) => {
  if (envVars[key]) {
    if (key.includes("SECRET") && (envVars[key].includes("sample") || envVars[key].length < 20)) {
      console.log(`⚠️  ${key}: Set but appears to be sample/weak value`)
      hasWarnings = true
    } else if (key === "DB_PASSWORD" && (envVars[key] === "password123" || envVars[key].includes("sample"))) {
      console.log(`⚠️  ${key}: Set but appears to be sample value`)
      hasWarnings = true
    } else {
      console.log(`✅ ${key}: Set`)
    }
  } else {
    console.log(`❌ ${key}: Missing`)
    hasErrors = true
  }
})

console.log("\n📋 Recommended Variables:")
recommended.forEach((key) => {
  if (envVars[key]) {
    if (envVars[key].includes("sample")) {
      console.log(`⚠️  ${key}: Set but appears to be sample value`)
      hasWarnings = true
    } else {
      console.log(`✅ ${key}: Set`)
    }
  } else {
    console.log(`⚠️  ${key}: Not set (optional)`)
  }
})

// Security checks
console.log("\n🔒 Security Checks:")

if (envVars.NODE_ENV === "production") {
  console.log("🏭 Production environment detected")

  if (envVars.DEBUG === "true") {
    console.log("⚠️  DEBUG is enabled in production")
    hasWarnings = true
  } else {
    console.log("✅ DEBUG is disabled")
  }

  if (envVars.JWT_SECRET && envVars.JWT_SECRET.includes("sample")) {
    console.log("❌ JWT_SECRET contains sample value in production")
    hasErrors = true
  }

  if (envVars.SESSION_SECRET && envVars.SESSION_SECRET.includes("sample")) {
    console.log("❌ SESSION_SECRET contains sample value in production")
    hasErrors = true
  }
} else {
  console.log("🔧 Development environment detected")
  console.log("✅ Security checks relaxed for development")
}

// Business configuration
console.log("\n💼 Business Configuration:")
const businessVars = ["DEFAULT_CURRENCY", "DEFAULT_TAX_RATE", "DEFAULT_MAKING_CHARGES"]
businessVars.forEach((key) => {
  if (envVars[key]) {
    console.log(`✅ ${key}: ${envVars[key]}`)
  } else {
    console.log(`⚠️  ${key}: Using default value`)
  }
})

// Summary
console.log("\n📊 Summary:")
if (hasErrors) {
  console.log("❌ Configuration has errors that must be fixed")
  console.log("Please update .env.local and run this check again")
  process.exit(1)
} else if (hasWarnings) {
  console.log("⚠️  Configuration has warnings but should work")
  console.log("Consider updating sample values before production deployment")
} else {
  console.log("✅ Configuration looks good!")
}

console.log("\nNext steps:")
console.log("- Run: npm run test:db (test database connection)")
console.log("- Run: npm run test:email (test email configuration)")
console.log("- Run: npm run dev (start development server)")
