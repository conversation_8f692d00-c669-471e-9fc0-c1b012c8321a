"use client"

import { useState, useEffect } from "react"

export interface BusinessSettings {
  // Purity Conversion Factors
  conversion_24k_to_22k: number
  conversion_24k_to_18k: number
  conversion_22k_to_24k: number
  conversion_18k_to_24k: number
  
  // Default Wastage Rates (%)
  wastage_rate_bar: number
  wastage_rate_jewel: number
  wastage_rate_old_jewel: number
  
  // Business Rules
  auto_calculate_balances: boolean
  track_stone_separately: boolean
  enable_wastage_alerts: boolean
  low_stock_threshold: number
  
  // Pricing Rules
  default_making_charges: number
  stone_pricing_method: "weight" | "value" | "both"
  apply_gst: boolean
  gst_rate: number
  
  // Calculation Methods
  rounding_precision: number
  balance_calculation_method: "simple" | "weighted_average" | "fifo"
  
  // Alert Thresholds
  high_wastage_threshold: number
  price_variance_threshold: number
}

export const DEFAULT_BUSINESS_SETTINGS: BusinessSettings = {
  conversion_24k_to_22k: 0.916,
  conversion_24k_to_18k: 0.750,
  conversion_22k_to_24k: 1.092,
  conversion_18k_to_24k: 1.333,
  
  wastage_rate_bar: 0.5,
  wastage_rate_jewel: 2.0,
  wastage_rate_old_jewel: 3.0,
  
  auto_calculate_balances: true,
  track_stone_separately: true,
  enable_wastage_alerts: true,
  low_stock_threshold: 10.0,
  
  default_making_charges: 500.0,
  stone_pricing_method: "both",
  apply_gst: true,
  gst_rate: 3.0,
  
  rounding_precision: 3,
  balance_calculation_method: "simple",
  
  high_wastage_threshold: 5.0,
  price_variance_threshold: 10.0
}

export function useDatabaseSettings(userId?: number) {
  const [settings, setSettings] = useState<BusinessSettings>(DEFAULT_BUSINESS_SETTINGS)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>("")

  useEffect(() => {
    loadSettings()
  }, [userId])

  // Add a periodic refresh to catch settings changes
  useEffect(() => {
    const interval = setInterval(() => {
      loadSettings()
    }, 30000) // Refresh every 30 seconds

    return () => clearInterval(interval)
  }, [userId])

  const loadSettings = async () => {
    try {
      setLoading(true)
      setError("")

      const params = new URLSearchParams({ business: "true" })
      if (userId) {
        params.append("user_id", userId.toString())
      }

      const response = await fetch(`/api/settings?${params}`)
      const data = await response.json()

      if (data.success) {
        setSettings({ ...DEFAULT_BUSINESS_SETTINGS, ...data.data })
      } else {
        throw new Error(data.error || "Failed to load settings")
      }
    } catch (error) {
      console.error("Error loading settings:", error)
      setError(error instanceof Error ? error.message : "Failed to load settings")
      // Fall back to defaults
      setSettings(DEFAULT_BUSINESS_SETTINGS)
    } finally {
      setLoading(false)
    }
  }

  const updateSettings = async (newSettings: Partial<BusinessSettings>) => {
    try {
      setError("")

      const requestBody: any = {
        settings: newSettings,
        updatedBy: "user"
      }

      if (userId) {
        requestBody.userId = userId
      }

      const response = await fetch("/api/settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      })

      const data = await response.json()

      if (data.success) {
        // Update local state immediately
        setSettings(prev => ({ ...prev, ...newSettings }))
        // Also reload from database to ensure consistency
        setTimeout(() => loadSettings(), 100)
        return true
      } else {
        throw new Error(data.error || "Failed to update settings")
      }
    } catch (error) {
      console.error("Error updating settings:", error)
      setError(error instanceof Error ? error.message : "Failed to update settings")
      return false
    }
  }

  const updateSingleSetting = async (key: keyof BusinessSettings, value: any) => {
    return updateSettings({ [key]: value })
  }

  const getWastageRate = (formType: string): number => {
    switch (formType) {
      case "Bar":
        return settings.wastage_rate_bar
      case "Jewel":
        return settings.wastage_rate_jewel
      case "Old Jewel":
        return settings.wastage_rate_old_jewel
      default:
        return settings.wastage_rate_jewel
    }
  }

  const getConversionFactor = (fromPurity: string, toPurity: string): number => {
    const key = `conversion_${fromPurity.toLowerCase()}_to_${toPurity.toLowerCase()}` as keyof BusinessSettings
    return settings[key] as number || 1.0
  }

  const resetToDefaults = async () => {
    try {
      setError("")

      // Delete all user settings to revert to system defaults
      if (userId) {
        const settingsKeys = Object.keys(DEFAULT_BUSINESS_SETTINGS)
        
        for (const key of settingsKeys) {
          // Determine category
          let category = 'business'
          if (key.startsWith('conversion_')) category = 'conversion'
          else if (key.startsWith('wastage_rate_')) category = 'wastage'
          else if (key.includes('making_charges') || key.includes('gst') || key.includes('pricing')) category = 'pricing'
          else if (key.includes('rounding') || key.includes('calculation')) category = 'calculation'
          else if (key.includes('threshold')) category = 'alerts'

          const params = new URLSearchParams({
            category,
            key,
            user_id: userId.toString()
          })

          await fetch(`/api/settings?${params}`, {
            method: "DELETE"
          })
        }
      } else {
        // Reset system settings to defaults
        await updateSettings(DEFAULT_BUSINESS_SETTINGS)
      }

      // Reload settings
      await loadSettings()
      return true
    } catch (error) {
      console.error("Error resetting settings:", error)
      setError(error instanceof Error ? error.message : "Failed to reset settings")
      return false
    }
  }

  const refreshSettings = () => {
    loadSettings()
  }

  const forceRefresh = () => {
    setLoading(true)
    loadSettings()
  }

  return {
    settings,
    loading,
    error,
    updateSettings,
    updateSingleSetting,
    getWastageRate,
    getConversionFactor,
    resetToDefaults,
    refreshSettings,
    forceRefresh,
    loadSettings
  }
}

// Backward compatibility hook that migrates localStorage to database
export function useBusinessSettings(userId?: number) {
  const databaseHook = useDatabaseSettings(userId)
  const [migrated, setMigrated] = useState(false)

  useEffect(() => {
    // One-time migration from localStorage to database
    if (!migrated && !databaseHook.loading) {
      migrateFromLocalStorage()
    }
  }, [migrated, databaseHook.loading])

  const migrateFromLocalStorage = async () => {
    try {
      const savedSettings = localStorage.getItem('business_settings')
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings)
        console.log("Migrating settings from localStorage to database...")
        
        const success = await databaseHook.updateSettings(parsed)
        if (success) {
          console.log("Settings migrated successfully")
          // Remove from localStorage after successful migration
          localStorage.removeItem('business_settings')
        }
      }
    } catch (error) {
      console.error("Error migrating settings:", error)
    } finally {
      setMigrated(true)
    }
  }

  return databaseHook
}
