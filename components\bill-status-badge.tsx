"use client"

import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useDatabaseMutation } from "@/lib/hooks/useDatabase"

interface BillStatusBadgeProps {
  billId: number
  currentStatus: "Pending" | "Completed" | "Cancelled"
  onStatusChange: () => void
}

export default function BillStatusBadge({ billId, currentStatus, onStatusChange }: BillStatusBadgeProps) {
  const { mutate, loading } = useDatabaseMutation()

  const handleStatusChange = async (newStatus: string) => {
    const result = await mutate(`bills/${billId}`, "PUT", { status: newStatus })
    if (result) {
      onStatusChange()
    }
  }

  return (
    <Select value={currentStatus} onValueChange={handleStatusChange} disabled={loading}>
      <SelectTrigger className="w-32">
        <SelectValue>
          <Badge
            variant={
              currentStatus === "Completed" ? "default" : currentStatus === "Pending" ? "secondary" : "destructive"
            }
          >
            {currentStatus}
          </Badge>
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="Pending">
          <Badge variant="secondary">Pending</Badge>
        </SelectItem>
        <SelectItem value="Completed">
          <Badge variant="default">Completed</Badge>
        </SelectItem>
        <SelectItem value="Cancelled">
          <Badge variant="destructive">Cancelled</Badge>
        </SelectItem>
      </SelectContent>
    </Select>
  )
}
