"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Plus, Search, Phone, Mail, MapPin, Edit, Trash2, Users, TrendingUp, IndianRupee } from "lucide-react"
import { useDatabase } from "@/hooks/use-database"
import { useDatabaseMutation } from "@/hooks/use-database-mutation"
import { useToast } from "@/hooks/use-toast"
import EditCustomerDialog from "@/components/edit-customer-dialog"

interface Customer {
  id: number
  name: string
  location: string
  contact_person: string
  phone: string
  email: string
  address: string
  total_purchases: number
  last_purchase_date: string
  status: "Active" | "Inactive"
}

export default function CustomerManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newCustomer, setNewCustomer] = useState<Partial<Customer>>({})
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  const { data: customers, loading, error, refetch } = useDatabase<Customer>("customers", searchTerm)
  const { mutate, loading: mutating } = useDatabaseMutation<Customer>()
  const { toast } = useToast()

  const filteredCustomers = (customers || []).filter(
    (customer) =>
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.contact_person.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleAddCustomer = async () => {
    if (!newCustomer.name || !newCustomer.contact_person) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields (Name and Contact Person).",
        variant: "destructive",
      })
      return
    }

    try {
      const result = await mutate("customers", "POST", newCustomer)
      if (result) {
        toast({
          title: "Customer Added Successfully",
          description: `"${newCustomer.name}" has been added to customers.`,
          variant: "default",
        })

        setNewCustomer({})
        setIsAddDialogOpen(false)
        await refetch()
      } else {
        toast({
          title: "Add Failed",
          description: "Failed to add the customer. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Add Error",
        description: "An error occurred while adding the customer.",
        variant: "destructive",
      })
    }
  }

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer)
    setIsEditDialogOpen(true)
  }

  const handleDeleteCustomer = async (id: number) => {
    const customer = customers?.find(c => c.id === id)
    const customerName = customer?.name || `Customer #${id}`

    if (confirm(`Are you sure you want to delete "${customerName}"?`)) {
      try {
        const result = await mutate(`customers/${id}`, "DELETE")
        if (result && (result as any).deleted) {
          toast({
            title: "Customer Deleted",
            description: `"${customerName}" has been successfully deleted.`,
            variant: "default",
          })
          await refetch()
        } else {
          toast({
            title: "Delete Failed",
            description: "Failed to delete the customer. Please try again.",
            variant: "destructive",
          })
        }
      } catch (error) {
        toast({
          title: "Delete Error",
          description: "An error occurred while deleting the customer.",
          variant: "destructive",
        })
      }
    }
  }

  const totalPurchases = (customers || []).reduce((sum, customer) => sum + (Number(customer.total_purchases) || 0), 0)
  const activeCustomers = (customers || []).filter((c) => c.status === "Active").length
  const topCustomer =
    (customers || []).length > 0
      ? (customers || []).reduce((prev, current) => ((Number(prev.total_purchases) || 0) > (Number(current.total_purchases) || 0) ? prev : current))
      : null

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{customers?.length || 0}</div>
            <p className="text-xs text-muted-foreground">Registered customers</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeCustomers}</div>
            <p className="text-xs text-muted-foreground">Currently active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
            <IndianRupee className="h-4 w-4 text-amber-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{(totalPurchases / 100000).toFixed(1)}L</div>
            <p className="text-xs text-muted-foreground">All time sales</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Customer</CardTitle>
            <Badge variant="secondary">Best</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{topCustomer?.name || "N/A"}</div>
            <p className="text-xs text-muted-foreground">Highest purchase value</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Customer Management</CardTitle>
              <CardDescription>Manage your jewelry customers and their details</CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Customer
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Add New Customer</DialogTitle>
                  <DialogDescription>Enter the details for the new customer</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto px-1">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="customerName">Customer Name</Label>
                      <Input
                        id="customerName"
                        value={newCustomer.name || ""}
                        onChange={(e) => setNewCustomer({ ...newCustomer, name: e.target.value })}
                        placeholder="Enter customer name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        value={newCustomer.location || ""}
                        onChange={(e) => setNewCustomer({ ...newCustomer, location: e.target.value })}
                        placeholder="Enter location"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="contactPerson">Contact Person</Label>
                      <Input
                        id="contactPerson"
                        value={newCustomer.contact_person || ""}
                        onChange={(e) => setNewCustomer({ ...newCustomer, contact_person: e.target.value })}
                        placeholder="Enter contact person name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={newCustomer.phone || ""}
                        onChange={(e) => setNewCustomer({ ...newCustomer, phone: e.target.value })}
                        placeholder="Enter phone number"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newCustomer.email || ""}
                      onChange={(e) => setNewCustomer({ ...newCustomer, email: e.target.value })}
                      placeholder="Enter email address"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      value={newCustomer.address || ""}
                      onChange={(e) => setNewCustomer({ ...newCustomer, address: e.target.value })}
                      placeholder="Enter full address"
                    />
                  </div>
                </div>
                <div className="flex justify-end gap-2 pt-4 border-t bg-white sticky bottom-0">
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddCustomer}>Add Customer</Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Badge variant="secondary">{filteredCustomers.length} customers</Badge>
          </div>

          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer Details</TableHead>
                    <TableHead>Contact Information</TableHead>
                    <TableHead>Purchase History</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCustomers.map((customer) => (
                    <TableRow key={customer.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{customer.name}</p>
                          <p className="text-sm text-muted-foreground flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {customer.location}
                          </p>
                          <p className="text-sm font-medium text-blue-600">{customer.contact_person}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="text-sm flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {customer.phone}
                          </p>
                          <p className="text-sm flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {customer.email}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">₹{(Number(customer.total_purchases) || 0).toLocaleString("en-IN")}</p>
                          <p className="text-sm text-muted-foreground">Last: {customer.last_purchase_date || 'N/A'}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={customer.status === "Active" ? "default" : "secondary"}>
                          {customer.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" onClick={() => handleEditCustomer(customer)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700 bg-transparent"
                            onClick={() => handleDeleteCustomer(customer.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
      <EditCustomerDialog
        customer={editingCustomer}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSuccess={refetch}
      />
    </div>
  )
}
