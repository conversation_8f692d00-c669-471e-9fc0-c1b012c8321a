import { Card, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { IndianRupee, Package, Users, TrendingUp, AlertTriangle } from "lucide-react"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from "recharts"

import { useDatabase } from "@/lib/hooks/useDatabase"
import LoadingSpinner from "@/components/loading-spinner"
import ErrorDisplay from "@/components/error-display"
import type { Bill } from "@/lib/models/bill"
import type { InventoryItem } from "@/lib/models/inventory"
import type { GoldRate } from "@/lib/models/gold-rate"

export default function Dashboard() {
  const { data: bills, loading: billsLoading, error: billsError, refetch: refetchBills } = useDatabase<Bill>("bills")
  const { data: inventory, loading: inventoryLoading, error: inventoryError } = useDatabase<InventoryItem>("inventory")
  const { data: goldRates, loading: goldRatesLoading } = useDatabase<GoldRate>("gold-rates?current=true")

  const loading = billsLoading || inventoryLoading || goldRatesLoading
  const error = billsError || inventoryError

  if (loading) {
    return <LoadingSpinner message="Loading dashboard data..." />
  }

  if (error) {
    return <ErrorDisplay error={error} onRetry={refetchBills} title="Dashboard Error" />
  }

  // Calculate real stats from database data
  const totalInventoryValue = inventory.reduce((sum, item) => sum + (Number(item.balance_weight_24k) || 0) * 6890, 0)
  const monthlySales = bills
    .filter((bill) => bill.status === "Paid")
    .reduce((sum, bill) => sum + (Number(bill.total_amount) || 0), 0)
  const currentGoldRate = Number(goldRates[0]?.rate_24k) || 6890
  const activeSuppliers = new Set(inventory.map((item) => item.supplier_id)).size

  const recentTransactions = bills.slice(0, 3).map((bill) => ({
    id: bill.bill_number,
    customer: bill.customer_name || "Unknown",
    amount: `₹${(Number(bill.total_amount) || 0).toLocaleString("en-IN")}`,
    type: "Sale",
    status: bill.status,
  }))

  const lowStockItems = inventory
    .filter((item) => (Number(item.balance_weight_24k) || 0) < 10)
    .slice(0, 3)
    .map((item) => ({
      name: item.product_name,
      stock: `${(Number(item.balance_weight_24k) || 0).toFixed(1)}g`,
      supplier: item.supplier_name || "Unknown",
    }))

  const stats = [
    {
      title: "Total Inventory Value",
      value: `₹${(totalInventoryValue / 100000).toFixed(1)}L`,
      change: "+12.5%",
      icon: Package,
      color: "text-blue-600",
    },
    {
      title: "Monthly Sales",
      value: `₹${(monthlySales / 100000).toFixed(1)}L`,
      change: "+8.2%",
      icon: TrendingUp,
      color: "text-green-600",
    },
    {
      title: "Active Suppliers",
      value: activeSuppliers.toString(),
      change: "+3",
      icon: Users,
      color: "text-purple-600",
    },
    {
      title: "Gold Rate (24K)",
      value: `₹${currentGoldRate.toLocaleString("en-IN")}/10g`,
      change: "+0.5%",
      icon: IndianRupee,
      color: "text-amber-600",
    },
  ]

  const goldRateTrend = [
    { date: "2024-01-01", rate: 6850 },
    { date: "2024-01-08", rate: 6890 },
    { date: "2024-01-15", rate: 6920 },
    { date: "2024-01-22", rate: 6890 },
    { date: "2024-01-29", rate: 6950 },
  ]

  const monthlySalesData = [
    { month: "Sep", sales: 450000, purchases: 320000 },
    { month: "Oct", sales: 520000, purchases: 380000 },
    { month: "Nov", sales: 680000, purchases: 450000 },
    { month: "Dec", sales: 750000, purchases: 520000 },
    { month: "Jan", sales: 890000, purchases: 620000 },
  ]

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">{stat.change}</span> from last month
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 md:grid-cols-2 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Gold Rate Trend (24K)</CardTitle>
            <CardDescription>Last 30 days price movement</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={goldRateTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" tickFormatter={(value) => new Date(value).toLocaleDateString()} />
                <YAxis />
                <Tooltip
                  labelFormatter={(value) => new Date(value).toLocaleDateString()}
                  formatter={(value) => [`₹${value}/10g`, "Gold Rate"]}
                />
                <Line type="monotone" dataKey="rate" stroke="#f59e0b" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Monthly Performance</CardTitle>
            <CardDescription>Sales vs Purchases comparison</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={monthlySalesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => `₹${(value / 100000).toFixed(1)}L`} />
                <Tooltip formatter={(value) => [`₹${value.toLocaleString("en-IN")}`, ""]} />
                <Bar dataKey="sales" fill="#10b981" name="Sales" />
                <Bar dataKey="purchases" fill="#f59e0b" name="Purchases" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
            <CardDescription>Latest billing activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{transaction.customer}</p>
                    <p className="text-sm text-muted-foreground">{transaction.id}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{transaction.amount}</p>
                    <Badge variant={transaction.status === "Completed" ? "default" : "secondary"}>
                      {transaction.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-amber-500" />
              Low Stock Alert
            </CardTitle>
            <CardDescription>Items running low in inventory</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {lowStockItems.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg bg-amber-50">
                  <div>
                    <p className="font-medium">{item.name}</p>
                    <p className="text-sm text-muted-foreground">{item.supplier}</p>
                  </div>
                  <Badge variant="outline" className="text-amber-600 border-amber-600">
                    {item.stock}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
