const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function checkAndUpdateSchema() {
  let connection;

  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME
  };

  try {
    console.log('🔌 Connecting to database...');
    connection = await mysql.createConnection(config);
    console.log(`📊 Connected to database: ${process.env.DB_NAME}`);

    console.log('\n=== CHECKING AND UPDATING DATABASE SCHEMA ===\n');

    // Check inventory table fields
    console.log('📦 CHECKING INVENTORY TABLE...');
    const [inventoryColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME]);

    const existingInventoryFields = inventoryColumns.map(col => col.COLUMN_NAME);
    
    // Required fields based on current application usage
    const requiredInventoryFields = [
      { name: 'id', type: 'INT AUTO_INCREMENT PRIMARY KEY' },
      { name: 'supplier_id', type: 'INT' },
      { name: 'product_name', type: 'VARCHAR(255) NOT NULL' },
      { name: 'product_type', type: 'VARCHAR(255)' },
      { name: 'metal_type', type: "ENUM('Gold', 'Silver', 'Platinum') DEFAULT 'Gold'" },
      { name: 'form_type', type: "ENUM('Bar', 'Jewel', 'Old Jewel') DEFAULT 'Jewel'" },
      { name: 'jewel_type', type: "ENUM('With Stone', 'Without Stone') NULL" },
      { name: 'jewel_category', type: 'VARCHAR(100) NULL' },
      { name: 'with_stone_weight', type: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'without_stone_weight', type: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'stone_weight', type: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'with_stone_cost', type: 'DECIMAL(10,2) DEFAULT 0.00' },
      { name: 'without_stone_cost', type: 'DECIMAL(10,2) DEFAULT 0.00' },
      { name: 'procured_in_24k', type: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'wastage_percentage', type: 'DECIMAL(5,2) DEFAULT 0.00' },
      { name: 'expected_processing_loss', type: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'sold_value_with_stone', type: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'sold_value_without_stone', type: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'sold_value_24k', type: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'sold_value_22k', type: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'sold_value_18k', type: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'balance_weight_24k', type: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'balance_weight_22k', type: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'making_charges', type: 'DECIMAL(10,2) DEFAULT 0.00' },
      { name: 'status', type: "ENUM('Available', 'Low Stock', 'Out of Stock') DEFAULT 'Available'" },
      { name: 'created_at', type: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP' },
      { name: 'updated_at', type: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP' }
    ];

    // Check for missing fields in inventory table
    const missingInventoryFields = requiredInventoryFields.filter(
      field => !existingInventoryFields.includes(field.name)
    );

    if (missingInventoryFields.length > 0) {
      console.log(`❌ Missing ${missingInventoryFields.length} fields in inventory table:`);
      for (const field of missingInventoryFields) {
        console.log(`   Adding: ${field.name}`);
        try {
          await connection.execute(`ALTER TABLE inventory ADD COLUMN ${field.name} ${field.type}`);
          console.log(`   ✅ Added: ${field.name}`);
        } catch (error) {
          console.log(`   ❌ Failed to add ${field.name}: ${error.message}`);
        }
      }
    } else {
      console.log('✅ All required inventory fields present');
    }

    // Check bills table fields
    console.log('\n🧾 CHECKING BILLS TABLE...');
    const [billsColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'bills'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME]);

    const existingBillsFields = billsColumns.map(col => col.COLUMN_NAME);
    
    const requiredBillsFields = [
      { name: 'inventory_item_id', type: 'INT' },
      { name: 'making_charges', type: 'DECIMAL(10,2) DEFAULT 0.00' }
    ];

    const missingBillsFields = requiredBillsFields.filter(
      field => !existingBillsFields.includes(field.name)
    );

    if (missingBillsFields.length > 0) {
      console.log(`❌ Missing ${missingBillsFields.length} fields in bills table:`);
      for (const field of missingBillsFields) {
        console.log(`   Adding: ${field.name}`);
        try {
          if (field.name === 'inventory_item_id') {
            await connection.execute(`
              ALTER TABLE bills 
              ADD COLUMN inventory_item_id INT AFTER customer_id,
              ADD FOREIGN KEY (inventory_item_id) REFERENCES inventory(id) ON DELETE SET NULL
            `);
          } else {
            await connection.execute(`ALTER TABLE bills ADD COLUMN ${field.name} ${field.type}`);
          }
          console.log(`   ✅ Added: ${field.name}`);
        } catch (error) {
          console.log(`   ❌ Failed to add ${field.name}: ${error.message}`);
        }
      }
    } else {
      console.log('✅ All required bills fields present');
    }

    // Check if sales_transactions table exists
    console.log('\n💼 CHECKING SALES_TRANSACTIONS TABLE...');
    const [salesTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'sales_transactions'
    `, [process.env.DB_NAME]);

    if (salesTables.length === 0) {
      console.log('❌ sales_transactions table missing, creating...');
      await connection.execute(`
        CREATE TABLE sales_transactions (
          id INT AUTO_INCREMENT PRIMARY KEY,
          inventory_id INT NOT NULL,
          customer_id INT,
          transaction_type ENUM('Sale', 'Return', 'Exchange', 'Wastage') DEFAULT 'Sale',
          weight_24k DECIMAL(10,3) DEFAULT 0.000,
          weight_22k DECIMAL(10,3) DEFAULT 0.000,
          weight_18k DECIMAL(10,3) DEFAULT 0.000,
          stone_weight DECIMAL(10,3) DEFAULT 0.000,
          stone_value DECIMAL(10,2) DEFAULT 0.00,
          rate_24k DECIMAL(10,2) DEFAULT 0.00,
          rate_22k DECIMAL(10,2) DEFAULT 0.00,
          making_charges DECIMAL(10,2) DEFAULT 0.00,
          total_amount DECIMAL(15,2) DEFAULT 0.00,
          transaction_date DATE DEFAULT (CURRENT_DATE),
          bill_number VARCHAR(50),
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          created_by VARCHAR(100),
          FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE,
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
          INDEX idx_sales_inventory_id (inventory_id),
          INDEX idx_sales_customer_id (customer_id),
          INDEX idx_sales_transaction_date (transaction_date),
          INDEX idx_sales_transaction_type (transaction_type),
          INDEX idx_sales_bill_number (bill_number)
        )
      `);
      console.log('✅ sales_transactions table created');
    } else {
      console.log('✅ sales_transactions table exists');
    }

    // Check if wastage_records table exists
    console.log('\n🗑️  CHECKING WASTAGE_RECORDS TABLE...');
    const [wastageTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'wastage_records'
    `, [process.env.DB_NAME]);

    if (wastageTables.length === 0) {
      console.log('❌ wastage_records table missing, creating...');
      await connection.execute(`
        CREATE TABLE wastage_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          inventory_id INT NOT NULL,
          wastage_type ENUM('Processing', 'Manufacturing', 'Refining', 'Loss', 'Other') DEFAULT 'Processing',
          weight_24k DECIMAL(10,3) DEFAULT 0.000,
          weight_22k DECIMAL(10,3) DEFAULT 0.000,
          weight_18k DECIMAL(10,3) DEFAULT 0.000,
          wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
          reason TEXT,
          process_stage VARCHAR(100),
          recovered_weight DECIMAL(10,3) DEFAULT 0.000,
          recovered_purity ENUM('24K', '22K', '18K', 'Mixed') DEFAULT '24K',
          recorded_date DATE DEFAULT (CURRENT_DATE),
          recorded_by VARCHAR(100),
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE,
          INDEX idx_wastage_inventory_id (inventory_id),
          INDEX idx_wastage_type (wastage_type),
          INDEX idx_wastage_date (recorded_date)
        )
      `);
      console.log('✅ wastage_records table created');
    } else {
      console.log('✅ wastage_records table exists');
    }

    // Check foreign key constraints
    console.log('\n🔗 CHECKING FOREIGN KEY CONSTRAINTS...');
    const [foreignKeys] = await connection.execute(`
      SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        CONSTRAINT_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = ? AND REFERENCED_TABLE_NAME IS NOT NULL
      ORDER BY TABLE_NAME, COLUMN_NAME
    `, [process.env.DB_NAME]);

    console.log(`✅ Found ${foreignKeys.length} foreign key constraints`);

    // Add missing foreign key for inventory.supplier_id if not exists
    const inventorySupplierFK = foreignKeys.find(fk => 
      fk.TABLE_NAME === 'inventory' && fk.COLUMN_NAME === 'supplier_id'
    );

    if (!inventorySupplierFK) {
      console.log('❌ Missing foreign key: inventory.supplier_id → suppliers.id');
      try {
        await connection.execute(`
          ALTER TABLE inventory 
          ADD FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL
        `);
        console.log('✅ Added foreign key: inventory.supplier_id → suppliers.id');
      } catch (error) {
        console.log(`❌ Failed to add foreign key: ${error.message}`);
      }
    }

    console.log('\n=== SCHEMA UPDATE COMPLETE ===');
    console.log('✅ Database schema is now up to date with current application requirements');

  } catch (error) {
    console.error('❌ Error during schema check/update:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the schema check and update
checkAndUpdateSchema();
