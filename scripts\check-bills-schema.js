#!/usr/bin/env node

require("dotenv").config({ path: ".env.local" })

const mysql = require("mysql2/promise")

async function checkBillsSchema() {
  console.log("🔍 Checking bills table schema...\n")

  const config = {
    host: process.env.DB_HOST || "localhost",
    port: Number.parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "jewellery_wholesale_software",
  }

  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(config)

    // Check bills table structure
    const [columns] = await connection.execute("DESCRIBE bills")

    console.log("📊 Bills Table Schema:")
    console.log("=" .repeat(80))
    console.log("Field                | Type                 | Null | Key | Default | Extra")
    console.log("-" .repeat(80))
    
    columns.forEach(col => {
      const field = String(col.Field).padEnd(20)
      const type = String(col.Type).padEnd(20)
      const nullVal = String(col.Null).padEnd(4)
      const key = String(col.Key || '').padEnd(3)
      const defaultVal = String(col.Default || '').padEnd(7)
      const extra = String(col.Extra || '').padEnd(10)
      
      console.log(`${field} | ${type} | ${nullVal} | ${key} | ${defaultVal} | ${extra}`)
    })

    console.log("-" .repeat(80))
    console.log(`Total Columns: ${columns.length}`)

    await connection.end()
    
  } catch (error) {
    console.log("\n❌ Schema check failed:")
    console.log(`   Error: ${error.message}`)
    process.exit(1)
  }
}

checkBillsSchema()
