import { type NextRequest, NextResponse } from "next/server"
import { SupplierModel } from "@/lib/models/supplier"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const supplier = await SupplierModel.getById(id)

    if (!supplier) {
      return NextResponse.json({ success: false, error: "Supplier not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: supplier })
  } catch (error) {
    console.error("Error fetching supplier:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch supplier" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const data = await request.json()

    const success = await SupplierModel.update(id, data)

    if (!success) {
      return NextResponse.json({ success: false, error: "Supplier not found or no changes made" }, { status: 404 })
    }

    const supplier = await SupplierModel.getById(id)
    return NextResponse.json({ success: true, data: supplier })
  } catch (error) {
    console.error("Error updating supplier:", error)
    return NextResponse.json({ success: false, error: "Failed to update supplier" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const success = await SupplierModel.delete(id)

    if (!success) {
      return NextResponse.json({ success: false, error: "Supplier not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "Supplier deleted successfully" })
  } catch (error) {
    console.error("Error deleting supplier:", error)
    return NextResponse.json({ success: false, error: "Failed to delete supplier" }, { status: 500 })
  }
}
