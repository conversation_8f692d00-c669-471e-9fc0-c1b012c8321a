import { type NextRequest, NextResponse } from "next/server"
import { CustomerModel } from "@/lib/models/customer"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const customer = await CustomerModel.getById(id)

    if (!customer) {
      return NextResponse.json({ success: false, error: "Customer not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: customer })
  } catch (error) {
    console.error("Error fetching customer:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch customer" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const data = await request.json()

    const success = await CustomerModel.update(id, data)

    if (!success) {
      return NextResponse.json({ success: false, error: "Customer not found or no changes made" }, { status: 404 })
    }

    const customer = await CustomerModel.getById(id)
    return NextResponse.json({ success: true, data: customer })
  } catch (error) {
    console.error("Error updating customer:", error)
    return NextResponse.json({ success: false, error: "Failed to update customer" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)
    const success = await CustomerModel.delete(id)

    if (!success) {
      return NextResponse.json({ success: false, error: "Customer not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "Customer deleted successfully" })
  } catch (error) {
    console.error("Error deleting customer:", error)
    return NextResponse.json({ success: false, error: "Failed to delete customer" }, { status: 500 })
  }
}
