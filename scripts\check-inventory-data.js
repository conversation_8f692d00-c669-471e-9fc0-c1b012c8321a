const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkInventoryData() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME
    });

    console.log('Connected to database:', process.env.DB_NAME);

    // Get all inventory items with supplier details
    const [inventory] = await connection.execute(`
      SELECT i.*, s.name as supplier_name, s.location as supplier_location
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      ORDER BY i.created_at DESC
    `);

    console.log('\n=== CURRENT INVENTORY DATA ===');
    console.log(`Found ${inventory.length} inventory items:\n`);

    inventory.forEach((item, index) => {
      console.log(`${index + 1}. ID: ${item.id}`);
      console.log(`   Supplier: ${item.supplier_name} (${item.supplier_location})`);
      console.log(`   Product: ${item.product_name} (${item.product_type})`);
      console.log(`   Metal Type: ${item.metal_type}`);
      console.log(`   Form Type: ${item.form_type}`);
      console.log(`   Jewel Type: ${item.jewel_type || 'N/A'}`);
      console.log(`   Jewel Category: ${item.jewel_category || 'N/A'}`);
      console.log(`   With Stone Weight: ${item.with_stone_weight}g`);
      console.log(`   Without Stone Weight: ${item.without_stone_weight}g`);
      console.log(`   Stone Weight: ${item.stone_weight || 0}g`);
      console.log(`   With Stone Cost: ${item.with_stone_cost}%`);
      console.log(`   Without Stone Cost: ${item.without_stone_cost}%`);
      console.log(`   Procured in 24K: ${item.procured_in_24k}g`);
      console.log(`   Balance Weight 24K: ${item.balance_weight_24k}g`);
      console.log(`   Balance Weight 22K: ${item.balance_weight_22k}g`);
      console.log(`   Status: ${item.status}`);
      console.log(`   Created: ${item.created_at}`);
      console.log('   ---');
    });

    // Compare with your physical record
    console.log('\n=== COMPARISON WITH PHYSICAL RECORD ===');
    console.log('Physical Record:');
    console.log('- Supplier: Emerald Jewel Industry, Coimbatore');
    console.log('- Product: Chain');
    console.log('- With Stone: 0');
    console.log('- Without Stone: 120.420g');
    console.log('- With Stone Cost: 93%');
    console.log('- Without Stone Cost: 94%');
    console.log('- Procured in 24K: 113.195g');
    console.log('- Stone Weight: 0.000g');
    console.log('- Gold Weight in 22K: 10.160g');
    console.log('- Gold Weight in 24K: 9.754g');
    console.log('- Gold Weight in 22K (Balance): 110.260g');

    // Find matching record
    const matchingRecord = inventory.find(item => 
      item.product_name.toLowerCase().includes('chain') &&
      item.supplier_name.toLowerCase().includes('emerald')
    );

    if (matchingRecord) {
      console.log('\n=== FOUND MATCHING RECORD ===');
      console.log('Database vs Physical Record:');
      console.log(`Without Stone Weight: ${matchingRecord.without_stone_weight}g vs 120.420g`);
      console.log(`Without Stone Cost: ${matchingRecord.without_stone_cost}% vs 94%`);
      console.log(`Procured in 24K: ${matchingRecord.procured_in_24k}g vs 113.195g`);
      console.log(`Balance 24K: ${matchingRecord.balance_weight_24k}g vs 9.754g`);
      console.log(`Balance 22K: ${matchingRecord.balance_weight_22k}g vs 110.260g`);
      
      // Calculate what the values should be
      console.log('\n=== CALCULATION ANALYSIS ===');
      const procured24k = 113.195;
      const balance22k = procured24k * 0.916; // 22K conversion
      console.log(`Expected Balance 22K: ${balance22k.toFixed(3)}g (${procured24k} * 0.916)`);
      console.log(`Actual Balance 22K in DB: ${matchingRecord.balance_weight_22k}g`);
      console.log(`Physical Record Balance 22K: 110.260g`);
      
    } else {
      console.log('\n❌ No matching record found in database');
    }

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
checkInventoryData();
