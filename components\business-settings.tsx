"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Settings, Calculator, Percent, AlertCircle, Save, RotateCcw } from "lucide-react"
import { useBusinessSettings } from "@/hooks/use-business-settings"

import type { BusinessSettings } from "@/hooks/use-business-settings"

export default function BusinessSettings() {
  const {
    settings,
    loading: settingsLoading,
    updateSettings,
    resetToDefaults: resetSettings
  } = useBusinessSettings()

  const [loading, setLoading] = useState(false)
  const [saved, setSaved] = useState(false)
  const [error, setError] = useState("")

  const saveSettings = async () => {
    setLoading(true)
    setSaved(false)
    setError("")

    try {
      // Settings are already saved via updateSettings, just show confirmation
      setSaved(true)
      setTimeout(() => setSaved(false), 3000)
    } catch (error) {
      console.error("Error saving settings:", error)
      setError("Failed to save settings")
    } finally {
      setLoading(false)
    }
  }

  const resetToDefaults = () => {
    resetSettings()
    setSaved(true)
    setTimeout(() => setSaved(false), 3000)
  }

  const updateSetting = (key: keyof BusinessSettings, value: any) => {
    updateSettings({ [key]: value })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Business Settings</h2>
          <p className="text-muted-foreground">
            Configure business rules, conversion factors, and calculation methods
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={resetToDefaults}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
          <Button onClick={saveSettings} disabled={loading}>
            <Save className="h-4 w-4 mr-2" />
            {loading ? "Saving..." : "Save Settings"}
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {saved && (
        <Alert>
          <Settings className="h-4 w-4" />
          <AlertDescription>Settings saved successfully!</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="conversion" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="conversion">Conversion Factors</TabsTrigger>
          <TabsTrigger value="wastage">Wastage Rules</TabsTrigger>
          <TabsTrigger value="business">Business Rules</TabsTrigger>
          <TabsTrigger value="pricing">Pricing & Tax</TabsTrigger>
        </TabsList>

        <TabsContent value="conversion" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Purity Conversion Factors
              </CardTitle>
              <CardDescription>
                Configure the conversion factors used for different gold purities
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">From 24K to Other Purities</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="conv_24_22">24K to 22K Factor</Label>
                      <Input
                        id="conv_24_22"
                        type="number"
                        step="0.001"
                        value={settings.conversion_24k_to_22k}
                        onChange={(e) => updateSetting('conversion_24k_to_22k', parseFloat(e.target.value))}
                        className="w-24"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="conv_24_18">24K to 18K Factor</Label>
                      <Input
                        id="conv_24_18"
                        type="number"
                        step="0.001"
                        value={settings.conversion_24k_to_18k}
                        onChange={(e) => updateSetting('conversion_24k_to_18k', parseFloat(e.target.value))}
                        className="w-24"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">To 24K from Other Purities</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="conv_22_24">22K to 24K Factor</Label>
                      <Input
                        id="conv_22_24"
                        type="number"
                        step="0.001"
                        value={settings.conversion_22k_to_24k}
                        onChange={(e) => updateSetting('conversion_22k_to_24k', parseFloat(e.target.value))}
                        className="w-24"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="conv_18_24">18K to 24K Factor</Label>
                      <Input
                        id="conv_18_24"
                        type="number"
                        step="0.001"
                        value={settings.conversion_18k_to_24k}
                        onChange={(e) => updateSetting('conversion_18k_to_24k', parseFloat(e.target.value))}
                        className="w-24"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">Conversion Examples</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p>100g of 24K gold = {(100 * settings.conversion_24k_to_22k).toFixed(3)}g of 22K gold</p>
                    <p>100g of 24K gold = {(100 * settings.conversion_24k_to_18k).toFixed(3)}g of 18K gold</p>
                  </div>
                  <div>
                    <p>100g of 22K gold = {(100 * settings.conversion_22k_to_24k).toFixed(3)}g of 24K gold</p>
                    <p>100g of 18K gold = {(100 * settings.conversion_18k_to_24k).toFixed(3)}g of 24K gold</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="wastage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Percent className="h-5 w-5" />
                Default Wastage Rates
              </CardTitle>
              <CardDescription>
                Set default wastage percentages for different types of inventory
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="wastage_bar">Bar Wastage (%)</Label>
                  <Input
                    id="wastage_bar"
                    type="number"
                    step="0.1"
                    value={settings.wastage_rate_bar}
                    onChange={(e) => updateSetting('wastage_rate_bar', parseFloat(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">Typical: 0.5% - 1.0%</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="wastage_jewel">Jewel Wastage (%)</Label>
                  <Input
                    id="wastage_jewel"
                    type="number"
                    step="0.1"
                    value={settings.wastage_rate_jewel}
                    onChange={(e) => updateSetting('wastage_rate_jewel', parseFloat(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">Typical: 1.5% - 3.0%</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="wastage_old">Old Jewel Wastage (%)</Label>
                  <Input
                    id="wastage_old"
                    type="number"
                    step="0.1"
                    value={settings.wastage_rate_old_jewel}
                    onChange={(e) => updateSetting('wastage_rate_old_jewel', parseFloat(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">Typical: 2.5% - 5.0%</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-semibold">Wastage Alert Settings</h4>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enable_wastage_alerts">Enable Wastage Alerts</Label>
                    <p className="text-sm text-muted-foreground">Get notified when wastage exceeds threshold</p>
                  </div>
                  <Switch
                    id="enable_wastage_alerts"
                    checked={settings.enable_wastage_alerts}
                    onCheckedChange={(checked) => updateSetting('enable_wastage_alerts', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="high_wastage_threshold">High Wastage Threshold (%)</Label>
                  <Input
                    id="high_wastage_threshold"
                    type="number"
                    step="0.1"
                    value={settings.high_wastage_threshold}
                    onChange={(e) => updateSetting('high_wastage_threshold', parseFloat(e.target.value))}
                    className="w-24"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="business" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Business Rules & Calculations
              </CardTitle>
              <CardDescription>
                Configure how the system handles calculations and business logic
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="auto_calculate">Auto-Calculate Balances</Label>
                    <p className="text-sm text-muted-foreground">Automatically update balances when sales/wastage recorded</p>
                  </div>
                  <Switch
                    id="auto_calculate"
                    checked={settings.auto_calculate_balances}
                    onCheckedChange={(checked) => updateSetting('auto_calculate_balances', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="track_stone">Track Stone Separately</Label>
                    <p className="text-sm text-muted-foreground">Maintain separate records for stone weight and value</p>
                  </div>
                  <Switch
                    id="track_stone"
                    checked={settings.track_stone_separately}
                    onCheckedChange={(checked) => updateSetting('track_stone_separately', checked)}
                  />
                </div>

                <Separator />

                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="low_stock_threshold">Low Stock Threshold (g)</Label>
                    <Input
                      id="low_stock_threshold"
                      type="number"
                      step="0.1"
                      value={settings.low_stock_threshold}
                      onChange={(e) => updateSetting('low_stock_threshold', parseFloat(e.target.value))}
                    />
                    <p className="text-xs text-muted-foreground">Alert when balance falls below this amount</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="rounding_precision">Rounding Precision (decimal places)</Label>
                    <Input
                      id="rounding_precision"
                      type="number"
                      min="0"
                      max="6"
                      value={settings.rounding_precision}
                      onChange={(e) => updateSetting('rounding_precision', parseInt(e.target.value))}
                    />
                    <p className="text-xs text-muted-foreground">Number of decimal places for weight calculations</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pricing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Pricing & Tax Configuration
              </CardTitle>
              <CardDescription>
                Configure default pricing rules and tax settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="default_making_charges">Default Making Charges (₹/g)</Label>
                  <Input
                    id="default_making_charges"
                    type="number"
                    step="0.01"
                    value={settings.default_making_charges}
                    onChange={(e) => updateSetting('default_making_charges', parseFloat(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">Default making charges per gram</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="price_variance_threshold">Price Variance Alert (%)</Label>
                  <Input
                    id="price_variance_threshold"
                    type="number"
                    step="0.1"
                    value={settings.price_variance_threshold}
                    onChange={(e) => updateSetting('price_variance_threshold', parseFloat(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">Alert when price varies by this percentage</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="apply_gst">Apply GST</Label>
                    <p className="text-sm text-muted-foreground">Include GST in price calculations</p>
                  </div>
                  <Switch
                    id="apply_gst"
                    checked={settings.apply_gst}
                    onCheckedChange={(checked) => updateSetting('apply_gst', checked)}
                  />
                </div>

                {settings.apply_gst && (
                  <div className="flex items-center justify-between">
                    <Label htmlFor="gst_rate">GST Rate (%)</Label>
                    <Input
                      id="gst_rate"
                      type="number"
                      step="0.1"
                      value={settings.gst_rate}
                      onChange={(e) => updateSetting('gst_rate', parseFloat(e.target.value))}
                      className="w-24"
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
