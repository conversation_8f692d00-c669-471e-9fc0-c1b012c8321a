const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function fixJewelWastageRate() {
  let connection;

  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME
  };

  try {
    console.log('🔌 Connecting to database...');
    connection = await mysql.createConnection(config);
    console.log(`📊 Connected to database: ${process.env.DB_NAME}`);

    console.log('\n=== FIXING JEWEL WASTAGE RATE ===\n');

    // Check current value
    const [currentValue] = await connection.execute(`
      SELECT setting_key, setting_value, updated_at
      FROM settings 
      WHERE category = 'wastage' AND setting_key = 'wastage_rate_jewel'
    `);

    if (currentValue.length > 0) {
      let value;
      try {
        value = typeof currentValue[0].setting_value === 'string' 
          ? JSON.parse(currentValue[0].setting_value) 
          : currentValue[0].setting_value;
      } catch (error) {
        value = currentValue[0].setting_value;
      }
      
      console.log(`📊 Current wastage_rate_jewel: ${value}%`);
      console.log(`📅 Last updated: ${currentValue[0].updated_at}`);
    }

    // Update to correct value
    console.log('🔧 Updating wastage_rate_jewel to 2.0%...');
    
    const [result] = await connection.execute(`
      UPDATE settings 
      SET setting_value = ?, updated_at = CURRENT_TIMESTAMP, updated_by = 'manual_fix'
      WHERE category = 'wastage' AND setting_key = 'wastage_rate_jewel'
    `, [JSON.stringify(2.0)]);

    if (result.affectedRows > 0) {
      console.log('✅ Successfully updated wastage_rate_jewel to 2.0%');
    } else {
      console.log('❌ Failed to update wastage_rate_jewel - setting not found');
    }

    // Verify the update
    console.log('\n📊 Verifying update...');
    const [updatedValue] = await connection.execute(`
      SELECT setting_key, setting_value, updated_at, updated_by
      FROM settings 
      WHERE category = 'wastage' AND setting_key = 'wastage_rate_jewel'
    `);

    if (updatedValue.length > 0) {
      let value;
      try {
        value = typeof updatedValue[0].setting_value === 'string' 
          ? JSON.parse(updatedValue[0].setting_value) 
          : updatedValue[0].setting_value;
      } catch (error) {
        value = updatedValue[0].setting_value;
      }
      
      console.log(`✅ Updated wastage_rate_jewel: ${value}%`);
      console.log(`📅 Updated at: ${updatedValue[0].updated_at}`);
      console.log(`👤 Updated by: ${updatedValue[0].updated_by}`);
    }

    // Test API response
    console.log('\n🌐 Testing API response...');
    try {
      const response = await fetch('http://localhost:3000/api/settings?category=wastage&key=wastage_rate_jewel');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          console.log(`✅ API response: ${data.data.value}%`);
          
          if (data.data.value === 2) {
            console.log('🎉 SUCCESS: wastage_rate_jewel is now correctly set to 2%');
          } else {
            console.log(`❌ API still returning ${data.data.value}% instead of 2%`);
          }
        } else {
          console.log('❌ API Error:', data.error);
        }
      } else {
        console.log('❌ API Request failed:', response.status);
      }
    } catch (error) {
      console.log('❌ API Request error:', error.message);
    }

    // Show all wastage rates
    console.log('\n📊 All current wastage rates:');
    const [allRates] = await connection.execute(`
      SELECT setting_key, setting_value
      FROM settings 
      WHERE category = 'wastage'
      ORDER BY setting_key
    `);

    allRates.forEach(setting => {
      let value;
      try {
        value = typeof setting.setting_value === 'string' 
          ? JSON.parse(setting.setting_value) 
          : setting.setting_value;
      } catch (error) {
        value = setting.setting_value;
      }
      
      console.log(`   ${setting.setting_key}: ${value}%`);
    });

    console.log('\n=== JEWEL WASTAGE RATE FIXED ===');
    console.log('✅ wastage_rate_jewel has been updated to 2.0%');
    console.log('🔄 Please refresh your browser to see the updated value');
    console.log('📝 The inventory form should now show 2% for Jewel items');

  } catch (error) {
    console.error('❌ Error fixing jewel wastage rate:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the fix
fixJewelWastageRate();
