const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function fixInventorySchema() {
  console.log('🔧 FIXING INVENTORY SCHEMA');
  console.log('==========================\n');

  let connection;
  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jewellery_wholesale'
  };

  try {
    connection = await mysql.createConnection(config);
    console.log(`📊 Connected to database: ${config.database}`);

    // Check what columns are missing
    const [existingColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory'
    `, [config.database]);

    const existing = existingColumns.map(col => col.COLUMN_NAME);
    console.log(`\n📋 Found ${existing.length} existing columns`);

    // Columns that the model needs but might be missing
    const requiredColumns = [
      {
        name: 'with_stone_tunch_percentage',
        definition: 'DECIMAL(5,2) DEFAULT NULL COMMENT "Tunch percentage for with stone weight"'
      },
      {
        name: 'without_stone_tunch_percentage', 
        definition: 'DECIMAL(5,2) DEFAULT NULL COMMENT "Tunch percentage for without stone weight"'
      }
    ];

    console.log('\n🔍 CHECKING REQUIRED COLUMNS:');
    const missingColumns = [];

    for (const col of requiredColumns) {
      if (existing.includes(col.name)) {
        console.log(`✅ ${col.name} - EXISTS`);
      } else {
        console.log(`❌ ${col.name} - MISSING`);
        missingColumns.push(col);
      }
    }

    // Add missing columns
    if (missingColumns.length > 0) {
      console.log('\n🔧 ADDING MISSING COLUMNS:');
      
      for (const col of missingColumns) {
        try {
          const alterQuery = `ALTER TABLE inventory ADD COLUMN ${col.name} ${col.definition}`;
          console.log(`   Adding: ${col.name}`);
          console.log(`   SQL: ${alterQuery}`);
          
          await connection.execute(alterQuery);
          console.log(`   ✅ Successfully added ${col.name}`);
        } catch (error) {
          console.log(`   ❌ Failed to add ${col.name}: ${error.message}`);
        }
      }
    } else {
      console.log('\n✅ All required columns already exist');
    }

    // Verify the schema is now correct
    console.log('\n🧪 TESTING INVENTORY INSERT:');
    
    try {
      // Test the exact query that the model uses
      const testQuery = `
        INSERT INTO inventory (
          supplier_id, product_name, product_type, metal_type, form_type,
          jewel_type, jewel_category, with_stone_weight, without_stone_weight,
          with_stone_cost, without_stone_cost, with_stone_tunch_percentage,
          without_stone_tunch_percentage, procured_in_24k, balance_weight_24k, balance_weight_22k
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const testParams = [
        1, // supplier_id
        'Test Item Schema Fix', // product_name
        'Ring', // product_type
        'Gold', // metal_type
        'Jewel', // form_type
        'Traditional', // jewel_type
        'Wedding', // jewel_category
        10.5, // with_stone_weight
        9.8, // without_stone_weight
        50000, // with_stone_cost
        45000, // without_stone_cost
        95.5, // with_stone_tunch_percentage
        96.2, // without_stone_tunch_percentage
        9.5, // procured_in_24k
        9.5, // balance_weight_24k
        8.7 // balance_weight_22k
      ];
      
      const [result] = await connection.execute(testQuery, testParams);
      console.log('✅ Test insert successful!');
      console.log(`   Inserted ID: ${result.insertId}`);
      
      // Clean up test record
      await connection.execute('DELETE FROM inventory WHERE product_name = ?', ['Test Item Schema Fix']);
      console.log('✅ Test record cleaned up');
      
    } catch (error) {
      console.log(`❌ Test insert failed: ${error.message}`);
      console.log('   This indicates there may still be schema issues');
    }

    // Show final schema
    console.log('\n📋 FINAL INVENTORY SCHEMA:');
    const [finalColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory'
      ORDER BY ORDINAL_POSITION
    `, [config.database]);

    finalColumns.forEach(col => {
      const nullable = col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL';
      const defaultVal = col.COLUMN_DEFAULT ? ` DEFAULT ${col.COLUMN_DEFAULT}` : '';
      console.log(`   ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${nullable}${defaultVal}`);
    });

    console.log('\n🎯 SCHEMA FIX COMPLETE');
    console.log('======================');
    
    if (missingColumns.length > 0) {
      console.log('✅ Missing columns have been added');
      console.log('✅ Inventory model should now work correctly');
      console.log('✅ Try adding an inventory item again');
    } else {
      console.log('✅ Schema was already correct');
      console.log('🔍 The 500 error may be due to other issues');
    }

    console.log('\n📋 NEXT STEPS:');
    console.log('1. Restart the Next.js server');
    console.log('2. Try adding an inventory item');
    console.log('3. Check server logs for any remaining errors');

  } catch (error) {
    console.error('❌ Schema fix failed:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the schema fix
fixInventorySchema();
