import { type NextRequest, NextResponse } from "next/server"
import { BillModel } from "@/lib/models/bill"
import { CustomerModel } from "@/lib/models/customer"
import { InventoryModel } from "@/lib/models/inventory"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search")
    const customerId = searchParams.get("customer_id")
    const startDate = searchParams.get("start_date")
    const endDate = searchParams.get("end_date")

    let bills
    if (search) {
      bills = await BillModel.search(search)
    } else if (customerId) {
      bills = await BillModel.getByCustomer(Number.parseInt(customerId))
    } else if (startDate && endDate) {
      bills = await BillModel.getByDateRange(startDate, endDate)
    } else {
      bills = await BillModel.getAll()
    }

    return NextResponse.json({ success: true, data: bills })
  } catch (error) {
    console.error("Error fetching bills:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch bills" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.customer_id || !data.product_name) {
      return NextResponse.json({ success: false, error: "Customer ID and product name are required" }, { status: 400 })
    }

    // Validate numeric fields
    if (data.total_amount && (isNaN(data.total_amount) || data.total_amount < 0)) {
      return NextResponse.json({ success: false, error: "Total amount must be a valid positive number" }, { status: 400 })
    }

    if (data.without_stone && (isNaN(data.without_stone) || data.without_stone < 0)) {
      return NextResponse.json({ success: false, error: "Weight must be a valid positive number" }, { status: 400 })
    }

    // Generate bill number if not provided
    if (!data.bill_number) {
      data.bill_number = await BillModel.generateBillNumber()
    }

    const billId = await BillModel.create(data)

    // Update customer total purchases
    if (data.total_amount) {
      await CustomerModel.updateTotalPurchases(data.customer_id, data.total_amount)
    }

    // Update inventory sold values if inventory item is specified
    if (data.inventory_item_id && (data.with_stone || data.without_stone)) {
      try {
        const inventoryUpdated = await InventoryModel.updateSoldValues(
          data.inventory_item_id,
          data.with_stone || 0,
          data.without_stone || 0
        )

        if (!inventoryUpdated) {
          console.warn(`Failed to update inventory ${data.inventory_item_id} for bill ${billId}`)
        }
      } catch (inventoryError) {
        console.error(`Error updating inventory for bill ${billId}:`, inventoryError)
        // Don't fail the bill creation if inventory update fails
      }
    }

    const bill = await BillModel.getById(billId)

    return NextResponse.json({ success: true, data: bill }, { status: 201 })
  } catch (error) {
    console.error("Error creating bill:", error)
    return NextResponse.json({ success: false, error: "Failed to create bill" }, { status: 500 })
  }
}
