const mysql = require('mysql2/promise');
require('dotenv').config();

async function updateInventoryCompleteSchema() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jewellery_wholesale'
    });

    console.log('🔗 Connected to database:', process.env.DB_NAME || 'jewellery_wholesale');

    // Check current inventory table structure
    console.log('\n📊 CHECKING CURRENT INVENTORY TABLE STRUCTURE:');
    const [currentColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME || 'jewellery_wholesale']);

    console.log('Current columns:');
    currentColumns.forEach(col => {
      console.log(`  - ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    // Define all required columns for complete inventory system
    const requiredColumns = [
      // Basic info
      { name: 'id', definition: 'INT AUTO_INCREMENT PRIMARY KEY' },
      { name: 'supplier_id', definition: 'INT' },
      { name: 'product_name', definition: 'VARCHAR(255) NOT NULL' },
      { name: 'product_type', definition: 'VARCHAR(255)' },
      
      // Metal and form information
      { name: 'metal_type', definition: "ENUM('Gold', 'Silver', 'Platinum') DEFAULT 'Gold'" },
      { name: 'form_type', definition: "ENUM('Bar', 'Jewel', 'Old Jewel') DEFAULT 'Jewel'" },
      { name: 'jewel_type', definition: "ENUM('With Stone', 'Without Stone') NULL" },
      { name: 'jewel_category', definition: 'VARCHAR(100) NULL' },
      
      // Physical weights
      { name: 'with_stone_weight', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'without_stone_weight', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'stone_weight', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      
      // Cost percentages (tunch percentages)
      { name: 'with_stone_cost', definition: 'DECIMAL(10,2) DEFAULT 0.00' },
      { name: 'without_stone_cost', definition: 'DECIMAL(10,2) DEFAULT 0.00' },
      { name: 'with_stone_tunch_percentage', definition: 'DECIMAL(5,2) DEFAULT 0.00' },
      { name: 'without_stone_tunch_percentage', definition: 'DECIMAL(5,2) DEFAULT 0.00' },
      
      // Gold weights
      { name: 'procured_in_24k', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'balance_weight_24k', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'balance_weight_22k', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      
      // Sold values (NEW FIELDS)
      { name: 'stone_weight_22k', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'sold_gold_weight_22k', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'sold_gold_weight_24k', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      
      // Balance in stock (NEW FIELD)
      { name: 'balance_gold_weight_22k', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      
      // Legacy sold values (keep for compatibility)
      { name: 'sold_value_with_stone', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'sold_value_without_stone', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'sold_value_24k', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'sold_value_22k', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'sold_value_18k', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      
      // Business parameters
      { name: 'wastage_percentage', definition: 'DECIMAL(5,2) DEFAULT 0.00' },
      { name: 'expected_processing_loss', definition: 'DECIMAL(10,3) DEFAULT 0.000' },
      { name: 'making_charges', definition: 'DECIMAL(10,2) DEFAULT 0.00' },
      
      // Status and timestamps
      { name: 'status', definition: "ENUM('Available', 'Low Stock', 'Out of Stock') DEFAULT 'Available'" },
      { name: 'created_at', definition: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP' },
      { name: 'updated_at', definition: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP' }
    ];

    // Find missing columns
    const existingColumnNames = currentColumns.map(col => col.COLUMN_NAME);
    const missingColumns = requiredColumns.filter(col => !existingColumnNames.includes(col.name));

    if (missingColumns.length > 0) {
      console.log('\n🔧 ADDING MISSING COLUMNS:');
      console.log('==========================');
      
      for (const col of missingColumns) {
        try {
          const alterQuery = `ALTER TABLE inventory ADD COLUMN ${col.name} ${col.definition}`;
          console.log(`📝 Adding: ${col.name}`);
          console.log(`   SQL: ${alterQuery}`);
          
          await connection.execute(alterQuery);
          console.log(`   ✅ Successfully added ${col.name}`);
        } catch (error) {
          console.log(`   ❌ Failed to add ${col.name}: ${error.message}`);
        }
      }
    } else {
      console.log('\n✅ All required columns already exist');
    }

    // Add indexes for performance
    console.log('\n🚀 ADDING PERFORMANCE INDEXES:');
    console.log('==============================');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_inventory_supplier_id ON inventory(supplier_id)',
      'CREATE INDEX IF NOT EXISTS idx_inventory_product_name ON inventory(product_name)',
      'CREATE INDEX IF NOT EXISTS idx_inventory_status ON inventory(status)',
      'CREATE INDEX IF NOT EXISTS idx_inventory_metal_type ON inventory(metal_type)',
      'CREATE INDEX IF NOT EXISTS idx_inventory_form_type ON inventory(form_type)',
      'CREATE INDEX IF NOT EXISTS idx_inventory_jewel_type ON inventory(jewel_type)',
      'CREATE INDEX IF NOT EXISTS idx_inventory_created_at ON inventory(created_at)'
    ];

    for (const indexQuery of indexes) {
      try {
        await connection.execute(indexQuery);
        console.log(`✅ Index created: ${indexQuery.split(' ')[4]}`);
      } catch (error) {
        console.log(`⚠️  Index might already exist: ${error.message}`);
      }
    }

    // Verify the final schema
    console.log('\n📊 FINAL INVENTORY TABLE SCHEMA:');
    console.log('=================================');
    const [finalColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME || 'jewellery_wholesale']);

    console.log('Complete column list:');
    finalColumns.forEach((col, index) => {
      console.log(`${(index + 1).toString().padStart(2, ' ')}. ${col.COLUMN_NAME.padEnd(30, ' ')} ${col.DATA_TYPE} ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    console.log('\n🎯 NEW FIELDS FOR COMPLETE INVENTORY SYSTEM:');
    console.log('============================================');
    console.log('✅ stone_weight_22k - Stone weight in 22K');
    console.log('✅ sold_gold_weight_22k - Sold gold weight in 22K');
    console.log('✅ sold_gold_weight_24k - Sold gold weight in 24K');
    console.log('✅ balance_gold_weight_22k - Final balance gold weight in 22K');

    console.log('\n📋 TABLE STRUCTURE NOW SUPPORTS:');
    console.log('=================================');
    console.log('• Description (Supplier Name, Location, Product Name)');
    console.log('• Product Type (With Stone, Without Stone)');
    console.log('• Purchase Price (With Stone Cost %, Without Stone Cost %)');
    console.log('• Procured in 24k');
    console.log('• Sold Value (Stone Weight 22k, Gold Weight 22k, Gold Weight 24k)');
    console.log('• Balance in Stock (Gold Weight in 22k)');

    console.log('\n🎉 INVENTORY SCHEMA UPDATE COMPLETE!');
    console.log('====================================');
    console.log('The database now supports all fields from your reference table.');
    console.log('Ready for comprehensive inventory management!');

  } catch (error) {
    console.error('❌ Error updating schema:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the script
updateInventoryCompleteSchema();
