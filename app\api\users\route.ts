import { type NextRequest, NextResponse } from "next/server"
import { UserModel } from "@/lib/models/user"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search")

    let users
    if (search) {
      users = await UserModel.search(search)
    } else {
      users = await UserModel.getAll()
    }

    return NextResponse.json({ success: true, data: users })
  } catch (error) {
    console.error("Error fetching users:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch users" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.name || !data.email || !data.role) {
      return NextResponse.json({ success: false, error: "Name, email, and role are required" }, { status: 400 })
    }

    const userId = await UserModel.create(data)
    const user = await UserModel.getById(userId)

    return NextResponse.json({ success: true, data: user }, { status: 201 })
  } catch (error) {
    console.error("Error creating user:", error)
    return NextResponse.json({ success: false, error: "Failed to create user" }, { status: 500 })
  }
}
