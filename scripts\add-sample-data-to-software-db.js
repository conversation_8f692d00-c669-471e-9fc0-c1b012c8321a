const mysql = require('mysql2/promise');
require('dotenv').config();

async function addSampleDataToSoftwareDB() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME
    });

    console.log('Connected to jewellery_wholesale_software database');

    // Check if suppliers already exist
    const [existingSuppliers] = await connection.execute('SELECT COUNT(*) as count FROM suppliers');
    if (existingSuppliers[0].count === 0) {
      console.log('Adding sample suppliers...');
      
      const suppliers = [
        {
          name: 'Golden Jewels Pvt Ltd',
          location: 'Mumbai',
          contact_person: '<PERSON><PERSON>',
          phone: '+91-9876543210',
          email: 'r<PERSON><PERSON>@goldenjewels.com',
          speciality: 'Gold Chains and Bangles'
        },
        {
          name: 'Diamond Palace',
          location: 'Surat',
          contact_person: '<PERSON><PERSON> Shah',
          phone: '+91-9876543211',
          email: '<EMAIL>',
          speciality: 'Diamond Jewelry'
        },
        {
          name: 'Silver Craft Industries',
          location: 'Jaipur',
          contact_person: 'Amit Sharma',
          phone: '+91-9876543212',
          email: '<EMAIL>',
          speciality: 'Silver Ornaments'
        },
        {
          name: 'Traditional Jewelers',
          location: 'Chennai',
          contact_person: 'Lakshmi Narayanan',
          phone: '+91-9876543213',
          email: '<EMAIL>',
          speciality: 'Temple Jewelry'
        },
        {
          name: 'Modern Gold Works',
          location: 'Delhi',
          contact_person: 'Vikram Singh',
          phone: '+91-9876543214',
          email: '<EMAIL>',
          speciality: 'Contemporary Designs'
        }
      ];

      for (const supplier of suppliers) {
        await connection.execute(`
          INSERT INTO suppliers (name, location, contact_person, phone, email, speciality)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [supplier.name, supplier.location, supplier.contact_person, supplier.phone, supplier.email, supplier.speciality]);
      }

      console.log(`✅ Added ${suppliers.length} sample suppliers`);
    } else {
      console.log(`✅ Found ${existingSuppliers[0].count} existing suppliers`);
    }

    // Check if customers already exist
    const [existingCustomers] = await connection.execute('SELECT COUNT(*) as count FROM customers');
    if (existingCustomers[0].count === 0) {
      console.log('Adding sample customers...');
      
      const customers = [
        {
          name: 'Retail Gold Store',
          location: 'Mumbai',
          contact_person: 'Suresh Patel',
          phone: '+91-9876543220',
          email: '<EMAIL>'
        },
        {
          name: 'Bridal Jewelry Shop',
          location: 'Delhi',
          contact_person: 'Meera Gupta',
          phone: '+91-9876543221',
          email: '<EMAIL>'
        },
        {
          name: 'Fashion Accessories',
          location: 'Bangalore',
          contact_person: 'Ravi Kumar',
          phone: '+91-9876543222',
          email: '<EMAIL>'
        }
      ];

      for (const customer of customers) {
        await connection.execute(`
          INSERT INTO customers (name, location, contact_person, phone, email)
          VALUES (?, ?, ?, ?, ?)
        `, [customer.name, customer.location, customer.contact_person, customer.phone, customer.email]);
      }

      console.log(`✅ Added ${customers.length} sample customers`);
    } else {
      console.log(`✅ Found ${existingCustomers[0].count} existing customers`);
    }

    // Check if gold rates exist
    const [existingRates] = await connection.execute('SELECT COUNT(*) as count FROM gold_rates');
    if (existingRates[0].count === 0) {
      console.log('Adding current gold rates...');
      const today = new Date().toISOString().split('T')[0];
      await connection.execute(`
        INSERT INTO gold_rates (rate_24k, rate_22k, rate_18k, rate_date)
        VALUES (?, ?, ?, ?)
      `, [7200.00, 6595.00, 5400.00, today]);
      console.log('✅ Added current gold rates');
    } else {
      console.log(`✅ Found ${existingRates[0].count} existing gold rate records`);
    }

    console.log('\n🎉 Sample data setup completed for jewellery_wholesale_software database!');

  } catch (error) {
    console.error('Error adding sample data:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
addSampleDataToSoftwareDB();
