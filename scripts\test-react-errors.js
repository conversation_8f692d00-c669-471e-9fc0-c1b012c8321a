const puppeteer = require('puppeteer');

async function testReactErrors() {
  console.log('🧪 TESTING REACT ERRORS IN BROWSER');
  console.log('==================================\n');

  let browser;
  let page;

  try {
    // Launch browser
    browser = await puppeteer.launch({ 
      headless: false, // Set to true for headless mode
      devtools: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    page = await browser.newPage();
    
    // Listen for console errors
    const consoleErrors = [];
    const jsErrors = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
        console.log(`❌ Console Error: ${msg.text()}`);
      }
    });
    
    page.on('pageerror', error => {
      jsErrors.push(error.message);
      console.log(`❌ JavaScript Error: ${error.message}`);
    });
    
    // Navigate to the application
    console.log('🌐 Loading application...');
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    // Wait for the page to fully load
    await page.waitForTimeout(3000);
    
    console.log('✅ Page loaded successfully');
    
    // Test navigation to different pages
    const pagesToTest = [
      { name: 'Home', url: 'http://localhost:3000/' },
      { name: 'Settings', url: 'http://localhost:3000/settings' },
      { name: 'Reports', url: 'http://localhost:3000/reports' },
      { name: 'Sales Wastage', url: 'http://localhost:3000/sales-wastage' }
    ];
    
    for (const testPage of pagesToTest) {
      console.log(`\n🔍 Testing ${testPage.name} page...`);
      
      const errorsBefore = jsErrors.length;
      
      await page.goto(testPage.url, { 
        waitUntil: 'networkidle2',
        timeout: 15000 
      });
      
      await page.waitForTimeout(2000);
      
      const errorsAfter = jsErrors.length;
      
      if (errorsAfter === errorsBefore) {
        console.log(`✅ ${testPage.name}: No new errors`);
      } else {
        console.log(`❌ ${testPage.name}: ${errorsAfter - errorsBefore} new errors`);
      }
    }
    
    // Test specific React functionality
    console.log('\n🧩 Testing React Components...');
    
    // Go back to home page
    await page.goto('http://localhost:3000/', { 
      waitUntil: 'networkidle2',
      timeout: 15000 
    });
    
    // Test if React components are rendering
    const reactTests = [
      {
        name: 'Main Navigation',
        selector: 'nav',
        description: 'Check if navigation is rendered'
      },
      {
        name: 'Dashboard Cards',
        selector: '[data-testid="dashboard-card"], .card',
        description: 'Check if dashboard cards are rendered'
      },
      {
        name: 'Buttons',
        selector: 'button',
        description: 'Check if buttons are rendered'
      }
    ];
    
    for (const test of reactTests) {
      try {
        const elements = await page.$$(test.selector);
        if (elements.length > 0) {
          console.log(`✅ ${test.name}: ${elements.length} elements found`);
        } else {
          console.log(`⚠️  ${test.name}: No elements found`);
        }
      } catch (error) {
        console.log(`❌ ${test.name}: Error - ${error.message}`);
      }
    }
    
    // Test settings page specifically (where the original error occurred)
    console.log('\n⚙️  Testing Settings Page Functionality...');
    
    await page.goto('http://localhost:3000/settings', { 
      waitUntil: 'networkidle2',
      timeout: 15000 
    });
    
    await page.waitForTimeout(2000);
    
    // Check if settings tabs are working
    try {
      const tabs = await page.$$('[role="tab"]');
      console.log(`✅ Settings Tabs: ${tabs.length} tabs found`);
      
      // Try clicking on different tabs
      if (tabs.length > 1) {
        await tabs[1].click();
        await page.waitForTimeout(1000);
        console.log('✅ Tab switching: Working');
      }
    } catch (error) {
      console.log(`❌ Settings Tabs: Error - ${error.message}`);
    }
    
    // Final error summary
    console.log('\n📊 FINAL ERROR SUMMARY');
    console.log('=====================');
    
    console.log(`\n🚨 JavaScript Errors: ${jsErrors.length}`);
    if (jsErrors.length > 0) {
      jsErrors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
    
    console.log(`\n📝 Console Errors: ${consoleErrors.length}`);
    if (consoleErrors.length > 0) {
      consoleErrors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
    
    // Check for specific useEffect error
    const useEffectErrors = jsErrors.filter(error => 
      error.includes('useEffect is not defined') || 
      error.includes('ReferenceError: useEffect')
    );
    
    if (useEffectErrors.length === 0) {
      console.log('\n🎉 SUCCESS: No useEffect errors found!');
      console.log('✅ React hooks are working correctly');
    } else {
      console.log('\n❌ FAILURE: useEffect errors still present');
      useEffectErrors.forEach(error => {
        console.log(`   - ${error}`);
      });
    }
    
    // Overall status
    if (jsErrors.length === 0) {
      console.log('\n🎉 OVERALL STATUS: EXCELLENT');
      console.log('✅ No JavaScript errors detected');
      console.log('✅ All React components working correctly');
      console.log('✅ Application is stable and ready for use');
    } else if (jsErrors.length <= 2) {
      console.log('\n✅ OVERALL STATUS: GOOD');
      console.log('✅ Minor issues detected but application is functional');
      console.log('⚠️  Consider investigating the reported errors');
    } else {
      console.log('\n⚠️  OVERALL STATUS: NEEDS ATTENTION');
      console.log('❌ Multiple errors detected');
      console.log('❌ Application may have stability issues');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Check if puppeteer is available
try {
  require('puppeteer');
  testReactErrors();
} catch (error) {
  console.log('⚠️  Puppeteer not available, running basic API test instead...');
  
  // Fallback to basic API test
  async function basicTest() {
    try {
      const response = await fetch('http://localhost:3000/api/settings?business=true');
      const data = await response.json();
      
      if (data.success) {
        console.log('✅ API Test: Settings endpoint working');
        console.log('✅ Server is running correctly');
        console.log('✅ Database connections are working');
        console.log('\n📝 Manual Testing Required:');
        console.log('1. Open http://localhost:3000 in your browser');
        console.log('2. Check browser console for any React errors');
        console.log('3. Navigate through different pages');
        console.log('4. Test settings functionality');
      } else {
        console.log('❌ API Test: Settings endpoint failed');
      }
    } catch (error) {
      console.log('❌ API Test: Failed to connect to server');
    }
  }
  
  basicTest();
}
