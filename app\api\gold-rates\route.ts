import { type NextRequest, NextResponse } from "next/server"
import { GoldRateModel } from "@/lib/models/gold-rate"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const current = searchParams.get("current")
    const startDate = searchParams.get("start_date")
    const endDate = searchParams.get("end_date")
    const changes = searchParams.get("changes")

    let goldRates
    if (current === "true") {
      goldRates = await GoldRateModel.getCurrent()
    } else if (startDate && endDate) {
      goldRates = await GoldRateModel.getHistory(startDate, endDate)
    } else if (changes === "true") {
      goldRates = await GoldRateModel.getRateChanges(10)
    } else {
      goldRates = await GoldRateModel.getAll()
    }

    return NextResponse.json({ success: true, data: goldRates })
  } catch (error) {
    console.error("Error fetching gold rates:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch gold rates" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.rate_24k) {
      return NextResponse.json({ success: false, error: "24K rate is required" }, { status: 400 })
    }

    const goldRateId = await GoldRateModel.create(data)
    const goldRate = await GoldRateModel.getByDate(data.rate_date || new Date().toISOString().slice(0, 10))

    return NextResponse.json({ success: true, data: goldRate }, { status: 201 })
  } catch (error) {
    console.error("Error creating gold rate:", error)
    return NextResponse.json({ success: false, error: "Failed to create gold rate" }, { status: 500 })
  }
}
