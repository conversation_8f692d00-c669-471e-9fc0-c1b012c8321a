import { NextRequest, NextResponse } from "next/server"
import { SalesModel, CreateWastageRecordData } from "@/lib/models/sales"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const inventoryId = searchParams.get("inventory_id")

    if (inventoryId) {
      const wastage = await SalesModel.getWastageForInventory(parseInt(inventoryId))
      return NextResponse.json(wastage)
    }

    return NextResponse.json({ error: "Inventory ID is required" }, { status: 400 })

  } catch (error) {
    console.error("Error fetching wastage records:", error)
    return NextResponse.json(
      { error: "Failed to fetch wastage data" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data: CreateWastageRecordData = await request.json()

    // Validate required fields
    if (!data.inventory_id) {
      return NextResponse.json(
        { error: "Inventory ID is required" },
        { status: 400 }
      )
    }

    const wastageId = await SalesModel.createWastageRecord(data)

    return NextResponse.json(
      { 
        message: "Wastage record created successfully", 
        id: wastageId 
      },
      { status: 201 }
    )

  } catch (error) {
    console.error("Error creating wastage record:", error)
    return NextResponse.json(
      { error: "Failed to create wastage record" },
      { status: 500 }
    )
  }
}
