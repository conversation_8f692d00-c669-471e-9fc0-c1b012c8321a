const mysql = require('mysql2/promise');
require('dotenv').config();

async function addTestInventory() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME
    });

    console.log('Connected to database');

    // Check if suppliers exist
    const [suppliers] = await connection.execute('SELECT id, name FROM suppliers LIMIT 1');
    
    if (suppliers.length === 0) {
      console.log('No suppliers found, creating test supplier...');
      await connection.execute(`
        INSERT INTO suppliers (name, location, contact_person, phone, email, created_at, updated_at)
        VALUES ('Test Supplier', 'Mumbai', '<PERSON>', '9876543210', '<EMAIL>', NOW(), NOW())
      `);
      console.log('Test supplier created');
    }

    // Get supplier ID
    const [supplierResult] = await connection.execute('SELECT id FROM suppliers LIMIT 1');
    const supplierId = supplierResult[0].id;

    // Add test inventory items
    const testItems = [
      {
        supplier_id: supplierId,
        product_name: 'Gold Chain',
        product_type: 'Chain',
        metal_type: 'Gold',
        form_type: 'Jewel',
        jewel_type: 'Without Stone',
        jewel_category: 'Chain',
        with_stone_weight: 0,
        without_stone_weight: 50.5,
        stone_weight: 0,
        with_stone_cost: 0,
        without_stone_cost: 95.0,
        procured_in_24k: 47.475,
        balance_weight_24k: 47.475,
        balance_weight_22k: 50.5,
        status: 'Available'
      },
      {
        supplier_id: supplierId,
        product_name: 'Diamond Ring',
        product_type: 'Ring',
        metal_type: 'Gold',
        form_type: 'Jewel',
        jewel_type: 'With Stone',
        jewel_category: 'Ring',
        with_stone_weight: 15.2,
        without_stone_weight: 12.8,
        stone_weight: 2.4,
        with_stone_cost: 98.0,
        without_stone_cost: 94.0,
        procured_in_24k: 14.896,
        balance_weight_24k: 14.896,
        balance_weight_22k: 15.2,
        status: 'Available'
      },
      {
        supplier_id: supplierId,
        product_name: 'Gold Bar',
        product_type: 'Bar',
        metal_type: 'Gold',
        form_type: 'Bar',
        jewel_type: null,
        jewel_category: null,
        with_stone_weight: 0,
        without_stone_weight: 100.0,
        stone_weight: 0,
        with_stone_cost: 0,
        without_stone_cost: 99.5,
        procured_in_24k: 99.5,
        balance_weight_24k: 99.5,
        balance_weight_22k: 100.0,
        status: 'Available'
      }
    ];

    for (const item of testItems) {
      await connection.execute(`
        INSERT INTO inventory (
          supplier_id, product_name, product_type, metal_type, form_type, 
          jewel_type, jewel_category, with_stone_weight, without_stone_weight, 
          stone_weight, with_stone_cost, without_stone_cost, procured_in_24k,
          balance_weight_24k, balance_weight_22k, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        item.supplier_id, item.product_name, item.product_type, item.metal_type,
        item.form_type, item.jewel_type, item.jewel_category, item.with_stone_weight,
        item.without_stone_weight, item.stone_weight, item.with_stone_cost,
        item.without_stone_cost, item.procured_in_24k, item.balance_weight_24k,
        item.balance_weight_22k, item.status
      ]);
      
      console.log(`Added: ${item.product_name}`);
    }

    console.log('\n✅ Test inventory data added successfully!');
    console.log(`Total items added: ${testItems.length}`);

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
addTestInventory();
