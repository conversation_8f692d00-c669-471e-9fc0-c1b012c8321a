#!/usr/bin/env node

const mysql = require('mysql2/promise')

const config = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'jewellery_wholesale_software',
  port: process.env.DB_PORT || 3306,
}

async function addMetalInfoColumns() {
  let connection

  try {
    console.log("🔌 Connecting to database...")
    connection = await mysql.createConnection(config)

    console.log("📊 Adding metal information columns to inventory table...")

    // Add metal information columns
    await connection.execute(`
      ALTER TABLE inventory 
      ADD COLUMN metal_type ENUM('Gold', 'Silver', 'Platinum') DEFAULT 'Gold' AFTER product_type,
      ADD COLUMN form_type ENUM('Bar', 'Jewel', 'Old Jewel') DEFAULT 'Jewel' AFTER metal_type,
      ADD COLUMN jewel_type ENUM('With Stone', 'Without Stone') NULL AFTER form_type,
      ADD COLUMN jewel_category VARCHAR(100) NULL AFTER jewel_type
    `)

    console.log("✅ Successfully added metal information columns!")

    // Update existing records to have default values
    console.log("🔄 Updating existing records with default values...")
    
    await connection.execute(`
      UPDATE inventory 
      SET metal_type = 'Gold', 
          form_type = 'Jewel',
          jewel_type = CASE 
            WHEN with_stone_weight > 0 THEN 'With Stone' 
            ELSE 'Without Stone' 
          END,
          jewel_category = CASE 
            WHEN product_name LIKE '%Chain%' THEN 'Chain'
            WHEN product_name LIKE '%Bangle%' THEN 'Bangle'
            WHEN product_name LIKE '%Ring%' THEN 'Ring'
            WHEN product_name LIKE '%Necklace%' THEN 'Necklace'
            WHEN product_name LIKE '%Stud%' THEN 'Studs'
            WHEN product_name LIKE '%Pendant%' THEN 'Pendant'
            WHEN product_name LIKE '%Bracelet%' THEN 'Bracelet'
            WHEN product_name LIKE '%Mangalsutra%' THEN 'Mangalsutra'
            WHEN product_name LIKE '%Nose%' THEN 'Nosepin'
            WHEN product_name LIKE '%Vaddanam%' THEN 'Vaddanam'
            WHEN product_name LIKE '%Choker%' THEN 'Choker'
            WHEN product_name LIKE '%Earring%' THEN 'Earrings'
            WHEN product_name LIKE '%Haram%' THEN 'Haram'
            WHEN product_name LIKE '%Anklet%' THEN 'Anklet'
            ELSE 'Others'
          END
      WHERE metal_type IS NULL OR form_type IS NULL
    `)

    console.log("✅ Successfully updated existing records!")

    // Show updated structure
    const [columns] = await connection.execute(`
      DESCRIBE inventory
    `)

    console.log("\n📋 Updated inventory table structure:")
    console.log("=" .repeat(60))
    columns.forEach(col => {
      console.log(`${col.Field.padEnd(25)} | ${col.Type.padEnd(20)} | ${col.Null} | ${col.Default || 'NULL'}`)
    })

  } catch (error) {
    console.error("❌ Error adding metal information columns:", error.message)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
      console.log("🔌 Database connection closed")
    }
  }
}

// Run the migration
addMetalInfoColumns()
