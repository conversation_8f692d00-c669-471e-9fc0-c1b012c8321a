#!/usr/bin/env node

require("dotenv").config({ path: ".env.local" })

const mysql = require("mysql2/promise")

async function checkInventoryStock() {
  console.log("📦 Checking inventory stock levels...\n")

  const config = {
    host: process.env.DB_HOST || "localhost",
    port: Number.parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "jewellery_wholesale_software",
  }

  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(config)

    // Get inventory with supplier info
    const [inventory] = await connection.execute(`
      SELECT i.id, i.product_name, i.product_type, i.balance_weight_24k, 
             i.balance_weight_22k, i.status, s.name as supplier_name
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      ORDER BY i.id
    `)

    console.log("📊 Current Inventory Stock:")
    console.log("=" .repeat(80))
    console.log("ID | Product Name      | Type          | 24K Stock | 22K Stock | Status    | Supplier")
    console.log("-" .repeat(80))
    
    inventory.forEach(item => {
      const id = String(item.id).padEnd(2)
      const name = String(item.product_name || '').padEnd(17)
      const type = String(item.product_type || '').padEnd(13)
      const stock24k = String((Number(item.balance_weight_24k) || 0).toFixed(3) + 'g').padEnd(9)
      const stock22k = String((Number(item.balance_weight_22k) || 0).toFixed(3) + 'g').padEnd(9)
      const status = String(item.status || '').padEnd(9)
      const supplier = String(item.supplier_name || '').padEnd(15)
      
      console.log(`${id} | ${name} | ${type} | ${stock24k} | ${stock22k} | ${status} | ${supplier}`)
    })

    console.log("-" .repeat(80))
    console.log(`Total Items: ${inventory.length}`)
    
    // Check for low stock items
    const lowStock = inventory.filter(item => (Number(item.balance_weight_24k) || 0) < 10)
    const outOfStock = inventory.filter(item => (Number(item.balance_weight_24k) || 0) <= 0)
    
    if (lowStock.length > 0) {
      console.log(`\n⚠️  Low Stock Items: ${lowStock.length}`)
      lowStock.forEach(item => {
        console.log(`   - ${item.product_name}: ${(Number(item.balance_weight_24k) || 0).toFixed(3)}g`)
      })
    }
    
    if (outOfStock.length > 0) {
      console.log(`\n❌ Out of Stock Items: ${outOfStock.length}`)
      outOfStock.forEach(item => {
        console.log(`   - ${item.product_name}: ${(Number(item.balance_weight_24k) || 0).toFixed(3)}g`)
      })
    }

    await connection.end()
    
  } catch (error) {
    console.log("\n❌ Inventory check failed:")
    console.log(`   Error: ${error.message}`)
    process.exit(1)
  }
}

checkInventoryStock()
