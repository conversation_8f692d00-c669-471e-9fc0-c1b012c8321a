-- Create database schema for Jewellery Wholesale Management System

-- Suppliers table
CREATE TABLE IF NOT EXISTS suppliers (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    location VARCHAR(255),
    contact_person VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    speciality VARCHAR(255),
    total_purchases DECIMAL(15,2) DEFAULT 0,
    last_purchase_date DATE,
    status VARCHAR(20) DEFAULT 'Active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inventory table
CREATE TABLE IF NOT EXISTS inventory (
    id SERIAL PRIMARY KEY,
    supplier_id INTEGER REFERENCES suppliers(id),
    product_name VARCHAR(255) NOT NULL,
    product_type VARCHAR(255),
    with_stone_weight DECIMAL(10,3) DEFAULT 0,
    without_stone_weight DECIMAL(10,3) DEFAULT 0,
    with_stone_cost DECIMAL(5,2) DEFAULT 0,
    without_stone_cost DECIMAL(5,2) DEFAULT 0,
    procured_in_24k DECIMAL(10,3) DEFAULT 0,
    sold_value_with_stone DECIMAL(10,3) DEFAULT 0,
    sold_value_without_stone DECIMAL(10,3) DEFAULT 0,
    balance_weight_24k DECIMAL(10,3) DEFAULT 0,
    balance_weight_22k DECIMAL(10,3) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    contact_person VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    total_purchases DECIMAL(15,2) DEFAULT 0,
    last_purchase_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bills table
CREATE TABLE IF NOT EXISTS bills (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER REFERENCES customers(id),
    bill_number VARCHAR(50) UNIQUE,
    product_name VARCHAR(255),
    product_type VARCHAR(255),
    with_stone DECIMAL(10,3) DEFAULT 0,
    without_stone DECIMAL(10,3) DEFAULT 0,
    gross_weight DECIMAL(10,3) DEFAULT 0,
    stone_weight DECIMAL(10,3) DEFAULT 0,
    net_weight DECIMAL(10,3) DEFAULT 0,
    tunch_with_stone INTEGER DEFAULT 0,
    tunch_without_stone INTEGER DEFAULT 0,
    weight_in_24k DECIMAL(10,3) DEFAULT 0,
    gold_24k_price DECIMAL(10,2) DEFAULT 0,
    stone_price DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'Pending',
    bill_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Gold rates table
CREATE TABLE IF NOT EXISTS gold_rates (
    id SERIAL PRIMARY KEY,
    rate_24k DECIMAL(10,2) NOT NULL,
    rate_22k DECIMAL(10,2) NOT NULL,
    rate_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name);
CREATE INDEX IF NOT EXISTS idx_inventory_supplier_id ON inventory(supplier_id);
CREATE INDEX IF NOT EXISTS idx_inventory_product_name ON inventory(product_name);
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name);
CREATE INDEX IF NOT EXISTS idx_bills_customer_id ON bills(customer_id);
CREATE INDEX IF NOT EXISTS idx_bills_bill_date ON bills(bill_date);
CREATE INDEX IF NOT EXISTS idx_gold_rates_date ON gold_rates(rate_date);
