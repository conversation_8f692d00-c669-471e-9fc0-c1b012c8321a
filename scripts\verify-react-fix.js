const fs = require('fs');
const path = require('path');

console.log('🔍 VERIFYING REACT HOOK IMPORTS');
console.log('===============================\n');

// Components to check for React hook imports
const componentsToCheck = [
  'components/billing-system.tsx',
  'components/inventory-management-improved.tsx',
  'components/edit-inventory-dialog.tsx',
  'components/calculation-verification.tsx',
  'components/sales-wastage-management.tsx',
  'components/gold-rate-tracker.tsx',
  'components/advanced-reports.tsx',
  'hooks/use-simple-toast.ts',
  'hooks/use-database-settings.ts'
];

// React hooks to check for
const reactHooks = ['useState', 'useEffect', 'useCallback', 'useMemo', 'useRef'];

let totalIssues = 0;
let fixedIssues = 0;

console.log('📋 CHECKING REACT HOOK IMPORTS:\n');

componentsToCheck.forEach(filePath => {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  ${filePath}: File not found`);
      return;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    // Find import statements
    const importLines = lines.filter(line => 
      line.trim().startsWith('import') && 
      (line.includes('from "react"') || line.includes('from \'react\''))
    );
    
    // Find hook usage
    const usedHooks = [];
    reactHooks.forEach(hook => {
      if (content.includes(hook + '(')) {
        usedHooks.push(hook);
      }
    });
    
    if (usedHooks.length === 0) {
      console.log(`✅ ${filePath}: No React hooks used`);
      return;
    }
    
    console.log(`🔍 ${filePath}:`);
    console.log(`   Used hooks: ${usedHooks.join(', ')}`);
    
    if (importLines.length === 0) {
      console.log(`   ❌ No React imports found but hooks are used`);
      totalIssues++;
      return;
    }
    
    // Check if all used hooks are imported
    const importedHooks = [];
    importLines.forEach(line => {
      reactHooks.forEach(hook => {
        if (line.includes(hook)) {
          importedHooks.push(hook);
        }
      });
    });
    
    const missingHooks = usedHooks.filter(hook => !importedHooks.includes(hook));
    
    if (missingHooks.length > 0) {
      console.log(`   ❌ Missing imports: ${missingHooks.join(', ')}`);
      console.log(`   📝 Import line: ${importLines[0]}`);
      totalIssues++;
    } else {
      console.log(`   ✅ All hooks properly imported`);
      console.log(`   📝 Import line: ${importLines[0]}`);
      fixedIssues++;
    }
    
  } catch (error) {
    console.log(`❌ ${filePath}: Error reading file - ${error.message}`);
    totalIssues++;
  }
  
  console.log(''); // Empty line for readability
});

// Test API endpoints to ensure server is working
console.log('🌐 TESTING API ENDPOINTS:\n');

async function testAPIs() {
  const apiTests = [
    { name: 'Settings API', url: 'http://localhost:3000/api/settings?business=true' },
    { name: 'Gold Rates API', url: 'http://localhost:3000/api/gold-rates' }
  ];

  for (const test of apiTests) {
    try {
      const response = await fetch(test.url);
      if (response.ok) {
        const data = await response.json();
        if (data.success !== false) {
          console.log(`✅ ${test.name}: Working`);
        } else {
          console.log(`❌ ${test.name}: API error - ${data.error}`);
        }
      } else {
        console.log(`❌ ${test.name}: HTTP ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
    }
  }
}

// Summary
console.log('\n📊 VERIFICATION SUMMARY');
console.log('======================');

const totalComponents = componentsToCheck.length;
const healthyComponents = fixedIssues;
const problematicComponents = totalIssues;

console.log(`\n📋 Components checked: ${totalComponents}`);
console.log(`✅ Healthy components: ${healthyComponents}`);
console.log(`❌ Components with issues: ${problematicComponents}`);

if (totalIssues === 0) {
  console.log('\n🎉 SUCCESS: All React hook imports are correct!');
  console.log('✅ No useEffect or other hook import issues found');
  console.log('✅ All components should work without React errors');
  console.log('✅ The "useEffect is not defined" error has been fixed');
} else {
  console.log('\n⚠️  ISSUES FOUND: Some components need attention');
  console.log('❌ React hook import issues detected');
  console.log('❌ May cause "useEffect is not defined" errors');
  console.log('\n🔧 RECOMMENDED ACTIONS:');
  console.log('1. Add missing React hook imports to the identified files');
  console.log('2. Ensure all used hooks are imported from React');
  console.log('3. Test the application in the browser');
  console.log('4. Check browser console for any remaining errors');
}

console.log('\n🚀 NEXT STEPS:');
console.log('1. Open http://localhost:3000 in your browser');
console.log('2. Check browser console for any React errors');
console.log('3. Navigate through different pages');
console.log('4. Test the billing system and inventory management');
console.log('5. Verify settings updates work correctly');

// Test APIs
testAPIs().then(() => {
  console.log('\n✅ VERIFICATION COMPLETE');
  
  if (totalIssues === 0) {
    console.log('🎉 All systems are working correctly!');
    console.log('🚀 The application is ready for use');
  } else {
    console.log('⚠️  Please fix the identified issues before proceeding');
  }
});
