const mysql = require('mysql2/promise');
require('dotenv').config();

async function addSoldTrackingFields() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jewellery_wholesale_software'
    });

    console.log('Connected to database');

    // Check current inventory table structure
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME || 'jewellery_wholesale_software']);

    console.log('Current inventory table columns:');
    columns.forEach(col => {
      console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE}`);
    });

    // Check if we need to add sold tracking fields for different purities
    const soldFields = [
      'sold_value_24k',
      'sold_value_22k', 
      'sold_value_18k'
    ];

    const existingColumns = columns.map(col => col.COLUMN_NAME);
    
    for (const field of soldFields) {
      if (!existingColumns.includes(field)) {
        console.log(`\nAdding ${field} column...`);
        await connection.execute(`
          ALTER TABLE inventory 
          ADD COLUMN ${field} DECIMAL(10,3) DEFAULT 0.000 
          AFTER sold_value_without_stone
        `);
        console.log(`✅ Added ${field} column`);
      } else {
        console.log(`✅ ${field} column already exists`);
      }
    }

    // Update the existing record to use the new structure
    console.log('\n=== UPDATING RECORD WITH PROPER SOLD VALUES ===');
    
    // Find the chain record
    const [records] = await connection.execute(`
      SELECT i.*, s.name as supplier_name
      FROM inventory i
      LEFT JOIN suppliers s ON i.supplier_id = s.id
      WHERE i.product_name LIKE '%chain%' 
      AND s.name LIKE '%emerald%'
    `);

    if (records.length > 0) {
      const record = records[0];
      
      // Update with proper sold values based on physical record
      await connection.execute(`
        UPDATE inventory 
        SET 
          sold_value_24k = ?,
          sold_value_22k = ?,
          sold_value_18k = 0,
          balance_weight_24k = procured_in_24k - sold_value_24k,
          balance_weight_22k = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [
        9.754,  // Sold in 24K from physical record
        10.160, // Sold in 22K from physical record  
        110.260, // Balance in 22K from physical record
        record.id
      ]);

      console.log('✅ Updated record with proper sold value tracking');
      
      // Verify the update
      const [updatedRecord] = await connection.execute(`
        SELECT * FROM inventory WHERE id = ?
      `, [record.id]);

      const updated = updatedRecord[0];
      console.log('\nUpdated values:');
      console.log(`- Procured in 24K: ${updated.procured_in_24k}g`);
      console.log(`- Sold in 24K: ${updated.sold_value_24k}g`);
      console.log(`- Sold in 22K: ${updated.sold_value_22k}g`);
      console.log(`- Balance 24K: ${updated.balance_weight_24k}g`);
      console.log(`- Balance 22K: ${updated.balance_weight_22k}g`);
      
      // Calculate what balance should be
      const expectedBalance24k = updated.procured_in_24k - updated.sold_value_24k;
      console.log(`\nCalculation check:`);
      console.log(`Expected Balance 24K: ${expectedBalance24k.toFixed(3)}g (${updated.procured_in_24k} - ${updated.sold_value_24k})`);
      console.log(`Actual Balance 24K: ${updated.balance_weight_24k}g`);
    }

    console.log('\n🎉 Database structure updated for proper sold value tracking!');

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
addSoldTrackingFields();
